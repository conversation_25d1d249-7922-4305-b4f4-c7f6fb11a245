'use client'

import { useEffect, useState } from 'react'
import Lenis from '@studio-freight/lenis'
import Header from "@/components/layout/Header"
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Zap, Users, Server } from 'lucide-react'
import Link from 'next/link'
import { CollapsibleJobListing } from '@/components/careers/CollapsibleJobListing'

export default function CareersPage() {

  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    })

    function raf(time: number) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    document.documentElement.classList.add('lenis', 'lenis-smooth')

    return () => {
      lenis.destroy()
      document.documentElement.classList.remove('lenis', 'lenis-smooth')
    }
  }, [])

  const jobPositions = [
    {
      title: "Founding Engineer (Fullstack)",
      description: "Join our core team to build the foundation of our AI-powered insurance platform. You'll work across the entire stack and help shape our technical direction.",
      requirements: [
        "5+ years of experience in fullstack development",
        "Proficiency in TypeScript, React, and Node.js",
        "Experience with cloud infrastructure and CI/CD pipelines",
        "Strong problem-solving skills and ability to work in a fast-paced environment"
      ],
      responsibilities: [
        "Design and implement core platform features",
        "Collaborate with AI engineers to integrate machine learning models",
        "Establish best practices and architectural patterns",
        "Mentor junior engineers and contribute to technical decisions"
      ],

      location: "Remote (US/Europe)",
      type: "Full-time"
    },
    {
      title: "Data Engineer (Python)",
      description: "Build and maintain our data infrastructure to support our AI models and analytics. You'll work with large insurance datasets and create pipelines for data processing.",
      requirements: [
        "3+ years of experience in data engineering",
        "Strong Python programming skills",
        "Experience with data processing frameworks (Spark, Airflow)",
        "Knowledge of SQL and NoSQL databases"
      ],
      responsibilities: [
        "Design and implement data pipelines",
        "Optimize data storage and retrieval systems",
        "Ensure data quality and reliability",
        "Collaborate with data scientists and ML engineers"
      ],

      location: "Remote (US/Europe)",
      type: "Full-time"
    },
    {
      title: "AI Engineer",
      description: "Develop cutting-edge AI solutions for the insurance industry. You'll work on NLP, computer vision, and predictive models to transform insurance operations.",
      requirements: [
        "3+ years of experience in machine learning or AI",
        "Strong Python skills and experience with ML frameworks",
        "Background in NLP, computer vision, or predictive modeling",
        "Experience deploying ML models to production"
      ],
      responsibilities: [
        "Design and implement AI models for insurance use cases",
        "Optimize model performance and accuracy",
        "Collaborate with product and engineering teams",
        "Stay current with the latest AI research and techniques"
      ],

      location: "Remote (US/Europe)",
      type: "Full-time"
    },
    {
      title: "DevOps Engineer (GCP Stack)",
      description: "Build and maintain our cloud infrastructure on GCP. You'll ensure our platform is scalable, secure, and reliable.",
      requirements: [
        "3+ years of experience in DevOps or SRE roles",
        "Strong experience with GCP services",
        "Knowledge of Kubernetes, Terraform, and CI/CD pipelines",
        "Experience with monitoring and observability tools"
      ],
      responsibilities: [
        "Design and implement cloud infrastructure",
        "Automate deployment and scaling processes",
        "Implement security best practices",
        "Monitor system performance and reliability"
      ],

      location: "Remote (US/Europe)",
      type: "Full-time"
    },
    {
      title: "Marketing and Sales Manager",
      description: "Lead our go-to-market strategy and build relationships with insurance companies. You'll help us grow our customer base and establish Rekover as a leader in the industry.",
      requirements: [
        "5+ years of experience in B2B marketing or sales",
        "Experience in the insurance or fintech industry",
        "Strong communication and presentation skills",
        "Ability to build and execute marketing campaigns"
      ],
      responsibilities: [
        "Develop and execute marketing and sales strategies",
        "Build relationships with insurance companies",
        "Create compelling content and presentations",
        "Track and analyze marketing metrics"
      ],

      location: "Remote (US/Europe)",
      type: "Full-time"
    }
  ];

  return (
    <div className="bg-[#141b2b] min-h-screen flex flex-col">
      <Header />

      {/* Hero Section */}
      <section className="pt-40 pb-20 bg-[#0f172a]">
        <div className="max-w-6xl mx-auto px-6 sm:px-8 text-center">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8"
          >
            Join Our Mission
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            Help us transform the insurance industry with cutting-edge AI technology and innovative solutions.
          </motion.p>
        </div>
      </section>

      {/* Why Join Us Section */}
      <section className="py-20 bg-[#141b2b]">
        <div className="max-w-6xl mx-auto px-6 sm:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Why Join Rekover?</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We're building the future of insurance technology with a team passionate about innovation, excellence, and positive industry impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-[#1a2436] p-8 rounded-xl border border-white/10 hover:border-[#0be5a9]/50 transition-all">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center">
                  <Zap className="w-8 h-8 text-[#0be5a9]" />
                </div>
                <h3 className="text-2xl font-bold text-white">Cutting-Edge Tech</h3>
              </div>
              <p className="text-gray-300">Work with the latest AI technologies and help solve complex problems in the insurance industry.</p>
            </div>

            <div className="bg-[#1a2436] p-8 rounded-xl border border-white/10 hover:border-[#0be5a9]/50 transition-all">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center">
                  <Users className="w-8 h-8 text-[#0be5a9]" />
                </div>
                <h3 className="text-2xl font-bold text-white">Amazing Team</h3>
              </div>
              <p className="text-gray-300">Join a diverse team of talented engineers, data scientists, and industry experts passionate about innovation.</p>
            </div>

            <div className="bg-[#1a2436] p-8 rounded-xl border border-white/10 hover:border-[#0be5a9]/50 transition-all">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center">
                  <Server className="w-8 h-8 text-[#0be5a9]" />
                </div>
                <h3 className="text-2xl font-bold text-white">Make an Impact</h3>
              </div>
              <p className="text-gray-300">Help transform an entire industry and build products that make a real difference for insurance companies and their customers.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions Section */}
      <section className="py-20 bg-[#0f172a]">
        <div className="max-w-6xl mx-auto px-6 sm:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Open Positions</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Join our team and help us build the future of insurance technology.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8">
            {jobPositions.map((job, index) => (
              <CollapsibleJobListing key={index} job={job} />
            ))}
          </div>
        </div>
      </section>

      {/* Application Process Section */}
      <section className="py-20 bg-[#141b2b]">
        <div className="max-w-6xl mx-auto px-6 sm:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Apply?</h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
            We're looking for passionate individuals who want to make a difference in the insurance industry.
          </p>
          <Button size="lg" className="bg-[#f69323] hover:bg-[#f69323]/80 text-white font-medium">
            <Link href="/contact">Contact Us</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
