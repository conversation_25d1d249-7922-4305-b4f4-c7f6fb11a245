import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  const body = await request.json()
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Simulate random relationships
  const entities = body.entities.map((e: any) => e.value)
  const relationships = []
  
  for (let i = 0; i < entities.length; i++) {
    for (let j = i + 1; j < entities.length; j++) {
      if (Math.random() > 0.5) {
        relationships.push({
          source: entities[i],
          target: entities[j],
        })
      }
    }
  }

  return NextResponse.json({ relationships })
}

