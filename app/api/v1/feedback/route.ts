import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

// Create a transporter object using Postmark
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST || 'smtp.postmarkapp.com',
  port: Number(process.env.EMAIL_SERVER_PORT) || 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_SERVER_USER || '', // Postmark uses the Server API Token for both username and password
    pass: process.env.EMAIL_SERVER_PASSWORD || '',
  },
})

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.tool_name || !data.feedback_type || !data.feedback_text) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Format the email content
    const emailSubject = `Tool Feedback: ${data.tool_name} - ${formatFeedbackType(data.feedback_type)}`

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Feedback Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          h2 { color: #2563eb; }
          .feedback-item { margin-bottom: 10px; }
          .feedback-label { font-weight: bold; }
          .feedback-text { background-color: #f9fafb; padding: 15px; border-radius: 5px; margin-top: 5px; }
        </style>
      </head>
      <body>
        <h2>New Feedback Submission</h2>
        <div class="feedback-item">
          <span class="feedback-label">Tool:</span> ${data.tool_name}
        </div>
        <div class="feedback-item">
          <span class="feedback-label">Type:</span> ${formatFeedbackType(data.feedback_type)}
        </div>
        <div class="feedback-item">
          <span class="feedback-label">User:</span> ${data.user_name} (${data.user_email})
        </div>
        <div class="feedback-item">
          <span class="feedback-label">User ID:</span> ${data.user_id}
        </div>
        <div class="feedback-item">
          <span class="feedback-label">Time:</span> ${new Date(data.timestamp).toLocaleString()}
        </div>
        <div class="feedback-item">
          <span class="feedback-label">Feedback:</span>
          <div class="feedback-text">${data.feedback_text.replace(/\\n/g, '<br>')}</div>
        </div>
      </body>
      </html>
    `

    // Send the email
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: process.env.FEEDBACK_EMAIL || '<EMAIL>',
      subject: emailSubject,
      html: emailContent,
      // Postmark specific headers
      headers: {
        'X-PM-Tag': 'tool-feedback',  // For categorizing emails in Postmark
      },
      // Optional: Message stream - uncomment if you have multiple streams configured in Postmark
      // messageStream: 'outbound',
    }

    await transporter.sendMail(mailOptions)

    return NextResponse.json({
      success: true,
      message: 'Feedback submitted successfully',
    })
  } catch (error) {
    console.error('Error submitting feedback:', error)
    return NextResponse.json(
      { error: 'Failed to submit feedback' },
      { status: 500 }
    )
  }
}

// Helper function to format feedback type
function formatFeedbackType(type: string): string {
  return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}
