import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Extract parameters from the request
    const target_latitude = searchParams.get('target_latitude')
    const target_longitude = searchParams.get('target_longitude')
    const window = searchParams.get('window')
    const vs30 = searchParams.get('Vs30')

    // Validate required parameters
    if (!target_latitude || !target_longitude || !window) {
      return NextResponse.json(
        { error: 'Missing required parameters: target_latitude, target_longitude, window' },
        { status: 400 }
      )
    }

    // Validate parameter ranges
    const lat = parseFloat(target_latitude)
    const lng = parseFloat(target_longitude)
    const windowNum = parseInt(window)

    if (isNaN(lat) || lat < -90 || lat > 90) {
      return NextResponse.json(
        { error: 'Invalid latitude: must be between -90 and 90' },
        { status: 400 }
      )
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      return NextResponse.json(
        { error: 'Invalid longitude: must be between -180 and 180' },
        { status: 400 }
      )
    }

    if (windowNum !== 1 && windowNum !== 3) {
      return NextResponse.json(
        { error: 'Invalid window: must be 1 or 3' },
        { status: 400 }
      )
    }

    // Build the external API URL
    const params = new URLSearchParams({
      target_latitude: target_latitude,
      target_longitude: target_longitude,
      window: window
    })

    if (vs30) {
      params.append('Vs30', vs30)
    }

    const externalApiUrl = `https://pga-curve-715610578807.us-central1.run.app/?${params}`

    console.log('Proxying request to:', externalApiUrl)

    // Make the request to the external API
    const response = await fetch(externalApiUrl, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
        'User-Agent': 'Rekover-Earthquake-Prediction/1.0',
      },
    })

    if (!response.ok) {
      console.error('External API error:', response.status, response.statusText)
      return NextResponse.json(
        { error: `External API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      )
    }

    // Get the response as text first
    const responseText = await response.text()

    // Try to parse as JSON, if that fails, try to convert Python dict format to JSON
    let data
    try {
      data = JSON.parse(responseText)
    } catch (parseError) {
      console.log('Standard JSON parse failed, trying Python dict format conversion...')

      try {
        // Convert Python dict format to JSON format
        // Replace unquoted numeric keys with quoted keys
        let jsonString = responseText
          .replace(/(\d+(?:\.\d+)?)\s*:/g, (match, p1) => `"${p1}":`) // Quote numeric keys
          .replace(/'/g, '"') // Replace single quotes with double quotes

        // Handle the case where the response might be truncated
        if (!jsonString.trim().endsWith('}')) {
          console.log('Response appears to be truncated, attempting to close it...')
          jsonString = jsonString.trim() + '}'
        }

        console.log('Conversion attempt - first 200 chars:', jsonString.substring(0, 200))
        data = JSON.parse(jsonString)
        console.log('Successfully converted Python dict format to JSON')
      } catch (conversionError) {
        console.error('Python dict conversion error:', conversionError)
        console.error('Response text (first 500 chars):', responseText.substring(0, 500))
        console.error('Response text (last 100 chars):', responseText.substring(responseText.length - 100))

        return NextResponse.json(
          {
            error: 'Unable to parse response from external API',
            details: 'The API returned data in an unexpected format'
          },
          { status: 502 }
        )
      }
    }

    // Return the data with proper CORS headers
    return NextResponse.json(data, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })

  } catch (error) {
    console.error('Proxy API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
