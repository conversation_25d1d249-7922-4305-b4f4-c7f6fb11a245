import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

// Create a transporter object using Postmark
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST || 'smtp.postmarkapp.com',
  port: Number(process.env.EMAIL_SERVER_PORT) || 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_SERVER_USER || '', // Postmark uses the Server API Token for both username and password
    pass: process.env.EMAIL_SERVER_PASSWORD || '',
  },
})

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.email || !data.subject || !data.message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Format the email content
    const emailSubject = `Contact Form: ${data.subject}`

    const emailContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Contact Form Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          h2 { color: #2563eb; }
          .contact-item { margin-bottom: 10px; }
          .contact-label { font-weight: bold; }
          .contact-text { background-color: #f9fafb; padding: 15px; border-radius: 5px; margin-top: 5px; }
        </style>
      </head>
      <body>
        <h2>New Contact Form Submission</h2>
        <div class="contact-item">
          <span class="contact-label">Name:</span> ${data.name}
        </div>
        <div class="contact-item">
          <span class="contact-label">Email:</span> ${data.email}
        </div>
        <div class="contact-item">
          <span class="contact-label">Subject:</span> ${data.subject}
        </div>
        <div class="contact-item">
          <span class="contact-label">Message:</span>
          <div class="contact-text">${data.message.replace(/\n/g, '<br>')}</div>
        </div>
        <div class="contact-item">
          <span class="contact-label">Submitted at:</span> ${new Date().toLocaleString()}
        </div>
      </body>
      </html>
    `

    // Send the email
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: process.env.CONTACT_EMAIL || '<EMAIL>',
      subject: emailSubject,
      html: emailContent,
      replyTo: data.email,
      // Postmark specific headers
      headers: {
        'X-PM-Tag': 'contact-form',  // For categorizing emails in Postmark
      },
    }

    await transporter.sendMail(mailOptions)

    return NextResponse.json({
      success: true,
      message: 'Message sent successfully',
    })
  } catch (error) {
    console.error('Error sending contact email:', error)
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    )
  }
}
