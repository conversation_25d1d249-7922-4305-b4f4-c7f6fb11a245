import type {  NextAuthOptions, Session } from "next-auth";
import type { JWT } from "next-auth/jwt";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";

export const options: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: {
          label: "Username",
          type: "text",
          required: true,
          placeholder: "username",
        },
        password: {
          label: "Password",
          type: "password",
          required: true,
          placeholder: "password",
        },
      },
      async authorize(credentials) {
        if (!credentials) {
          throw new Error("No credentials provided");
        }

        try {
          const { username, password } = credentials;
          
          // Convert credentials to form data format
          const formData = new URLSearchParams();
          formData.append('username', username);
          formData.append('password', password);

          const response = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/login`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/x-www-form-urlencoded",
              },
              body: formData,
            }
          );

          const data = await response.json();

          if (response.ok && data) {
            return {
              id: data.id,
              name: data.username,
              email: data.email,
              image: data.image,
              accessToken: data.access_token,
              role: data.role,
            };
          }

          return null;
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      },
    }),
  ],
  pages: {
    signIn: '/login',
    error: '/login',
  },
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/verify-google`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: user.email,
                name: user.name,
                google_id: user.id,

              }),
            }
          );

          if (!response.ok) {
            return false; // This will redirect to error page if email doesn't exist in DB
          }

          const data = await response.json();
          
          // Add the access token and role to the user object
          user.accessToken = data.access_token;
          user.id = data.id;
          user.role = data.role;
          user.image = data.image;
          
          return true;
        } catch (error) {
          console.error('Google verification error:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      if (user && account) {
        token.accessToken = user.accessToken;
        token.id = user.id;
        token.role = user.role;
        token.image = user.image;
      }
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (session.user) {
        (session.user as any).id = token.id;
        (session.user as any).accessToken = token.accessToken;
        (session.user as any).role = token.role;
        (session.user as any).image = token.image;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Always allow redirects to /dashboard
      if (url.startsWith("/dashboard")) {
        return `${baseUrl}${url}`
      }
      // Allows relative callback URLs
      if (url.startsWith("/")) {
        return `${baseUrl}/dashboard`
      }
      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) {
        return url
      }
      return baseUrl
    },
  },
  session: {
    strategy: 'jwt',
    maxAge:   4 * 60 * 60, 
  },  
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
}
