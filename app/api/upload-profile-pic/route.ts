import { NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    // Get the session token using cookies
    const cookieStore = cookies();
    
    const token = await getToken({
      req: {
        cookies: Object.fromEntries(
          cookieStore.getAll().map(cookie => [cookie.name, cookie.value])
        ),
        headers: request.headers,
      } as any,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Forward the request to the FastAPI backend
    const formData = await request.formData();
    
    // Make sure user_id is set in the form data
    if (!formData.has('user_id')) {
      formData.append('user_id', token.id as string);
    }
    
    console.log('Sending request to FastAPI with user_id:', formData.get('user_id'));
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/upload-profile-pic`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${token.accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('FastAPI error response:', errorText);
      throw new Error(errorText);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in upload-profile-pic route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload image' },
      { status: 500 }
    );
  }
}
