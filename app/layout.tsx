import './globals.css'
import { Inter } from 'next/font/google'
import { Toaster } from "@/components/ui/toaster"
import { SessionProvider } from '@/components/providers/SessionProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Rekover - AI Consultancy for Insurance Industry',
  description: 'Custom AI solutions and marketplace of features for insurance companies, built by experts in AI and insurance',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.className}>
      <body>
        <SessionProvider>
          <main className="flex-1 overflow-auto">
            {children}
          </main>
          <Toaster />
        </SessionProvider>
      </body>
    </html>
  )
}
