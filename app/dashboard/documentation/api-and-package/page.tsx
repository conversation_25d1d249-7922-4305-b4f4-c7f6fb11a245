'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Brain, BarChart, Car, Home, ImageIcon, CloudRain, UserCheck, Mic, Building2, Satellite, FileText, BotIcon as Robot } from 'lucide-react'

export default function APIAndPackageDocumentation() {
  const [activeTab, setActiveTab] = useState("api")

  const functionalities = [
    { 
      name: "ML Models", 
      icon: Brain, 
      description: "Our suite of machine learning models designed specifically for the insurance industry.",
      items: [
        {
          name: "Claims Segmentation",
          description: "Automatically categorize and prioritize claims based on complexity, urgency, and potential fraud risk."
        },
        {
          name: "Total Loss",
          description: "Predict the likelihood of a total loss claim, enabling faster decision-making and more accurate reserving."
        },
        {
          name: "CLV (Customer Lifetime Value)",
          description: "Forecast the long-term value of customers to inform pricing and retention strategies."
        },
        {
          name: "Churn",
          description: "Identify customers at risk of leaving, allowing for proactive retention measures."
        },
        {
          name: "Custom Models",
          description: "Develop and deploy bespoke ML models tailored to your specific business needs and data."
        }
      ]
    },
    { 
      name: "Fraud Detection", 
      icon: BarChart, 
      description: "Advanced tools to identify and prevent fraudulent activities in insurance claims.",
      items: [
        {
          name: "Custom Fraud Detection Tools",
          description: "Implement specialized fraud detection algorithms adapted to your unique risk profile and claim patterns."
        }
      ]
    },
    { 
      name: "Market Analysis", 
      icon: BarChart, 
      description: "Tools for understanding market trends and competitive positioning.",
      items: [
        {
          name: "Market Evaluation",
          description: "Analyze market data to inform pricing strategies, product development, and competitive positioning."
        }
      ]
    },
    { 
      name: "Financial Checks", 
      icon: BarChart, 
      description: "Tools for assessing financial risks associated with policyholders and partners.",
      items: [
        {
          name: "Check Debtor",
          description: "Evaluate the financial status and credit risk of policyholders or claimants."
        },
        {
          name: "Check Insurer",
          description: "Assess the financial stability and reliability of partner insurers or reinsurers."
        }
      ]
    },
    { 
      name: "Claims Processing", 
      icon: FileText, 
      description: "Streamline and automate various aspects of the claims handling process.",
      items: [
        {
          name: "Claim Acceptance",
          description: "Automate the initial assessment and acceptance of claims based on policy terms and risk factors."
        },
        {
          name: "Network Analysis",
          description: "Identify connections between claims, claimants, and other entities to detect patterns or potential fraud rings."
        }
      ]
    },
    { 
      name: "Vehicle Analysis", 
      icon: Car, 
      description: "Tools specifically designed for assessing and valuing vehicles in auto insurance.",
      items: [
        {
          name: "Car Appraisal",
          description: "Accurately estimate the value of vehicles based on make, model, condition, and market factors."
        },
        {
          name: "Vehicle Specs",
          description: "Access comprehensive vehicle specifications to inform underwriting and claims processes."
        }
      ]
    },
    { 
      name: "Property Analysis", 
      icon: Home, 
      description: "Tools for evaluating and assessing properties in property insurance.",
      items: [
        {
          name: "Home Appraisal",
          description: "Estimate property values and assess risk factors for residential properties."
        }
      ]
    },
    { 
      name: "Data Analysis", 
      icon: ImageIcon, 
      description: "Advanced tools for extracting insights from various data sources.",
      items: [
        {
          name: "Image Analyser",
          description: "Automatically analyze images from claims to detect damage, estimate repair costs, and flag potential fraud."
        },
        {
          name: "OSINT (Open Source Intelligence)",
          description: "Gather and analyze publicly available information to support underwriting and claims investigations."
        }
      ]
    },
    { 
      name: "Environmental Data", 
      icon: CloudRain, 
      description: "Access to environmental and weather data for risk assessment and claims validation.",
      items: [
        {
          name: "Weather",
          description: "Retrieve historical and real-time weather data to validate claims and assess environmental risks."
        }
      ]
    },
    { 
      name: "Identity Verification", 
      icon: UserCheck, 
      description: "Tools for verifying the identity of policyholders and claimants.",
      items: [
        {
          name: "Identity Check",
          description: "Verify the identity of individuals to prevent fraud and ensure compliance with KYC regulations."
        }
      ]
    },
    { 
      name: "Speech Processing", 
      icon: Mic, 
      description: "Tools for processing and analyzing spoken language in insurance contexts.",
      items: [
        {
          name: "FNOL Speech-to-Text",
          description: "Convert spoken First Notice of Loss (FNOL) reports into text for easier processing and analysis."
        }
      ]
    },
    { 
      name: "Business Intelligence", 
      icon: Building2, 
      description: "Tools for gathering and analyzing business information for informed decision-making.",
      items: [
        {
          name: "Commercial Due Diligence",
          description: "Conduct thorough assessments of businesses for underwriting or investment purposes."
        }
      ]
    },
    { 
      name: "Audit Tools", 
      icon: FileText, 
      description: "Tools for reviewing and validating insurance processes and decisions.",
      items: [
        {
          name: "Appraisal Audit",
          description: "Automatically review and validate property or vehicle appraisals for accuracy and consistency."
        }
      ]
    },
    { 
      name: "Geospatial Analysis", 
      icon: Satellite, 
      description: "Tools for analyzing location-based data and imagery.",
      items: [
        {
          name: "Satellite Imagery",
          description: "Utilize satellite imagery for property assessment, disaster response, and risk evaluation."
        }
      ]
    },
    { 
      name: "Document Processing", 
      icon: FileText, 
      description: "Tools for extracting and processing information from insurance documents.",
      items: [
        {
          name: "OCR Workflows",
          description: "Implement Optical Character Recognition workflows to digitize and extract data from paper documents and images."
        }
      ]
    },
    { 
      name: "AI Agents", 
      icon: Robot, 
      description: "Advanced AI-powered assistants for various insurance tasks.",
      items: [
        {
          name: "AI Agents",
          description: "Deploy intelligent AI agents to handle customer inquiries, assist with claims processing, and support underwriting decisions."
        }
      ]
    }
  ];

  return (
    <div className="container mx-auto py-10">
      <section className="mb-12">
        <h1 className="text-4xl font-bold mb-6">API and Python Package Documentation</h1>
        <p className="text-xl mb-8">
          Welcome to the Rekover AI documentation. Our platform offers a wide range of AI-powered tools and functionalities designed to revolutionize insurance operations. Whether you're integrating our services via API or using our Python package, this guide will help you harness the full potential of Rekover AI.
        </p>
        <p className="text-lg mb-8">
          Our suite of tools covers everything from claims processing and fraud detection to advanced market analysis and AI-driven assistants. Each functionality is designed to streamline your workflow, enhance decision-making, and improve overall efficiency in the insurance sector.
        </p>
        <p className="text-lg">
          Choose between our RESTful API for seamless integration into your existing systems, or our Python package for easy implementation in your Python applications. Both options provide full access to our comprehensive set of features and AI models.
        </p>
      </section>
      <p className="text-xl mb-8">
        Learn how to use the Rekover AI API and Python package to access our powerful insurance technology tools.
      </p>

      <Tabs defaultValue="api" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="api">API</TabsTrigger>
          <TabsTrigger value="python">Python Package</TabsTrigger>
        </TabsList>
        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>
                Use our RESTful API to integrate Rekover AI functionalities into your applications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold mb-4">Authentication</h3>
              <p className="mb-4">
                To use the Rekover AI API, you need to include your API key in the header of each request:
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                <code>
                  {`Authorization: Bearer YOUR_API_KEY`}
                </code>
              </pre>

              <h3 className="text-lg font-semibold mt-8 mb-4">Base URL</h3>
              <p className="mb-4">
                All API requests should be made to:
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                <code>
                  {`https://api.rekover.ai/v1/`}
                </code>
              </pre>

              <h3 className="text-lg font-semibold mt-8 mb-4">Endpoints</h3>
              <Accordion type="single" collapsible className="w-full">
                {functionalities.map((functionality, index) => (
                  <AccordionItem value={`item-${index}`} key={index}>
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <functionality.icon className="mr-2 h-5 w-5" />
                        <span>{functionality.name}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-4 text-gray-600">{functionality.description}</p>
                      <ul className="list-disc pl-6">
                        {functionality.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="mb-4">
                            <h4 className="font-semibold">{item.name}</h4>
                            <p className="mb-2 text-gray-600">{item.description}</p>
                            <p className="mb-2">Endpoint: <code className="bg-gray-100 p-1 rounded">/api/v1/{item.name.toLowerCase().replace(/ /g, '-')}</code></p>
                            <p className="mb-2">Method: POST</p>
                            <p>Example request:</p>
                            <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto mt-2">
                              <code>
                                {`curl -X POST \\
  https://api.rekover.ai/v1/${item.name.toLowerCase().replace(/ /g, '-')} \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"param1": "value1", "param2": "value2"}'`}
                              </code>
                            </pre>
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="python">
          <Card>
            <CardHeader>
              <CardTitle>Python Package Documentation</CardTitle>
              <CardDescription>
                Use our Python package to easily integrate Rekover AI functionalities into your Python applications.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <h3 className="text-lg font-semibold mb-4">Installation</h3>
              <p className="mb-4">
                Install the Rekover AI Python package using pip:
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                <code>
                  {`pip install rekover-ai`}
                </code>
              </pre>

              <h3 className="text-lg font-semibold mt-8 mb-4">Usage</h3>
              <p className="mb-4">
                First, import the package and initialize the client:
              </p>
              <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                <code>
                  {`from rekover_ai import RekoverAI

client = RekoverAI(api_key="YOUR_API_KEY")`}
                </code>
              </pre>

              <h3 className="text-lg font-semibold mt-8 mb-4">Available Methods</h3>
              <Accordion type="single" collapsible className="w-full">
                {functionalities.map((functionality, index) => (
                  <AccordionItem value={`item-${index}`} key={index}>
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <functionality.icon className="mr-2 h-5 w-5" />
                        <span>{functionality.name}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-4 text-gray-600">{functionality.description}</p>
                      <ul className="list-disc pl-6">
                        {functionality.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="mb-4">
                            <h4 className="font-semibold">{item.name}</h4>
                            <p className="mb-2 text-gray-600">{item.description}</p>
                            <p className="mb-2">Method: <code className="bg-gray-100 p-1 rounded">client.{item.name.toLowerCase().replace(/ /g, '_')}()</code></p>
                            <p>Example usage:</p>
                            <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto mt-2">
                              <code>
                                {`result = client.${item.name.toLowerCase().replace(/ /g, '_')}(param1="value1", param2="value2")
print(result)`}
                              </code>
                            </pre>
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-12">
        <h2 className="text-2xl font-bold mb-4">Need Help?</h2>
        <p className="mb-4">
          If you have any questions or need assistance, please don't hesitate to contact our support team.
        </p>
        <Button>Contact Support</Button>
      </div>
    </div>
  )
}

