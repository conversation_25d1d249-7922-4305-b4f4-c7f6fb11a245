"use client"

import { useState, useEffect } from 'react'
import { useParams } from "next/navigation";
import axios from 'axios'
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, Edit } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"

import { Input } from "@/components/ui/input"
import { LuCheck, LuX } from "react-icons/lu";

interface Claim {
  claim_no: string;
  claim_amount: number;
  claim_type: string;
  claim_status: string;
  claim_handler: string;
  claim_date: string;
  report_date: string;
  description: string;
  notes: string;
  resolution_timeline_estimate: string;
  payments_made: string;
  claim_location: string;
  internal_tags: string;
  police_report: boolean;
  witness_report: boolean;
  medical_report: boolean;
  appraisal_report: boolean;
  id: string;
}




export default function ClaimDetails() {

  const params = useParams();
  const claimId = params?.id as string;

const [claim, setClaim] = useState<Claim | null>(null)
const [isLoading, setIsLoading] = useState(true)
const [error, setError] = useState<string | null>(null)
const [isEditing, setIsEditing] = useState(false);
const [editedClaim, setEditedClaim] = useState<Claim | null>(null);
// const [policyQuestion, setPolicyQuestion] = useState("")
// const [policyAnswer, setPolicyAnswer] = useState<string | null>(null)
// const [chatMessage, setChatMessage] = useState("")
// const [chatHistory, setChatHistory] = useState<{ role: "user" | "assistant", content: string }[]>([])

useEffect(() => {
  const fetchClaimDetails = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.get<Claim>(
`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claims/${claimId}`
      );
      console.log(response.data);
      setClaim(response.data);
    } catch (err) {
      setError("Failed to fetch claim details. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  fetchClaimDetails();
}, [claimId]);



if (isLoading) {
    return <p>Loading claim details...</p>;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!claim) {
    return (
      <Alert>
        <AlertTitle>Not Found</AlertTitle>
        <AlertDescription>The requested claim could not be found.</AlertDescription>
      </Alert>
    );
  }



  const handleStatusChange = async (newStatus: "Open" | "Closed" | "Pending") => {
    if (!claim) return;
  
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claims/${claimId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...claim, claim_status: newStatus }),
      });
  
      if (!response.ok) {
        throw new Error("Failed to update claim status");
      }
  
      toast({ title: "Status Updated", description: `Claim status updated to ${newStatus}.` });
      setClaim({ ...claim, claim_status: newStatus });
    } catch (err) {
      toast({ title: "Error", description: "Failed to update claim status.", variant: "destructive" });
    }
  };

  const handleEditClaim = () => {
    setEditedClaim({ ...claim }); // Copy existing claim data
    setIsEditing(true);
  };
  
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
  
    setEditedClaim((prev) => {
      if (!prev) return null;
  
      const updatedClaim = {
        ...prev,
        [name]: type === "number" ? parseFloat(value) || 0 : value,
      };
  
      return updatedClaim;
    });
  };
  
  const handleCheckboxChange = (name: keyof Claim) => {
    setEditedClaim((prev) => prev ? { ...prev, [name]: !prev[name] } : null);
  };

  const handleSaveChanges = async () => {
    if (!editedClaim) return;
  
  
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claims/${claimId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(editedClaim), // Ensure only editedClaim is sent
      });
  
      if (!response.ok) {
        throw new Error("Failed to update claim");
      }
  
      const updatedClaim = await response.json();
  
      setClaim(updatedClaim);
      setIsEditing(false);
  
      toast({
        title: "Claim Updated",
        description: "The claim details have been successfully updated.",
      });
    } catch (err) {
      console.error("Update Error:", err);
      toast({
        title: "Error",
        description: "Failed to update claim. Please try again later.",
        variant: "destructive",
      });
    }
  };
  
  
  

if (isLoading) {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-8 w-[200px]" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </CardContent>
    </Card>
  )
}

if (error) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  )
}

if (!claim) {
  return (
    <Alert>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Not Found</AlertTitle>
      <AlertDescription>The requested claim could not be found.</AlertDescription>
    </Alert>
  )
}

return (
  <Card>
    <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Claim {claim.claim_no}</span>
          <Badge variant="default">{claim.claim_status}</Badge>
        </CardTitle>
      </CardHeader>
    <CardContent>
      <Tabs defaultValue="details">
        <TabsList className="grid grid-cols-4 lg:grid-cols-9">
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>
        <TabsContent value="details">

<div className="grid gap-4">
  <div className="grid grid-cols-2 gap-4">
    <div>
      <h3 className="font-semibold">Claim Type</h3>
      <p>{claim.claim_type}</p>
    </div>
    <div>
      <h3 className="font-semibold">Claim Date</h3>
      <p>{claim.claim_date}</p>
    </div>
    <div>
      <h3 className="font-semibold">Amount</h3>
      <p>${claim.claim_amount}</p>
    </div>
    <div>
      <h3 className="font-semibold">Location</h3>
      <p>{claim.claim_location}</p>
    </div>
    <div>
      <h3 className="font-semibold">Resolution Timeline</h3>
      <p>{claim.resolution_timeline_estimate}</p>
    </div>
    <div>
      <h3 className="font-semibold">Payments Made</h3>
      <p>{claim.payments_made}</p>
    </div>
    <div>
      <h3 className="font-semibold">Internal Tags</h3>
      <p>{claim.internal_tags}</p>
    </div>
  </div>

  <div>
    <h3 className="font-semibold">Description</h3>
    <p>{claim.description}</p>
  </div>

  <div>
    <h3 className="font-semibold">Reports</h3>
    <div className="grid grid-cols-2 gap-2">
      <div className="flex items-center gap-2">
        <p>Police Report</p>
        {claim.police_report ? <LuCheck className="text-green-500" /> : <LuX className="text-red-500" />}
      </div>
      <div className="flex items-center gap-2">
        <p>Witness Report</p>
        {claim.witness_report ? <LuCheck className="text-green-500" /> : <LuX className="text-red-500" />}
      </div>
      <div className="flex items-center gap-2">
        <p>Medical Report</p>
        {claim.medical_report ? <LuCheck className="text-green-500" /> : <LuX className="text-red-500" />}
      </div>
      <div className="flex items-center gap-2">
        <p>Appraisal Report</p>
        {claim.appraisal_report ? <LuCheck className="text-green-500" /> : <LuX className="text-red-500" />}
      </div>
    </div>
  </div>
</div>

          </TabsContent>
        
      </Tabs>
    </CardContent>
    <CardFooter className="flex justify-between">
    <Card>
      <CardContent>
        {isEditing ? (
          <div>
  <div>
    <label>Claim Type</label>
    <Input name="claim_type" value={editedClaim?.claim_type || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Claim Amount</label>
    <Input name="claim_amount" type="number" value={editedClaim?.claim_amount || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Claim Location</label>
    <Input name="claim_location" value={editedClaim?.claim_location || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Description</label>
    <Input name="description" value={editedClaim?.description || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Resolution Timeline</label>
    <Input name="resolution_timeline_estimate" value={editedClaim?.resolution_timeline_estimate || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Payments Made</label>
    <Input name="payments_made" value={editedClaim?.payments_made || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>Internal Tags</label>
    <Input name="internal_tags" value={editedClaim?.internal_tags || ""} onChange={handleInputChange} />
  </div>

  <div>
    <label>
      <input type="checkbox" checked={editedClaim?.police_report} onChange={() => handleCheckboxChange("police_report")} />
      Police Report
    </label>
  </div>

  <div>
    <label>
      <input type="checkbox" checked={editedClaim?.witness_report} onChange={() => handleCheckboxChange("witness_report")} />
      Witness Report
    </label>
  </div>

  <div>
    <label>
      <input type="checkbox" checked={editedClaim?.medical_report} onChange={() => handleCheckboxChange("medical_report")} />
      Medical Report
    </label>
  </div>

  <div>
    <label>
      <input type="checkbox" checked={editedClaim?.appraisal_report} onChange={() => handleCheckboxChange("appraisal_report")} />
      Appraisal Report
    </label>
  </div>
</div>

        ) : (
          <></>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {isEditing ? (
          <>
            <Button variant="default" onClick={handleSaveChanges}>Save Changes</Button>
            <Button variant="outline" onClick={() => setIsEditing(false)}>Cancel</Button>
          </>
        ) : (
          <Button variant="outline" onClick={handleEditClaim}><Edit className="mr-2 h-4 w-4" />Edit Claim</Button>
        )}
      </CardFooter>
    </Card>
      <div>
        <Button
          variant="default"
          className="mr-2"
          onClick={() => handleStatusChange("Open")}
          disabled={claim.claim_status === "Open"}
        >
          Open
        </Button>
        <Button
          variant="secondary"
          className="mr-2"
          onClick={() => handleStatusChange("Pending")}
          disabled={claim.claim_status === "Pending"}
        >
          Pending
        </Button>
        <Button
          variant="destructive"
          onClick={() => handleStatusChange("Closed")}
          disabled={claim.claim_status === "Closed"}
        >
          Close
        </Button>
      </div>
    </CardFooter>
  </Card>
)
}