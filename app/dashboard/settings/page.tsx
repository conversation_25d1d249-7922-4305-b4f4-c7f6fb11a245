import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { CompanyProfileForm } from "@/components/settings/company-profile-form"
import { UserManagement } from "@/components/settings/user-management"
import { TokenManagement } from "@/components/settings/token-management"
import { Separator } from "@/components/ui/separator"
import BuyTokens from "@/components/settings/buy-tokens"
import { getServerSession } from "next-auth"
import { options } from "@/app/api/auth/[...nextauth]/options"
import type { UserRole } from '@/types/user'
import { redirect } from 'next/navigation'

export default async function SettingsPage() {
  const session = await getServerSession(options)

  if (!session ) {
    redirect('/login')
  }

  if  ((session.user?.role as UserRole) !== 'admin') {
    redirect('/dashboard')
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold">Settings</h1>
      <p className="text-muted-foreground mt-2">Manage your company profile, users, and API tokens.</p>
      
      <Separator className="my-6" />
      
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">Company Profile</TabsTrigger>
          <TabsTrigger value="users">Manage Users</TabsTrigger>
          <TabsTrigger value="tokens">API Tokens</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="space-y-6">
          <CompanyProfileForm />
        </TabsContent>
        
        <TabsContent value="users" className="space-y-6">
          <UserManagement />
        </TabsContent>

        <TabsContent value="tokens" className="space-y-6">
          <TokenManagement />
        </TabsContent>


      </Tabs>
    </div>
  )
}

