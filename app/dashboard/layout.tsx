import { Inter } from 'next/font/google'
import { Toaster } from "@/components/ui/toaster"
import dynamic from 'next/dynamic'
// import { Sidebar } from '@/components/sidebar'
import { getServerSession } from "next-auth"
import { options } from "@/app/api/auth/[...nextauth]/options"
import { redirect } from "next/navigation"

// Import the sidebar with no SSR to avoid hydration issues
const Sidebar = dynamic(() => import('@/components/sidebar1').then(mod => mod.Sidebar), { 
  ssr: false 
})

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Rekover - Insurance Management System',
  description: 'An advanced insurance management and fraud detection system',
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.className}>
      <body>
        <DashboardLayout>
          {children}
        </DashboardLayout>
        <Toaster />
      </body>
    </html>
  )
}

export async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(options)

  if (!session) {
    redirect('/login')
  }

  return (
    <div className="flex h-screen">
      <Sidebar session={session} />
      <div className="flex flex-col flex-1 overflow-hidden bg-gray-900">
        {/* <TopBar /> */}
        <main className="flex-1 p-5 overflow-auto m-5 bg-white rounded-lg border">
          {children}
        </main>
      </div>
    </div>
  )
}
