"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TotalLossForm } from "@/components/total-loss-form"
import { TotalLossResults } from "@/components/total-loss-results"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from 'lucide-react'
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"
import { CSVImportModal } from "@/components/csv-import-modal"
import { Table, TableBody, TableCell, TableHeader, TableHead, TableRow } from "@/components/ui/table"

export default function TotalLossPage() {
const [totalLossResults, setTotalLossResults] = useState<any>(null);
const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
const [bulkResults, setBulkResults] = useState<any[] | null>(null)

const handleSubmit = (totalLossInfo: any) => {
  setUploadedFiles(totalLossInfo.damageFiles);
  // Mock results
  setTotalLossResults({
    totalLossScore: Math.random(),
    estimatedRepairCost: Math.floor(Math.random() * 10000) + 5000,
    estimatedMarketValue: Math.floor(Math.random() * 20000) + 10000,
    explanation: "Based on the damage assessment and vehicle value analysis..."
  });
};

const handleBulkPredictions = async (data: any[]) => {
  // Simulate bulk predictions with processing delay
  const results = []
  for (const item of data) {
    await new Promise(resolve => setTimeout(resolve, 100)) // Simulate processing time
    results.push({
      ...item,
      totalLossScore: Math.random(),
      estimatedRepairCost: Math.floor(Math.random() * 10000) + 5000,
      estimatedMarketValue: Math.floor(Math.random() * 20000) + 10000,
    })
  }
  setBulkResults(results)
}

const modelInfo = {
  title: "Total Loss Prediction Model",
  description: "A computer vision and machine learning model that analyzes vehicle damage to predict whether a vehicle should be declared a total loss.",
  type: "regression" as const,
  accuracy: 92,
  purpose: [
    "Quickly assess vehicle damage severity from images and data",
    "Estimate repair costs based on damage analysis",
    "Compare repair costs with vehicle market value",
    "Provide consistent and objective total loss recommendations"
  ],
  methodology: [
    "Uses computer vision to analyze damage from uploaded photos/videos",
    "Considers vehicle age, make, model, and pre-accident condition",
    "Calculates repair costs using current market labor and parts rates",
    "Compares costs against real-time market value data"
  ],
  outputs: [
    "Total Loss Probability Score (0-1)",
    "Estimated Repair Cost Breakdown",
    "Current Market Value Assessment",
    "Detailed Explanation of Recommendation",
    "Confidence Score for the Prediction"
  ]
};

const apiInfo = {
  modelName: "Total Loss Prediction Model",
  endpoint: "total-loss-prediction",
  requestExample: {
    vehicle_make: "Toyota",
    vehicle_model: "Camry",
    vehicle_year: 2020,
    mileage: 45000,
    damage_description: "Front-end collision with significant damage",
    accident_severity: "severe",
    images: ["base64_encoded_image_1", "base64_encoded_image_2"]
  },
  responseExample: {
    total_loss_probability: 0.85,
    estimated_repair_cost: 15000,
    estimated_market_value: 18000,
    confidence: 0.92,
    processing_time: "1.2s",
    explanation: "Based on the severity of front-end damage and repair costs exceeding 80% of market value..."
  }
};

return (
  <div className="container mx-auto py-6">
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-3xl font-bold">Total Loss Prediction Model</h1>
      <div className="flex gap-2">
        <ModelInfoDialog {...modelInfo} />
        <APIDialog {...apiInfo} />
        <CSVImportModal onImport={handleBulkPredictions} modelName="Total Loss Prediction" />
      </div>
    </div>
    
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Information</CardTitle>
          <CardDescription>Enter the vehicle details and upload damage images/videos for total loss prediction</CardDescription>
        </CardHeader>
        <CardContent>
          <TotalLossForm onSubmit={handleSubmit} />
        </CardContent>
      </Card>
      {totalLossResults && (
        <TotalLossResults results={totalLossResults} />
      )}
    </div>
    {bulkResults && (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Bulk Prediction Results</CardTitle>
          <CardDescription>
            Showing results for {bulkResults.length} imported vehicles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Total Loss Score</TableHead>
                <TableHead>Estimated Repair Cost</TableHead>
                <TableHead>Estimated Market Value</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bulkResults.map((result, index) => (
                <TableRow key={index}>
                  <TableCell>{result.vehicle || `Vehicle ${index + 1}`}</TableCell>
                  <TableCell>{(result.totalLossScore * 100).toFixed(2)}%</TableCell>
                  <TableCell>${result.estimatedRepairCost.toLocaleString()}</TableCell>
                  <TableCell>${result.estimatedMarketValue.toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )}
    {uploadedFiles.length > 0 && (
      <Alert className="mt-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Files Uploaded</AlertTitle>
        <AlertDescription>
          {uploadedFiles.length} file(s) uploaded successfully. These will be used in the total loss assessment.
        </AlertDescription>
      </Alert>
    )}
  </div>
)
}

