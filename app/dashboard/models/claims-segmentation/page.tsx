"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ClaimInformationForm } from "@/components/claim-information-form"
import { SegmentationResults } from "@/components/segmentation-results"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"
import { CSVImportModal } from "@/components/csv-import-modal"
import { Table, TableBody, TableCell, TableHeader, TableHead, TableRow } from "@/components/ui/table"

// Define a type for the segment data
interface Segment {
  name: string;
  score: number;
}

// Mock function to simulate model prediction
const predictSegmentation = (claimInfo: any) => {
  return [
    { name: "High Value", score: Math.random() },
    { name: "Complex", score: Math.random() },
    { name: "Fraud Risk", score: Math.random() },
    { name: "Fast Track", score: Math.random() },
    { name: "Legal Review", score: Math.random() },
  ];
};

export default function ClaimsSegmentationPage() {
  const [segmentationResults, setSegmentationResults] = useState<any>(null);
  const [bulkResults, setBulkResults] = useState<any[] | null>(null);

  const handleSubmit = (claimInfo: any) => {
    const results = predictSegmentation(claimInfo);
    setSegmentationResults(results);
  };

  const handleBulkPredictions = async (data: any[]) => {
    // Simulate bulk predictions with processing delay
    const results = [];
    for (const item of data) {
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate processing time
      results.push({
        ...item,
        segments: [
          { name: "High Value", score: Math.random() },
          { name: "Complex", score: Math.random() },
          { name: "Fraud Risk", score: Math.random() },
          { name: "Fast Track", score: Math.random() },
          { name: "Legal Review", score: Math.random() },
        ]
      });
    }
    setBulkResults(results);  // Now this is inside the component
  };

  const modelInfo = {
    title: "Claims Segmentation Model",
    description: "An advanced machine learning model that automatically categorizes insurance claims into different segments for optimal processing and handling.",
    type: "classification" as const,
    accuracy: 94,
    purpose: [
      "Automatically route claims to appropriate processing channels",
      "Identify high-risk or complex claims that require special attention",
      "Optimize resource allocation and processing efficiency",
      "Ensure consistent handling of similar claims"
    ],
    methodology: [
      "Analyzes multiple claim attributes including type, amount, and documentation",
      "Uses historical claims data to identify patterns and relationships",
      "Employs ensemble learning combining multiple classification algorithms",
      "Continuously learns and adapts from new claim data and outcomes"
    ],
    outputs: [
      "High Value: Claims requiring senior adjuster review",
      "Complex: Claims with multiple coverage types or special circumstances",
      "Fraud Risk: Claims showing potential fraud indicators",
      "Fast Track: Claims eligible for expedited processing",
      "Legal Review: Claims requiring legal consultation"
    ]
  };

  const apiInfo = {
    modelName: "Claims Segmentation Model",
    endpoint: "claims-segmentation",
    requestExample: {
      claim_number: "CLM-2023-001",
      claimant_name: "John Doe",
      claim_date: "2023-12-01",
      claim_type: "auto",
      claim_amount: 15000
    },
    responseExample: {
      segments: [
        { name: "High Value", score: 0.85 },
        { name: "Complex", score: 0.45 },
        { name: "Fraud Risk", score: 0.32 },
        { name: "Fast Track", score: 0.12 },
        { name: "Legal Review", score: 0.28 }
      ],
      confidence: 0.92,
      processing_time: "0.15s"
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Claims Segmentation Model</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
          <CSVImportModal onImport={handleBulkPredictions} modelName="Claims Segmentation" />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Claim Information</CardTitle>
            <CardDescription>Enter the claim details for segmentation</CardDescription>
          </CardHeader>
          <CardContent>
            <ClaimInformationForm onSubmit={handleSubmit} />
          </CardContent>
        </Card>
        {segmentationResults && (
          <SegmentationResults results={segmentationResults} />
        )}
      </div>
      {bulkResults && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Bulk Prediction Results</CardTitle>
            <CardDescription>
              Showing results for {bulkResults.length} imported claims
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Claim Number</TableHead>
                  <TableHead>High Value</TableHead>
                  <TableHead>Complex</TableHead>
                  <TableHead>Fraud Risk</TableHead>
                  <TableHead>Fast Track</TableHead>
                  <TableHead>Legal Review</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bulkResults.map((result, index) => (
                  <TableRow key={index}>
                    <TableCell>{result.claim_number || `Claim ${index + 1}`}</TableCell>
                    {result.segments.map((segment: Segment, segmentIndex: number) => (  // Added type here
                      <TableCell key={segmentIndex}>
                        {(segment.score * 100).toFixed(2)}%
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
