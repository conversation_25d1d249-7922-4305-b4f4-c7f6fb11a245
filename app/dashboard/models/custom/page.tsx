"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { FileUploader } from "@/components/file-uploader"
import { PlusCircle } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { CustomModelCard } from "@/components/custom-model-card"

interface CustomModel {
  id: string
  name: string
  type: 'classification' | 'regression'
  pythonVersion: string
  createdAt: string
  version: string
  trainingMetrics: {
    accuracy: number
    f1Score: number
    precision: number
    recall: number
  }
  realPerformance: {
    accuracy: number
  }
}

const pythonVersions = ['3.7', '3.8', '3.9', '3.10', '3.11']

export default function CustomModelsPage() {
  const [customModels, setCustomModels] = useState<CustomModel[]>([
    {
      id: '1',
      name: 'Customer Churn Predictor',
      type: 'classification',
      pythonVersion: '3.9',
      createdAt: '2023-06-15',
      version: '1.2.0',
      trainingMetrics: {
        accuracy: 0.92,
        f1Score: 0.91,
        precision: 0.90,
        recall: 0.93
      },
      realPerformance: {
        accuracy: 0.89
      }
    },
    {
      id: '2',
      name: 'Premium Estimator',
      type: 'regression',
      pythonVersion: '3.8',
      createdAt: '2023-06-10',
      version: '2.1.1',
      trainingMetrics: {
        accuracy: 0.88,
        f1Score: 0.87,
        precision: 0.86,
        recall: 0.89
      },
      realPerformance: {
        accuracy: 0.85
      }
    },
  ])
  const [newModel, setNewModel] = useState({
    name: '',
    type: 'classification' as 'classification' | 'regression',
    inputSchema: '',
    pythonVersion: '3.9',
    requirementsTxt: null as File | null,
    modelPkl: null as File | null,
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setNewModel({ ...newModel, [e.target.name]: e.target.value })
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewModel({ ...newModel, [name]: value })
  }

  const handleFileUpload = (name: string, files: File[]) => {
    if (files.length > 0) {
      setNewModel({ ...newModel, [name]: files[0] })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const newCustomModel: CustomModel = {
      id: (customModels.length + 1).toString(),
      name: newModel.name,
      type: newModel.type,
      pythonVersion: newModel.pythonVersion,
      createdAt: new Date().toISOString().split('T')[0],
      version: '1.0.0',
      trainingMetrics: {
        accuracy: Math.random() * 0.2 + 0.8,
        f1Score: Math.random() * 0.2 + 0.8,
        precision: Math.random() * 0.2 + 0.8,
        recall: Math.random() * 0.2 + 0.8
      },
      realPerformance: {
        accuracy: Math.random() * 0.15 + 0.8
      }
    }
    setCustomModels([...customModels, newCustomModel])
    setIsDialogOpen(false)
    // Reset form
    setNewModel({
      name: '',
      type: 'classification',
      inputSchema: '',
      pythonVersion: '3.9',
      requirementsTxt: null,
      modelPkl: null,
    })
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Custom Models</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add New Model
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Custom Model</DialogTitle>
              <DialogDescription>
                Enter the details for your new custom ML model.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={newModel.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="type" className="text-right">
                    Type
                  </Label>
                  <Select
                    name="type"
                    value={newModel.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select model type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="classification">Classification</SelectItem>
                      <SelectItem value="regression">Regression</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="inputSchema" className="text-right">
                    Input Schema
                  </Label>
                  <Textarea
                    id="inputSchema"
                    name="inputSchema"
                    value={newModel.inputSchema}
                    onChange={handleInputChange}
                    className="col-span-3"
                    placeholder="Enter JSON schema for input data"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="pythonVersion" className="text-right">
                    Python Version
                  </Label>
                  <Select
                    name="pythonVersion"
                    value={newModel.pythonVersion}
                    onValueChange={(value) => handleSelectChange('pythonVersion', value)}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select Python version" />
                    </SelectTrigger>
                    <SelectContent>
                      {pythonVersions.map((version) => (
                        <SelectItem key={version} value={version}>
                          {version}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="requirementsTxt" className="text-right">
                    Requirements.txt
                  </Label>
                  <div className="col-span-3">
                    <FileUploader
                      id="requirementsTxt"
                      accept=".txt"
                      onFilesSelected={(files) => handleFileUpload('requirementsTxt', files)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="modelPkl" className="text-right">
                    Model PKL
                  </Label>
                  <div className="col-span-3">
                    <FileUploader
                      id="modelPkl"
                      accept=".pkl"
                      onFilesSelected={(files) => handleFileUpload('modelPkl', files)}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Add Model</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {customModels.map((model) => (
          <CustomModelCard key={model.id} model={model} />
        ))}
      </div>
    </div>
  )
}

