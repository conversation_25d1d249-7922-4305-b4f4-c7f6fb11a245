"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CustomFraudDetectionResults } from "@/components/models/custom-fraud-detection/custom-fraud-detection-results"
import { CustomFraudDetectionForm } from "@/components/models/custom-fraud-detection/custom-fraud-detection-form"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"
import { CSVImportModal } from "@/components/csv-import-modal"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface RedFlagResult {
flag: string
score: number
confirmed: boolean
comment: string
}

export default function CustomFraudDetectionPage() {
const [bulkResults, setBulkResults] = useState<any[] | null>(null)
const [analyzing, setAnalyzing] = useState(false)
const [analysisResults, setAnalysisResults] = useState<RedFlagResult[] | null>(null)

const handleSubmit = async (formData: { context: string, redFlags: string[] }) => {
  setAnalyzing(true)
  try {
    // Simulate API call to fraud detection model
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const results: RedFlagResult[] = formData.redFlags.map(flag => {
      const score = Math.random()
      const confirmed = score > 0.5
      return {
        flag,
        score,
        confirmed,
        comment: confirmed
          ? `High likelihood of fraud detected related to "${flag}".`
          : `No significant evidence of fraud found related to "${flag}".`
      }
    })

    setAnalysisResults(results)
  } finally {
    setAnalyzing(false)
  }
}

const handleBulkPredictions = async (data: any[]) => {
  const results = []
  for (const item of data) {
    await new Promise(resolve => setTimeout(resolve, 100))
    const detectedFlags = item.redFlags?.split(',').map((flag: string) => flag.trim()).filter((flag: string) => Math.random() > 0.5) || []
    const confidence = Math.random()
    results.push({
      ...item,
      detectedRedFlags: detectedFlags,
      confidence,
      analysis: `Analysis complete. Confidence: ${(confidence * 100).toFixed(2)}%. Detected ${detectedFlags.length} red flags.`
    })
  }
  setBulkResults(results)
}

const modelInfo = {
  title: "Custom Fraud Detection Model",
  description: "Analyze context for potential fraud using custom red flags.",
  type: "classification" as const,
  accuracy: 90,
  purpose: [
    "Detect potential fraud in various contexts",
    "Customize red flags based on specific needs",
    "Analyze text and identify suspicious patterns"
  ],
  methodology: [
    "Uses natural language processing (NLP) to analyze text",
    "Matches predefined and custom red flags against the context",
    "Calculates a confidence score based on detected flags"
  ],
  outputs: [
    "Detected Red Flags",
    "Confidence Score",
    "Analysis Summary"
  ]
}

const apiInfo = {
  modelName: "CustomFraudDetection",
  endpoint: "custom-fraud-detection",
  requestExample: {
    context: "The customer filed a temporary incapacity to work during the month of June to go to fruit harvest and continue getting paid by the company.",
    redFlags: [
      "Filed temporary incapacity during June",
      "Mentioned fruit harvest",
      "Continues receiving payment"
    ]
  },
  responseExample: {
    detectedRedFlags: [
      "Filed temporary incapacity during June",
      "Mentioned fruit harvest",
      "Continues receiving payment"
    ],
    confidence: 0.95,
    analysis: "High probability of fraud based on detected red flags."
  }
}

return (
  <div className="container mx-auto py-6">
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-3xl font-bold">Custom Fraud Detection Model</h1>
      <div className="flex gap-2">
        <ModelInfoDialog {...modelInfo} />
        <APIDialog {...apiInfo} />
        <CSVImportModal onImport={handleBulkPredictions} modelName="Custom Fraud Detection" />
      </div>
    </div>

    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Fraud Detection Input</CardTitle>
          <CardDescription>Enter context and red flags for analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <CustomFraudDetectionForm onSubmit={handleSubmit} analyzing={analyzing} />
        </CardContent>
      </Card>

      {analysisResults && (
        <CustomFraudDetectionResults results={analysisResults} />
      )}
    </div>

    {bulkResults && (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Bulk Prediction Results</CardTitle>
          <CardDescription>Showing results for {bulkResults.length} imported entries</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Context</TableHead>
                <TableHead>Detected Red Flags</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Analysis</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bulkResults.map((result, index) => (
                <TableRow key={index}>
                  <TableCell>{result.context || `Context ${index + 1}`}</TableCell>
                  <TableCell>{result.detectedRedFlags?.join(', ') || "None"}</TableCell>
                  <TableCell>{(result.confidence * 100).toFixed(2)}%</TableCell>
                  <TableCell>{result.analysis}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )}
  </div>
)
}

