"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChurnForm } from "@/components/churn-form"
import { ChurnResults } from "@/components/churn-results"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"
import { CSVImportModal } from "@/components/csv-import-modal"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function CustomerChurnPage() {
  const [churnResults, setChurnResults] = useState<any>(null);
  const [bulkResults, setBulkResults] = useState<any[] | null>(null)

  const handleSubmit = (churnInfo: any) => {
    // Mock results
    setChurnResults({
      churnProbability: Math.random(),
      riskLevel: ["High", "Medium", "Low"][Math.floor(Math.random() * 3)],
      topFactors: [
        "Low engagement with customer support",
        "Decreasing usage of services",
        "Recent price sensitivity"
      ],
      recommendedActions: [
        "Offer personalized retention package",
        "Increase proactive customer support",
        "Conduct satisfaction survey"
      ]
    });
  };

  const handleBulkPredictions = async (data: any[]) => {
    // Simulate bulk predictions with processing delay
    const results = []
    for (const item of data) {
      await new Promise(resolve => setTimeout(resolve, 100)) // Simulate processing time
      results.push({
        ...item,
        churnProbability: Math.random(),
        riskLevel: ["High", "Medium", "Low"][Math.floor(Math.random() * 3)],
      })
    }
    setBulkResults(results)
  }

  const modelInfo = {
    title: "Customer Churn Prediction Model",
    description: "A machine learning model that predicts the likelihood of a customer discontinuing their relationship with the company.",
    type: "classification" as const,
    accuracy: 92,
    purpose: [
      "Identify customers at high risk of churning",
      "Enable proactive retention strategies",
      "Optimize customer retention spending",
      "Improve overall customer satisfaction and loyalty"
    ],
    methodology: [
      "Analyzes historical customer behavior and interactions",
      "Considers demographic data and service usage patterns",
      "Evaluates customer satisfaction metrics and feedback",
      "Uses machine learning algorithms to identify churn indicators"
    ],
    outputs: [
      "Churn Probability Score",
      "Risk Level Classification",
      "Top Contributing Factors",
      "Recommended Retention Actions"
    ]
  };

  const apiInfo = {
    modelName: "Customer Churn Prediction Model",
    endpoint: "customer-churn-prediction",
    requestExample: {
      customer_id: "C123456",
      tenure: 24,
      contract_type: "month-to-month",
      total_spend: 1500,
      last_interaction: "2023-05-15",
      service_calls_last_month: 3,
      payment_delays: 1
    },
    responseExample: {
      churn_probability: 0.75,
      risk_level: "High",
      top_factors: [
        "Month-to-month contract",
        "Recent increase in service calls",
        "History of payment delays"
      ],
      recommended_actions: [
        "Offer contract upgrade with incentives",
        "Proactive outreach from customer success team",
        "Provide flexible payment options"
      ],
      confidence: 0.92,
      processing_time: "0.05s"
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Customer Churn Prediction Model</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
          <CSVImportModal onImport={handleBulkPredictions} modelName="Churn Prediction" />
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
            <CardDescription>Enter customer details to predict churn probability</CardDescription>
          </CardHeader>
          <CardContent>
            <ChurnForm onSubmit={handleSubmit} />
          </CardContent>
        </Card>
        {churnResults && (
          <ChurnResults results={churnResults} />
        )}
      </div>
      {bulkResults && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Bulk Prediction Results</CardTitle>
            <CardDescription>
              Showing results for {bulkResults.length} imported customers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer ID</TableHead>
                  <TableHead>Churn Probability</TableHead>
                  <TableHead>Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bulkResults.map((result, index) => (
                  <TableRow key={index}>
                    <TableCell>{result.customer_id || `Customer ${index + 1}`}</TableCell>
                    <TableCell>{(result.churnProbability * 100).toFixed(2)}%</TableCell>
                    <TableCell>{result.riskLevel}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

