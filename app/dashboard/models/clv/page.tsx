"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CLVForm } from "@/components/clv-form"
import { CLVResults } from "@/components/clv-results"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"
import { CSVImportModal } from "@/components/csv-import-modal"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function CLVPage() {
const [clvResults, setClvResults] = useState<any>(null);
const [bulkResults, setBulkResults] = useState<any[] | null>(null)

const handleSubmit = (clvInfo: any) => {
  // Mock results
  setClvResults({
    predictedCLV: Math.floor(Math.random() * 50000) + 10000,
    churnProbability: Math.random(),
    upsellProbability: Math.random(),
    customerSegment: ["High Value", "Medium Value", "Low Value"][Math.floor(Math.random() * 3)],
    explanation: "Based on the customer's profile and historical data..."
  });
};

const handleBulkPredictions = async (data: any[]) => {
  // Simulate bulk predictions with processing delay
  const results = []
  for (const item of data) {
    await new Promise(resolve => setTimeout(resolve, 100)) // Simulate processing time
    results.push({
      ...item,
      predictedCLV: Math.floor(Math.random() * 50000) + 10000,
      churnProbability: Math.random(),
      upsellProbability: Math.random(),
      customerSegment: ["High Value", "Medium Value", "Low Value"][Math.floor(Math.random() * 3)],
    })
  }
  setBulkResults(results)
}

const modelInfo = {
  title: "Customer Lifetime Value (CLV) Model",
  description: "A predictive analytics model that forecasts the total value a customer will bring to the business over their entire relationship.",
  type: "regression" as const,
  accuracy: 89,
  purpose: [
    "Predict future revenue potential from each customer",
    "Identify high-value customers for retention focus",
    "Guide customer acquisition and retention spending",
    "Optimize customer service and engagement strategies"
  ],
  methodology: [
    "Analyzes historical purchase patterns and policy renewals",
    "Considers demographic data and customer behavior",
    "Evaluates customer engagement and satisfaction metrics",
    "Uses machine learning to identify value indicators and trends"
  ],
  outputs: [
    "Predicted Customer Lifetime Value ($)",
    "Churn Probability Score",
    "Upsell Opportunity Score",
    "Customer Segment Classification",
    "Recommended Actions and Strategies"
  ]
};

const apiInfo = {
  modelName: "Customer Lifetime Value Model",
  endpoint: "customer-lifetime-value",
  requestExample: {
    customer_age: 35,
    annual_income: 75000,
    product_category: "auto",
    purchase_frequency: 2,
    customer_tenure: 5,
    total_purchases: 8,
    average_purchase_value: 1200
  },
  responseExample: {
    predicted_clv: 45000,
    churn_probability: 0.15,
    upsell_probability: 0.75,
    customer_segment: "High Value",
    confidence: 0.89,
    processing_time: "0.08s",
    recommendations: [
      "Consider premium product offerings",
      "Eligible for loyalty program upgrade",
      "High potential for cross-selling"
    ]
  }
};

return (
  <div className="container mx-auto py-6">
    <div className="flex items-center justify-between mb-6">
      <h1 className="text-3xl font-bold">Customer Lifetime Value Model</h1>
      <div className="flex gap-2">
        <ModelInfoDialog {...modelInfo} />
        <APIDialog {...apiInfo} />
        <CSVImportModal onImport={handleBulkPredictions} modelName="CLV Calculation" />
      </div>
    </div>
    
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
          <CardDescription>Enter customer details to predict CLV</CardDescription>
        </CardHeader>
        <CardContent>
          <CLVForm onSubmit={handleSubmit} />
        </CardContent>
      </Card>
      {clvResults && (
        <CLVResults results={clvResults} />
      )}
    </div>
    {bulkResults && (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Bulk Prediction Results</CardTitle>
          <CardDescription>
            Showing results for {bulkResults.length} imported customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Predicted CLV</TableHead>
                <TableHead>Churn Probability</TableHead>
                <TableHead>Upsell Probability</TableHead>
                <TableHead>Customer Segment</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bulkResults.map((result, index) => (
                <TableRow key={index}>
                  <TableCell>{result.customer || `Customer ${index + 1}`}</TableCell>
                  <TableCell>${result.predictedCLV.toLocaleString()}</TableCell>
                  <TableCell>{(result.churnProbability * 100).toFixed(2)}%</TableCell>
                  <TableCell>{(result.upsellProbability * 100).toFixed(2)}%</TableCell>
                  <TableCell>{result.customerSegment}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )}
  </div>
)
}

