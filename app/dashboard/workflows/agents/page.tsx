"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Search, X, Pencil } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AgentCard } from "@/components/agent-card"
import { ToolConfigDialog } from "@/components/tool-config-dialog"
import {Checkbox} from "@/components/ui/checkbox"

const llms = ["GPT-3.5", "GPT-4", "Claude", "Jurassic-1"]
const tools = [
  "Web Access",
  "SQL Connection",
  "Document Parser",
  "Code Executor",
  "Email Sender",
  "Calculator",
  "Image Analysis",
  "Speech Recognition",
]

interface Agent {
  id: string
  name: string
  description: string
  historicalContext: string
  llm: string
  availableTools: string[]
}

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([
    { id: '1', name: 'Claims Adjuster Agent', description: 'Adjusts claims based on policy and damage information.', historicalContext: 'Specialized in auto claims with 5 years of experience.', llm: 'GPT-4', availableTools: ['Web Access', 'Document Parser'] },
    { id: '2', name: 'Fraud Detection Agent', description: 'Detects potential fraud in claims.', historicalContext: 'Trained on 10,000 fraud cases.', llm: 'GPT-3.5', availableTools: ['SQL Connection', 'Image Analysis'] },
    { id: '3', name: 'Underwriting Agent', description: 'Assesses risk and determines policy premiums.', historicalContext: 'Expert in life insurance underwriting.', llm: 'Claude', availableTools: [] },
    { id: '4', name: 'Customer Service Agent', description: 'Answers customer inquiries and resolves issues.', historicalContext: 'Handles general insurance questions.', llm: 'Jurassic-1', availableTools: [] },
    { id: '5', name: 'Policy Renewal Agent', description: 'Manages policy renewals and updates.', historicalContext: 'Experienced in commercial insurance policies.', llm: 'GPT-4', availableTools: ['Web Access'] },
    { id: '6', name: 'Risk Assessment Agent', description: 'Evaluates risk profiles for individuals and businesses.', historicalContext: 'Focuses on property and casualty insurance.', llm: 'GPT-3.5', availableTools: ['Document Parser'] },
    { id: '7', name: 'Claims Segmentation Agent', description: 'Categorizes claims for efficient processing.', historicalContext: 'Handles high-volume claims processing.', llm: 'Claude', availableTools: ['SQL Connection'] },
    { id: '8', name: 'Lead Qualification Agent', description: 'Qualifies leads for sales teams.', historicalContext: 'Identifies potential customers for various insurance products.', llm: 'Jurassic-1', availableTools: [] },
    { id: '9', name: 'Compliance Agent', description: 'Ensures compliance with regulations.', historicalContext: 'Monitors regulatory changes and updates.', llm: 'GPT-4', availableTools: [] },
    { id: '10', name: 'Market Analysis Agent', description: 'Analyzes market trends and competitor strategies.', historicalContext: 'Provides insights into insurance market dynamics.', llm: 'GPT-3.5', availableTools: ['Web Access'] },
  ])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [agentToEdit, setAgentToEdit] = useState<Agent | null>(null)
  const [newAgent, setNewAgent] = useState({
    name: '',
    description: '',
    historicalContext: '',
    llm: '',
    availableTools: [] as string[]
  })
  const [selectedTool, setSelectedTool] = useState(null)

  const handleCreateAgent = () => {
    if (!newAgent.name || !newAgent.description || !newAgent.llm) {
      // Handle validation errors (toast message, etc.)
      return
    }

    setAgents(prev => [
      ...prev,
      {
        id: (prev.length + 1).toString(),
        ...newAgent
      }
    ])
    setIsCreateDialogOpen(false)
    setNewAgent({
      name: '',
      description: '',
      historicalContext: '',
      llm: '',
      availableTools: []
    })
  }

  const handleEditAgent = (agent: Agent) => {
    setIsEditing(true)
    setAgentToEdit(agent)
    setNewAgent({...agent})
    setIsCreateDialogOpen(true)
  }

  const handleSaveAgent = () => {
    if (!newAgent.name || !newAgent.description || !newAgent.llm) {
      // Handle validation errors
      return
    }

    setAgents(prev => prev.map(a =>
      a.id === agentToEdit?.id
        ? { ...a, ...newAgent }
        : a
    ))
    setIsCreateDialogOpen(false)
    setIsEditing(false)
    setAgentToEdit(null)
    setNewAgent({
      name: '',
      description: '',
      historicalContext: '',
      llm: '',
      availableTools: []
    })
  }

  const handleDeleteAgent = (agentId: string) => {
    setAgents(prev => prev.filter(agent => agent.id !== agentId))
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">AI Agents</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Agent
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {agents.map((agent) => (
          <AgentCard key={agent.id} agent={agent} onEdit={() => handleEditAgent(agent)} onDelete={() => handleDeleteAgent(agent.id)} />
        ))}
      </div>

      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Agent' : 'Create New Agent'}</DialogTitle>
            <DialogDescription>
              {isEditing ? 'Modify the agent\'s information.' : 'Define the agent\'s characteristics and purpose.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Agent Name</Label>
              <Input
                id="name"
                value={newAgent.name}
                onChange={(e) => setNewAgent(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter agent name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newAgent.description}
                onChange={(e) => setNewAgent(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the agent's purpose"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="historicalContext">Historical Context</Label>
              <Textarea
                id="historicalContext"
                value={newAgent.historicalContext}
                onChange={(e) => setNewAgent(prev => ({ ...prev, historicalContext: e.target.value }))}
                placeholder="Provide relevant background information"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="llm">LLM</Label>
              <Select
                value={newAgent.llm}
                onValueChange={(value) => setNewAgent(prev => ({ ...prev, llm: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select LLM" />
                </SelectTrigger>
                <SelectContent>
                  {llms.map((llm) => (
                    <SelectItem key={llm} value={llm}>{llm}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tools">Available Tools</Label>
              <ScrollArea className="h-40 w-full rounded-md border p-4">
                {tools.map((tool, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <Checkbox
                      id={`tool-${index}`}
                      checked={newAgent.availableTools.includes(tool)}
                      onCheckedChange={() => {
                        setNewAgent(prev => ({
                          ...prev,
                          availableTools: prev.availableTools.includes(tool)
                            ? prev.availableTools.filter(t => t !== tool)
                            : [...prev.availableTools, tool]
                        }))
                      }}
                    />
                    <Label htmlFor={`tool-${index}`} className="text-sm">{tool}</Label>
                  </div>
                ))}
              </ScrollArea>
            </div>
          </div>
          <DialogFooter>
            {isEditing ? (
              <Button type="submit" onClick={handleSaveAgent}>Save Changes</Button>
            ) : (
              <Button type="submit" onClick={handleCreateAgent}>Create Agent</Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {selectedTool && (
        <ToolConfigDialog
          tool={selectedTool}
          open={!!selectedTool}
          onOpenChange={() => setSelectedTool(null)}
        />
      )}
    </div>
  )
}

