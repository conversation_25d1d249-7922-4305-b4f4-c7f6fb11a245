"use client"

import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { WorkflowCard } from "@/components/workflow-card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  AlertTriangle,
  Car,
  MessageSquare,
  FileSearch,
  Calculator,
  UserCheck,
  Receipt,
  ClipboardCheck,
  Scale,
  BadgeCheck,
  FileWarning,
} from "lucide-react";

interface Workflow {
  title: string;
  description: string;
  icon: React.ReactNode;
  tasks: { name: string; description: string }[];
  successRate: number;
  averageTime: string;
  status: string;
  type: "core" | "custom";
}

const workflows = [
  {
    title: "FNOL Processing",
    description: "Automated First Notice of Loss processing with intelligent routing",
    icon: <FileText className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Document Analysis", description: "Extract and validate information from submitted documents" },
      { name: "Risk Assessment", description: "Evaluate initial risk factors and severity" },
      { name: "Route Assignment", description: "Intelligently assign to appropriate claims handler" },
      { name: "Client Communication", description: "Generate and send automated updates to client" }
    ],
    successRate: 94,
    averageTime: "12 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Fraud Detection",
    description: "Advanced fraud analysis using multiple AI models",
    icon: <AlertTriangle className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Pattern Analysis", description: "Detect suspicious patterns in claims data" },
      { name: "Network Analysis", description: "Identify potential fraud networks" },
      { name: "Document Verification", description: "Validate authenticity of submitted documents" },
      { name: "Risk Scoring", description: "Calculate fraud risk score" }
    ],
    successRate: 89,
    averageTime: "8 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Vehicle Appraisal",
    description: "Automated vehicle damage assessment and valuation",
    icon: <Car className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Image Analysis", description: "Analyze damage from uploaded photos/videos" },
      { name: "Cost Estimation", description: "Calculate repair costs based on damage" },
      { name: "Market Valuation", description: "Determine current market value" },
      { name: "Report Generation", description: "Create detailed appraisal report" }
    ],
    successRate: 92,
    averageTime: "15 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Customer Support AI",
    description: "Intelligent customer inquiry handling and response",
    icon: <MessageSquare className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Query Analysis", description: "Understand and categorize customer inquiries" },
      { name: "Response Generation", description: "Generate appropriate responses" },
      { name: "Sentiment Analysis", description: "Monitor customer satisfaction" },
      { name: "Escalation Routing", description: "Route complex cases to human agents" }
    ],
    successRate: 87,
    averageTime: "3 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Policy Analysis",
    description: "Automated policy review and coverage assessment",
    icon: <FileSearch className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Coverage Review", description: "Analyze policy coverage details" },
      { name: "Gap Analysis", description: "Identify potential coverage gaps" },
      { name: "Risk Assessment", description: "Evaluate policy risks" },
      { name: "Recommendations", description: "Generate coverage recommendations" }
    ],
    successRate: 95,
    averageTime: "10 minutes",
    status: "active",
    type: "custom"
  },
  {
    title: "Premium Calculator",
    description: "Dynamic premium calculation using ML models",
    icon: <Calculator className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Risk Evaluation", description: "Assess risk factors" },
      { name: "Market Analysis", description: "Compare market rates" },
      { name: "Price Optimization", description: "Optimize premium pricing" },
      { name: "Quote Generation", description: "Generate detailed quote" }
    ],
    successRate: 96,
    averageTime: "5 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Client Onboarding",
    description: "Automated client verification and onboarding",
    icon: <UserCheck className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "ID Verification", description: "Verify client identity documents" },
      { name: "Background Check", description: "Perform automated background checks" },
      { name: "Risk Profiling", description: "Create client risk profile" },
      { name: "Document Generation", description: "Generate onboarding documents" }
    ],
    successRate: 91,
    averageTime: "20 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Claims Settlement",
    description: "Automated claims processing and settlement",
    icon: <Receipt className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Claim Validation", description: "Verify claim details and coverage" },
      { name: "Payment Processing", description: "Process and authorize payments" },
      { name: "Document Generation", description: "Generate settlement documents" },
      { name: "Client Communication", description: "Send settlement notifications" }
    ],
    successRate: 93,
    averageTime: "25 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Compliance Check",
    description: "Automated regulatory compliance verification",
    icon: <ClipboardCheck className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Policy Review", description: "Review policy against regulations" },
      { name: "Document Validation", description: "Verify required documentation" },
      { name: "Compliance Scoring", description: "Calculate compliance score" },
      { name: "Report Generation", description: "Generate compliance reports" }
    ],
    successRate: 97,
    averageTime: "15 minutes",
    status: "active",
    type: "custom"
  },
  {
    title: "Underwriting Assistant",
    description: "AI-powered underwriting decision support",
    icon: <Scale className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Risk Analysis", description: "Analyze application risk factors" },
      { name: "Market Research", description: "Compare market statistics" },
      { name: "Decision Support", description: "Generate underwriting recommendations" },
      { name: "Documentation", description: "Prepare underwriting documents" }
    ],
    successRate: 88,
    averageTime: "30 minutes",
    status: "active",
    type: "core"
  },
  {
    title: "Quality Assurance",
    description: "Automated quality control for claims processing",
    icon: <BadgeCheck className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Process Audit", description: "Review claims handling process" },
      { name: "Compliance Check", description: "Verify regulatory compliance" },
      { name: "Error Detection", description: "Identify potential errors" },
      { name: "Report Generation", description: "Generate QA reports" }
    ],
    successRate: 94,
    averageTime: "18 minutes",
    status: "active",
    type: "custom"
  },
  {
    title: "Risk Assessment",
    description: "Comprehensive risk analysis and monitoring",
    icon: <FileWarning className="h-5 w-5 text-primary" />,
    tasks: [
      { name: "Data Analysis", description: "Analyze historical and current data" },
      { name: "Trend Detection", description: "Identify risk patterns" },
      { name: "Impact Assessment", description: "Evaluate potential impact" },
      { name: "Monitoring Setup", description: "Configure risk monitoring" }
    ],
    successRate: 90,
    averageTime: "22 minutes",
    status: "active",
    type: "core"
  }
];

export default function WorkflowsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterValue, setFilterValue] = useState("all");

  // Get unique workflow types dynamically
  const workflowTypes = useMemo(() => {
    const types = workflows.map((workflow) => workflow.type);
    return ["all", ...new Set(types)];
  }, []);

  // Filter workflows based on search and selected type
  const filteredWorkflows = useMemo(() => {
    return workflows.filter((workflow) => {
      const matchesType = filterValue === "all" || workflow.type === filterValue;
      const matchesSearch =
        workflow.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        workflow.description.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesType && matchesSearch;
    });
  }, [searchQuery, filterValue]);

  return (
    <div className="container mx-auto py-6">
      {/* Header with Search and Filter */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">AI Workflows</h1>
        <div className="flex space-x-4">
          <Input
            placeholder="Search workflows..."
            className="w-[250px]"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Select value={filterValue} onValueChange={setFilterValue}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              {workflowTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type === "all" ? "All Types" : type.charAt(0).toUpperCase() + type.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Workflows Grid or Fallback Message */}
      {filteredWorkflows.length === 0 ? (
        <p className="text-center text-gray-500">No workflows found matching your criteria.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkflows.map((workflow, index) => (
            <WorkflowCard key={index} {...workflow} />
          ))}
        </div>
      )}
    </div>
  );
}