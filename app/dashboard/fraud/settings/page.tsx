"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, X, Pencil, Trash } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface FraudType {
    id: string;
    name: string;
    description: string;
    severity: "Low" | "Medium" | "High";
}

const initialFraudTypes: FraudType[] = [
    {
        id: "1",
        name: "Multiple Claims",
        description: "Filing multiple claims for the same incident",
        severity: "High",
    },
    {
        id: "2",
        name: "Identity Fraud",
        description: "Using a stolen identity to file a claim",
        severity: "High",
    },
    {
        id: "3",
        name: "Inflated Damages",
        description: "Exaggerating the value of damages",
        severity: "Medium",
    },
    {
        id: "4",
        name: "Staged Accident",
        description: "Intentionally causing an accident",
        severity: "High",
    },
];

export default function FraudSettingsPage() {
    const [fraudTypes, setFraudTypes] = useState<FraudType[]>(initialFraudTypes);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingFraudType, setEditingFraudType] = useState<FraudType | null>(null);
    const [newFraudType, setNewFraudType] = useState<FraudType>({
        id: "",
        name: "",
        description: "",
        severity: "Medium",
    });

    const handleAddFraudType = () => {
        const newId = (fraudTypes.length + 1).toString();
        setNewFraudType({ ...newFraudType, id: newId });
        setEditingFraudType(null);
        setIsDialogOpen(true);
    };

    const handleEditFraudType = (fraudType: FraudType) => {
        setNewFraudType({ ...fraudType });
        setEditingFraudType(fraudType);
        setIsDialogOpen(true);
    };

    const handleDeleteFraudType = (id: string) => {
        setFraudTypes(fraudTypes.filter((type) => type.id !== id));
        toast({
            title: "Fraud type deleted",
            description: "The fraud type has been deleted successfully.",
        });
    };

    const handleSaveFraudType = () => {
        if (editingFraudType) {
            // Update existing fraud type
            setFraudTypes(
                fraudTypes.map((type) =>
                    type.id === editingFraudType.id ? newFraudType : type
                )
            );
            toast({
                title: "Fraud type updated",
                description: "The fraud type has been updated successfully.",
            });
        } else {
            // Add new fraud type
            setFraudTypes([...fraudTypes, newFraudType]);
            toast({
                title: "Fraud type added",
                description: "The fraud type has been added successfully.",
            });
        }

        setIsDialogOpen(false);
        setNewFraudType({
            id: "",
            name: "",
            description: "",
            severity: "Medium",
        });
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
        setNewFraudType({
            id: "",
            name: "",
            description: "",
            severity: "Medium",
        });
        setEditingFraudType(null)
    }

    return (
        <div className="space-y-6">
            <h1 className="text-3xl font-bold">Fraud Settings</h1>

            <Card>
                <CardHeader>
                    <CardTitle>Manage Fraud Types</CardTitle>
                    <CardDescription>Add, edit, or delete fraud types</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="mb-4">
                        <Button onClick={handleAddFraudType}>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Fraud Type
                        </Button>
                    </div>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Description</TableHead>
                                <TableHead>Severity</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {fraudTypes.map((fraudType) => (
                                <TableRow key={fraudType.id}>
                                    <TableCell>{fraudType.name}</TableCell>
                                    <TableCell>{fraudType.description}</TableCell>
                                    <TableCell>
                                        <Badge
                                            variant={
                                                fraudType.severity === "High"
                                                    ? "destructive"
                                                    : fraudType.severity === "Medium"
                                                        ? "default"
                                                        : "secondary"
                                            }
                                        >
                                            {fraudType.severity}
                                        </Badge>
                                    </TableCell>
                                    <TableCell className="flex items-center space-x-2">
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => handleEditFraudType(fraudType)}
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="destructive"
                                            size="icon"
                                            onClick={() => handleDeleteFraudType(fraudType.id)}
                                        >
                                            <Trash className="h-4 w-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>

            <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
                <DialogContent className="sm:max-w-xl">
                    <DialogHeader>
                        <DialogTitle>{editingFraudType ? "Edit Fraud Type" : "Add New Fraud Type"}</DialogTitle>
                        <DialogDescription>
                            {editingFraudType
                                ? "Modify the details of the fraud type."
                                : "Define a new type of fraud."}
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={(e) => { e.preventDefault(); handleSaveFraudType(); }}>
                        <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Name</Label>
                                <Input
                                    id="name"
                                    value={newFraudType.name}
                                    onChange={(e) =>
                                        setNewFraudType({ ...newFraudType, name: e.target.value })
                                    }
                                    placeholder="Enter fraud type name"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Input
                                    id="description"
                                    value={newFraudType.description}
                                    onChange={(e) =>
                                        setNewFraudType({
                                            ...newFraudType,
                                            description: e.target.value,
                                        })
                                    }
                                    placeholder="Enter a description"
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="severity">Severity</Label>
                                <Select
                                    value={newFraudType.severity}
                                    onValueChange={(value) =>
                                        setNewFraudType({
                                            ...newFraudType,
                                            severity: value as "Low" | "Medium" | "High",
                                        })
                                    }
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select severity" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Low">Low</SelectItem>
                                        <SelectItem value="Medium">Medium</SelectItem>
                                        <SelectItem value="High">High</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit">Save</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}

