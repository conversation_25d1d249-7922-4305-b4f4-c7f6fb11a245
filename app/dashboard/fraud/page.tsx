"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Line, LineChart, ResponsiveContainer, XAxis, YAxis, BarChart, Bar, 
  PieChart, Pie, Cell, Tooltip, ScatterChart, Scatter, ComposedChart,
  Area, RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis 
} from "recharts"
import { ChartContainer, ChartTooltipContent } from "@/components/ui/chart"
import { Button } from "@/components/ui/button"
import { AlertCircle, ArrowUpRight, ArrowDownRight, Filter } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

/* 
  Helper function to decide the date range (number of days) 
  based on the selected time filter.
*/
const getDateCount = (timeRange: string): number => {
  const now = new Date()
  if (timeRange === "last30") return 30
  if (timeRange === "mtd") return now.getDate() // from 1st of month until today
  if (timeRange === "lastYear") return 365
  if (timeRange === "ytd") {
    const start = new Date(now.getFullYear(), 0, 1)
    const diffTime = now.getTime() - start.getTime()
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1
  }
  return 30
}

// Generates time-series fraud score trend data
const generateData = (days: number = 30) => {
  const data = []
  const today = new Date()
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    data.push({
      date: date.toISOString().split('T')[0],
      fraudScore: Math.floor(Math.random() * 100)
    })
  }
  return data
}

// Generates regional data used for fraud and transaction metrics
const generateRegionalData = () => {
  const regions = ['North America', 'Europe', 'Asia', 'South America', 'Africa', 'Oceania']
  return regions.map(region => ({
    region,
    fraudCases: Math.floor(Math.random() * 1000),
    totalTransactions: Math.floor(Math.random() * 10000),
    fraudAmount: Math.floor(Math.random() * 1000000),
  }))
}

// Generates data based on different transaction types
const generateTransactionTypeData = () => {
  const types = ['Credit Card', 'Wire Transfer', 'Mobile Payment', 'Cryptocurrency', 'ACH']
  return types.map(type => ({
    type,
    fraudRate: parseFloat((Math.random() * 10).toFixed(2)),
    volume: Math.floor(Math.random() * 100000),
    averageAmount: Math.floor(Math.random() * 1000),
  }))
}

// Generates a 24-hour overview of fraud attempts and legitimate transactions
const generateTimeOfDayData = () => {
  const data = []
  for (let hour = 0; hour < 24; hour++) {
    data.push({
      hour: `${hour}:00`,
      fraudAttempts: Math.floor(Math.random() * 100),
      legitimateTransactions: Math.floor(Math.random() * 1000),
    })
  }
  return data
}

// Generates a risk scoring distribution
const generateRiskScoreData = () => {
  const scores = []
  for (let i = 0; i < 100; i++) {
    scores.push({
      score: i,
      frequency: Math.floor(Math.random() * 1000 * Math.exp(-((i - 50) ** 2) / 500))
    })
  }
  return scores
}

// Generates merchant category metrics data
const generateMerchantData = () => {
  const categories = [
    'E-commerce', 'Travel', 'Gaming', 'Financial Services', 
    'Retail', 'Digital Goods', 'Entertainment', 'Healthcare'
  ]
  return categories.map(category => ({
    category,
    fraudRate: parseFloat((Math.random() * 8).toFixed(2)),
    transactionVolume: Math.floor(Math.random() * 100000),
    averageTicketSize: Math.floor(Math.random() * 500) + 50,
    declineRate: parseFloat((Math.random() * 15).toFixed(2))
  }))
}

// Generates data for different device and channel metrics
const generateChannelData = () => {
  const channels = [
    'Mobile App', 'Web Browser', 'POS Terminal', 'API Integration',
    'Mobile Web', 'Kiosk', 'Phone Order', 'In-Store'
  ]
  return channels.map(channel => ({
    channel,
    fraudAttempts: Math.floor(Math.random() * 1000),
    successfulFraud: Math.floor(Math.random() * 200),
    preventedFraud: Math.floor(Math.random() * 800),
    legitimateDeclines: Math.floor(Math.random() * 300)
  }))
}

// Generates user behavior patterns data
const generateBehaviorData = () => {
  return {
    velocityMetrics: Array.from({ length: 24 }).map((_, hour) => ({
      hour,
      transactions: Math.floor(Math.random() * 100),
      uniqueIPs: Math.floor(Math.random() * 50),
      deviceChanges: Math.floor(Math.random() * 20)
    })),
    geoDispersion: Array.from({ length: 10 }).map((_, i) => ({
      distance: i * 100,
      frequency: Math.floor(Math.random() * 1000 * Math.exp(-(i ** 2) / 20))
    }))
  }
}

// Generates example models data with insurance fraud detection models
const generateModelsData = () => {
  return [
    {
      id: "modelA",
      name: "Claim Clarity",
      mlMetrics: { accuracy: 93, precision: 90, recall: 88, f1Score: 89 },
      featureImportance: [
        { feature: "Claim Amount", importance: 0.35 },
        { feature: "Claim Frequency", importance: 0.25 },
        { feature: "Customer Tenure", importance: 0.15 }
      ],
      fraudsFlagged: 135,
      fraudsConfirmed: 85,
      potentialSavings: 125000,
      pendingSavings: 30000,
      actualSavings: 95000
    },
    {
      id: "modelB",
      name: "Premium Fraud Monitor",
      mlMetrics: { accuracy: 91, precision: 87, recall: 90, f1Score: 88 },
      featureImportance: [
        { feature: "Premium Amount", importance: 0.40 },
        { feature: "Claim History", importance: 0.30 },
        { feature: "Policy Age", importance: 0.10 }
      ],
      fraudsFlagged: 120,
      fraudsConfirmed: 80,
      potentialSavings: 105000,
      pendingSavings: 25000,
      actualSavings: 80000
    },
    {
      id: "modelC",
      name: "Risk Eagle Eye",
      mlMetrics: { accuracy: 94, precision: 92, recall: 86, f1Score: 89 },
      featureImportance: [
        { feature: "Risk Score", importance: 0.50 },
        { feature: "Claim Amount", importance: 0.20 },
        { feature: "Policy Renewal", importance: 0.10 }
      ],
      fraudsFlagged: 150,
      fraudsConfirmed: 90,
      potentialSavings: 135000,
      pendingSavings: 40000,
      actualSavings: 95000
    },
    {
      id: "modelD",
      name: "FraudGuard Sentinel",
      mlMetrics: { accuracy: 92, precision: 89, recall: 87, f1Score: 88 },
      featureImportance: [
        { feature: "Claim Frequency", importance: 0.30 },
        { feature: "Customer Age", importance: 0.25 },
        { feature: "Policy Type", importance: 0.20 }
      ],
      fraudsFlagged: 110,
      fraudsConfirmed: 70,
      potentialSavings: 95000,
      pendingSavings: 20000,
      actualSavings: 75000
    },
    {
      id: "modelE",
      name: "Integrity Insight",
      mlMetrics: { accuracy: 95, precision: 93, recall: 89, f1Score: 91 },
      featureImportance: [
        { feature: "Loss History", importance: 0.45 },
        { feature: "Claim Duration", importance: 0.20 },
        { feature: "Customer Score", importance: 0.15 }
      ],
      fraudsFlagged: 140,
      fraudsConfirmed: 95,
      potentialSavings: 150000,
      pendingSavings: 35000,
      actualSavings: 115000
    },
    {
      id: "modelF",
      name: "Policy Protector",
      mlMetrics: { accuracy: 90, precision: 85, recall: 84, f1Score: 84 },
      featureImportance: [
        { feature: "Policy Premium", importance: 0.38 },
        { feature: "Claim Timing", importance: 0.22 },
        { feature: "Customer Location", importance: 0.18 }
      ],
      fraudsFlagged: 130,
      fraudsConfirmed: 75,
      potentialSavings: 110000,
      pendingSavings: 28000,
      actualSavings: 82000
    },
    {
      id: "modelG",
      name: "Underwrite Uncover",
      mlMetrics: { accuracy: 92, precision: 90, recall: 85, f1Score: 87 },
      featureImportance: [
        { feature: "Underwriting Score", importance: 0.42 },
        { feature: "Claim Amount", importance: 0.28 },
        { feature: "Customer Loyalty", importance: 0.12 }
      ],
      fraudsFlagged: 125,
      fraudsConfirmed: 78,
      potentialSavings: 100000,
      pendingSavings: 26000,
      actualSavings: 74000
    },
    {
      id: "modelH",
      name: "Risk Remediator",
      mlMetrics: { accuracy: 93, precision: 91, recall: 88, f1Score: 89 },
      featureImportance: [
        { feature: "Claim Severity", importance: 0.40 },
        { feature: "Risk Exposure", importance: 0.30 },
        { feature: "Customer History", importance: 0.15 }
      ],
      fraudsFlagged: 145,
      fraudsConfirmed: 88,
      potentialSavings: 140000,
      pendingSavings: 34000,
      actualSavings: 106000
    }
  ]
}

export default function FraudDashboard() {
  const [timeRange, setTimeRange] = useState('last30')
  const [newPlotOpen, setNewPlotOpen] = useState(false)
  const [plotType, setPlotType] = useState("line")
  const [dataSource, setDataSource] = useState("fraudScoreTrend")
  const [filters, setFilters] = useState("")
  const daysCount = getDateCount(timeRange)

  // Initialize states to empty values
  const [data, setData] = useState<any[]>([])
  const [regionalData, setRegionalData] = useState<any[]>([])
  const [transactionData, setTransactionData] = useState<any[]>([])
  const [timeData, setTimeData] = useState<any[]>([])
  const [riskScoreData, setRiskScoreData] = useState<any[]>([])
  const [merchantData, setMerchantData] = useState<any[]>([])
  const [channelData, setChannelData] = useState<any[]>([])
  const [behaviorData, setBehaviorData] = useState<any>({})
  const [modelsData, setModelsData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Populate data on the client after mount.
  useEffect(() => {
    const days = getDateCount(timeRange)
    setData(generateData(days))
    setRegionalData(generateRegionalData())
    setTransactionData(generateTransactionTypeData())
    setTimeData(generateTimeOfDayData())
    setRiskScoreData(generateRiskScoreData())
    setMerchantData(generateMerchantData())
    setChannelData(generateChannelData())
    setBehaviorData(generateBehaviorData())
    setModelsData(generateModelsData())
    const timer = setTimeout(() => setIsLoading(false), 1500)
    return () => clearTimeout(timer)
  }, [timeRange])

  const totalTransactions = regionalData.reduce((sum, region) => sum + region.totalTransactions, 0)
  const totalFraudCases = regionalData.reduce((sum, region) => sum + region.fraudCases, 0)
  const fraudRate = totalTransactions ? ((totalFraudCases / totalTransactions) * 100).toFixed(2) : "0.00"
  const averageTransactionAmount = totalFraudCases ? Math.floor(
    regionalData.reduce((sum, region) => sum + region.fraudAmount, 0) / totalFraudCases
  ) : 0

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  return (
    <div className="space-y-8 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last30">Last 30 Days</SelectItem>
              <SelectItem value="mtd">Month to Date</SelectItem>
              <SelectItem value="lastYear">Last Year</SelectItem>
              <SelectItem value="ytd">Year to Date</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => {
            setIsLoading(true)
            const days = getDateCount(timeRange)
            setTimeout(() => {
              setData(generateData(days))
              setRegionalData(generateRegionalData())
              setTransactionData(generateTransactionTypeData())
              setTimeData(generateTimeOfDayData())
              setRiskScoreData(generateRiskScoreData())
              setMerchantData(generateMerchantData())
              setChannelData(generateChannelData())
              setBehaviorData(generateBehaviorData())
              setModelsData(generateModelsData())
              setIsLoading(false)
            }, 1000)
          }} disabled={isLoading}>
            {isLoading ? "Refreshing..." : "Refresh Data"}
          </Button>
          <Button onClick={() => setNewPlotOpen(true)}>Create New Plot</Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid grid-cols-7 gap-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="regional">Regional</TabsTrigger>
          <TabsTrigger value="merchants">Merchants</TabsTrigger>
          <TabsTrigger value="behavior">User Behavior</TabsTrigger>
          <TabsTrigger value="model">ML Metrics</TabsTrigger>
          <TabsTrigger value="modelsInfo">Models Info</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Fraud Score Trend</CardTitle>
              <CardDescription>Based on the selected time range</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  fraudScore: {
                    label: "Fraud Score",
                    color: "hsl(var(--chart-1))"
                  }
                }}
                className="h-[300px] w-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={data}>
                    <XAxis dataKey="date" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                    <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} tickFormatter={(value) => `${value}`} />
                    <Tooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="fraudScore" stroke="var(--color-fraudScore)" strokeWidth={2} dot={false} />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="regional">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Fraud Cases by Region</CardTitle>
                <CardDescription>Distribution of fraud cases</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={regionalData}
                        dataKey="fraudCases"
                        nameKey="region"
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        label
                      >
                        {regionalData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Fraud Amount by Region</CardTitle>
                <CardDescription>Total fraud amount in USD</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={regionalData}>
                      <XAxis dataKey="region" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="fraudAmount" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="merchants">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Merchant Category Risk</CardTitle>
                <CardDescription>Fraud rates by merchant category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={merchantData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="category" />
                      <PolarRadiusAxis />
                      <Radar dataKey="fraudRate" fill="#8884d8" fillOpacity={0.6} />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Transaction Volume vs Fraud Rate</CardTitle>
                <CardDescription>Size indicates average ticket value</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ScatterChart>
                      <XAxis dataKey="transactionVolume" name="Volume" />
                      <YAxis dataKey="fraudRate" name="Fraud Rate (%)" />
                      <Tooltip />
                      <Scatter data={merchantData} fill="#8884d8" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="behavior">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Velocity Patterns</CardTitle>
                <CardDescription>24-hour activity metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={behaviorData.velocityMetrics}>
                      <XAxis dataKey="hour" />
                      <YAxis />
                      <Tooltip />
                      <Area dataKey="transactions" fill="#8884d8" stroke="#8884d8" />
                      <Line dataKey="uniqueIPs" stroke="#82ca9d" />
                      <Bar dataKey="deviceChanges" fill="#ffc658" />
                    </ComposedChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Geographic Dispersion</CardTitle>
                <CardDescription>Transaction distance distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={behaviorData.geoDispersion}>
                      <XAxis dataKey="distance" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="frequency" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="model">
          <Card>
            <CardHeader>
              <CardTitle>ML Metrics</CardTitle>
              <CardDescription>Model performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {modelsData.map((model, index) => (
                  <div key={index}>
                    <h3 className="text-lg font-bold">{model.name}</h3>
                    <p><strong>Accuracy:</strong> {model.mlMetrics.accuracy}%</p>
                    <p><strong>Precision:</strong> {model.mlMetrics.precision}%</p>
                    <p><strong>Recall:</strong> {model.mlMetrics.recall}%</p>
                    <p><strong>F1 Score:</strong> {model.mlMetrics.f1Score}%</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modelsInfo">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Insurance Fraud Detection Models</CardTitle>
                <CardDescription>
                  Below are details for each fraud detection model used in insurance.
                </CardDescription>
              </CardHeader>
            </Card>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {modelsData.map(model => (
                <Card key={model.id} className="bg-white shadow-lg rounded-lg hover:shadow-2xl transition-shadow duration-300">
                  <CardHeader className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-t-lg px-4 py-3">
                    <CardTitle className="text-xl font-bold">{model.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold border-b pb-1">Frauds & Savings Overview</h3>
                      <p><strong>Frauds Flagged:</strong> {model.fraudsFlagged}</p>
                      <p><strong>Frauds Confirmed:</strong> {model.fraudsConfirmed}</p>
                      <p><strong>Potential Savings:</strong> ${model.potentialSavings}</p>
                      <p><strong>Pending Savings:</strong> ${model.pendingSavings}</p>
                      <p><strong>Actual Savings:</strong> ${model.actualSavings}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Risk Alerts Dashboard</CardTitle>
              <CardDescription>Recent critical alerts and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>High Risk Pattern Detected</AlertTitle>
                  <AlertDescription>
                    Unusual velocity detected: 150% increase in high-value transactions from new devices in the APAC region within the last 3 hours.
                  </AlertDescription>
                </Alert>
                <Alert variant="default">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Model Performance Update</AlertTitle>
                  <AlertDescription>
                    Recent model retraining shows 3.2% improvement in precision while maintaining recall above 85%. New features added for device fingerprinting.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {newPlotOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-96">
            <h2 className="text-xl font-bold mb-4">Create New Plot</h2>
            <div className="mb-4">
              <label className="block mb-2">Plot Type</label>
              <Select value={plotType} onValueChange={setPlotType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select plot type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Line Chart</SelectItem>
                  <SelectItem value="bar">Bar Chart</SelectItem>
                  <SelectItem value="pie">Pie Chart</SelectItem>
                  <SelectItem value="scatter">Scatter Chart</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="mb-4">
              <label className="block mb-2">Data Source</label>
              <Select value={dataSource} onValueChange={setDataSource}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select data source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fraudScoreTrend">Fraud Score Trend</SelectItem>
                  <SelectItem value="regionalFraudCases">Regional Fraud Cases</SelectItem>
                  <SelectItem value="merchantRisk">Merchant Risk</SelectItem>
                  <SelectItem value="userBehavior">User Behavior</SelectItem>
                  <SelectItem value="mlMetrics">ML Metrics</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="mb-4">
              <label className="block mb-2">Filters</label>
              <input
                type="text"
                placeholder="Enter filters..."
                className="border border-gray-300 rounded p-2 w-full"
                value={filters}
                onChange={(e) => setFilters(e.target.value)}
              />
            </div>
            <div className="flex justify-end gap-4">
              <Button variant="default" onClick={() => setNewPlotOpen(false)}>Cancel</Button>
              <Button onClick={() => { console.log("Creating new plot with:", { plotType, dataSource, filters }); setNewPlotOpen(false); }}>Create Plot</Button>
            </div>
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>High Risk Pattern Detected</AlertTitle>
          <AlertDescription>
            Unusual velocity detected: 150% increase in high-value transactions from new devices in the APAC region within the last 3 hours.
          </AlertDescription>
        </Alert>

        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Model Performance Update</AlertTitle>
          <AlertDescription>
            Recent model retraining shows 3.2% improvement in precision while maintaining recall above 85%. New features added for device fingerprinting.
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}

