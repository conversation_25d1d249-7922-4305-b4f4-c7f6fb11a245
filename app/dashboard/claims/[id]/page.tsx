
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>t, Printer, Download } from 'lucide-react'
import ClaimDetails from "../../claim-details/[id]/page"


export default function ClaimPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button asChild variant="outline">
            <Link href="/claims/browse">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Claims
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">Claim Details</h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </But<PERSON>>
        </div>
      </div>
      <ClaimDetails/>
    </div>
  )
}

