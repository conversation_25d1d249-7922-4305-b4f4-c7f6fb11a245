"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FileUploader } from "@/components/file-uploader";
import { toast } from "@/components/ui/use-toast";

interface Project {
  name: string;
  description: string;
  products: string[];
  teamMembers: string[];
  documents: File[];
}

const products = [
  "Auto Insurance",
  "Home Insurance",
  "Life Insurance",
  "Health Insurance",
  "Travel Insurance",
];

const teamMembers = ["John Doe", "<PERSON> Smith", "Peter Jones", "Mary Brown"];

export default function ProjectManagementSettings() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [newProject, setNewProject] = useState<Project>({
    name: "",
    description: "",
    products: [],
    teamMembers: [],
    documents: [],
  });

  const handleInputChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setNewProject({ ...newProject, [e.target.name]: e.target.value });
  };

  // const handleProductChange = (value: string[]) => {
  //   setNewProject({ ...newProject, products: value });
  // };

  // const handleTeamMemberChange = (value: string[]) => {
  //   setNewProject({ ...newProject, teamMembers: value });
  // };

  const handleDocumentUpload = (files: File[]) => {
    setNewProject({ ...newProject, documents: files });
  };

  const handleCreateProject = () => {
    if (
      !newProject.name ||
      !newProject.description ||
      newProject.products.length === 0 ||
      newProject.teamMembers.length === 0
    ) {
      toast({
        title: "Error creating project",
        description: "Please fill in all the required fields.",
        variant: "destructive",
      });
      return;
    }

    setProjects([...projects, newProject]);
    setNewProject({
      name: "",
      description: "",
      products: [],
      teamMembers: [],
      documents: [],
    });

    toast({
      title: "Project created",
      description: `Project "${newProject.name}" created successfully.`,
    });
  };

  const handleProductChange = (value: string) => {
    setNewProject((prev) => ({
      ...prev,
      products: prev.products.includes(value)
        ? prev.products.filter((item) => item !== value) // Remove if already selected
        : [...prev.products, value], // Add if not selected
    }));
  };

  const handleTeamMemberChange = (value: string) => {
    setNewProject((prev) => ({
      ...prev,
      teamMembers: prev.teamMembers.includes(value)
        ? prev.teamMembers.filter((item) => item !== value)
        : [...prev.teamMembers, value],
    }));
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Project Management Settings</h1>

      <Card>
        <CardHeader>
          <CardTitle>Create New Project</CardTitle>
          <CardDescription>
            Define the project details, associated products, team members, and
            relevant documents.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                name="name"
                value={newProject.name}
                onChange={handleInputChange}
                placeholder="Enter project name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="projectDescription">Description</Label>
              <Textarea
                id="projectDescription"
                name="description"
                value={newProject.description}
                onChange={handleInputChange}
                placeholder="Describe the project"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="products">Associated Products</Label>
              <Select value="" onValueChange={handleProductChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select products" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product} value={product}>
                      {newProject.products.includes(product) ? "✓ " : ""}{" "}
                      {product}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="teamMembers">Assigned Team Members</Label>
              <Select value="" onValueChange={handleTeamMemberChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select team members" />
                </SelectTrigger>
                <SelectContent>
                  {teamMembers.map((member) => (
                    <SelectItem key={member} value={member}>
                      {newProject.teamMembers.includes(member) ? "✓ " : ""}{" "}
                      {member}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="documents">Relevant Documents</Label>
              <FileUploader
                id="documents"
                onFilesSelected={handleDocumentUpload}
                multiple
              />
            </div>
            <Button onClick={handleCreateProject}>Create Project</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
