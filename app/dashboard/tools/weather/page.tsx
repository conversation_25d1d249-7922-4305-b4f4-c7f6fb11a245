'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from 'lucide-react'
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const apiInfo = {
  toolName: "Weather Tool",
  endpoint: "https://weather-0gek.onrender.com",
  requestExample: {
    date: "2024-03-11",
    time: "10:05",
    location: {
      method: "address",
      address: "Rua cidade de sao paulo",
      latitude: 0,
      longitude: 0
    }
  },
  responseExample: {
    temperature: "25°C",
    wind: "10 km/h",
    rain: "0%",
    events: ["Sunny day"]
  }
}

const modelInfo = {
  title: "Weather Tool",
  description: "This tool retrieves weather information for a given date, time, and location (either by address or geolocation).",
  type: "tool" as const,
  purpose: [
    "Get weather forecasts for specific dates and times",
    "Plan outdoor activities with precise timing",
    "Assess weather-related risks at exact moments",
    "Retrieve weather data for exact geographical coordinates"
  ],
  methodology: [
    "Accesses weather data from reliable sources",
    "Provides historical and forecast data for specific times",
    "Supports both address-based and geolocation-based queries",
    "Includes information about special weather events (e.g., storms, fires)"
  ],
  outputs: [
    "Temperature at the specified time",
    "Wind speed and direction",
    "Precipitation probability",
    "List of weather events"
  ]
}

interface WeatherResponse {
  request_info: {
    date: string;
    time: string;
    location: {
      method: string;
      data: string;
    };
    timestamp: string;
  };
  weather_data: {
    temperature: number;
    wind_speed: number;
    wind_direction: number;
    weather_description: string;
    precipitation: number;
    events: string[];
    detailed_forecast: {
      hourly: {
        time: string;
        temperature: number;
        precipitation: number;
      }[];
    };
  };
  risk_assessment: {
    overall_risk_score: number;
    risk_category: string;
    recommendations: string;
  };
  ai_analysis: {
    weather_summary: string;
    weather_implications: string[];
  };
  summary: string;
}

export default function WeatherPage() {
  const [dateTime, setDateTime] = useState("")
  const [address, setAddress] = useState("")
  const [latitude, setLatitude] = useState("")
  const [longitude, setLongitude] = useState("")
  const [weatherData, setWeatherData] = useState<WeatherResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [inputMethod, setInputMethod] = useState<"address" | "geolocation">("address")

  const handleGetWeather = async () => {
    if (!dateTime || (inputMethod === "address" && !address) || (inputMethod === "geolocation" && (!latitude || !longitude))) {
      setError("Please fill in all required fields")
      return
    }

    setIsLoading(true)
    setError(null)
    setWeatherData(null)

    // Format date and time from datetime-local input
    const date = new Date(dateTime)
    const formattedDate = date.toISOString().split('T')[0]
    const formattedTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`

    const payload = {
      date: formattedDate,
      time: formattedTime,
      location: {
        method: inputMethod === "address" ? "address" : "coordinates",
        address: inputMethod === "address" ? address : "",
        latitude: inputMethod === "geolocation" ? parseFloat(latitude) : 0,
        longitude: inputMethod === "geolocation" ? parseFloat(longitude) : 0
      }
    }

    try {
      const response = await fetch('https://weather-0gek.onrender.com/unified/weather', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.text()
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData}`)
      }

      const data = await response.json()
      setWeatherData(data)
    } catch (err) {
      console.error('Fetch error:', err)
      setError(err instanceof Error ? err.message : "Failed to fetch weather data. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6 w-full overflow-x-hidden">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Weather Tool</h1>
        <div className="flex space-x-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>

      <Card className="border border-neutral-200 rounded-xl shadow-sm">
        <CardContent className="p-6">
          <Tabs defaultValue="address">
            <TabsList className="w-full bg-transparent border-b border-neutral-200 rounded-none mb-6">
              <TabsTrigger 
                value="address" 
                className="px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-neutral-900 data-[state=active]:shadow-none"
              >
                <span className="text-sm font-semibold">Address</span>
              </TabsTrigger>
              <TabsTrigger 
                value="geolocation" 
                className="px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-neutral-900 data-[state=active]:shadow-none"
              >
                <span className="text-sm font-semibold">Geolocation</span>
              </TabsTrigger>
            </TabsList>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-700">Date and Time</Label>
                <Input
                  type="datetime-local"
                  value={dateTime}
                  onChange={(e) => setDateTime(e.target.value)}
                  className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300"
                  required
                />
              </div>

              <TabsContent value="address" className="m-0">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-700">Address</Label>
                  <Input
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Enter address"
                    className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300"
                    required
                  />
                </div>
              </TabsContent>

              <TabsContent value="geolocation" className="m-0">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-neutral-700">Latitude</Label>
                    <Input
                      type="number"
                      step="any"
                      value={latitude}
                      onChange={(e) => setLatitude(e.target.value)}
                      placeholder="Enter latitude"
                      className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-neutral-700">Longitude</Label>
                    <Input
                      type="number"
                      step="any"
                      value={longitude}
                      onChange={(e) => setLongitude(e.target.value)}
                      placeholder="Enter longitude"
                      className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300"
                      required
                    />
                  </div>
                </div>
              </TabsContent>

              <div className="flex flex-col space-y-2 md:col-start-3 md:row-start-1">
                <Button 
                  onClick={handleGetWeather} 
                  disabled={isLoading}
                  className="h-11 bg-neutral-900 text-white hover:bg-neutral-800 focus:ring-2 focus:ring-neutral-300"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Getting weather...
                    </>
                  ) : (
                    "Get Weather"
                  )}
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setDateTime("");
                    setAddress("");
                    setLatitude("");
                    setLongitude("");
                    setWeatherData(null);
                    setError(null);
                  }}
                  className="h-11 border-neutral-300 text-neutral-700 hover:bg-neutral-50"
                >
                  Reset Fields
                </Button>
              </div>
            </div>
          </Tabs>

          {error && (
            <Alert variant="destructive" className="mt-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {weatherData && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Weather Information</CardTitle>
                  <CardDescription>{weatherData.summary}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Current Conditions Card */}
                    <Card className="p-4 border border-neutral-200">
                      <h3 className="font-semibold text-lg mb-4">Current Conditions</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-neutral-600">Temperature</span>
                          <span className="text-xl font-medium">{weatherData.weather_data.temperature}°C</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-neutral-600">Wind Speed</span>
                          <span className="text-xl font-medium">{weatherData.weather_data.wind_speed} km/h</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-neutral-600">Precipitation</span>
                          <span className="text-xl font-medium">{weatherData.weather_data.precipitation}mm</span>
                        </div>
                        <div className="mt-4 p-3 bg-neutral-50 rounded-lg">
                          <span className="text-neutral-600">Conditions:</span>
                          <p className="text-lg font-medium mt-1">{weatherData.weather_data.weather_description}</p>
                        </div>
                      </div>
                    </Card>

                    {/* Risk Assessment Card */}
                    <Card className="p-4 border border-neutral-200">
                      <h3 className="font-semibold text-lg mb-4">Risk Assessment</h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-neutral-600">Risk Level</span>
                            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                              weatherData.risk_assessment.overall_risk_score < 0.3 ? 'bg-green-100 text-green-800' :
                              weatherData.risk_assessment.overall_risk_score < 0.6 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {weatherData.risk_assessment.risk_category}
                            </span>
                          </div>
                          <div className="w-full h-3 bg-neutral-200 rounded-full overflow-hidden">
                            <div 
                              className={`h-full rounded-full ${
                                weatherData.risk_assessment.overall_risk_score < 0.3 ? 'bg-green-500' :
                                weatherData.risk_assessment.overall_risk_score < 0.6 ? 'bg-yellow-500' :
                                'bg-red-500'
                              }`}
                              style={{ width: `${weatherData.risk_assessment.overall_risk_score * 100}%` }}
                            />
                          </div>
                          <div className="text-sm text-neutral-500 mt-1">
                            Risk Score: {(weatherData.risk_assessment.overall_risk_score * 100).toFixed(1)}%
                          </div>
                        </div>
                        
                        <div className="p-3 bg-neutral-50 rounded-lg">
                          <span className="text-neutral-600">Recommendation:</span>
                          <p className="text-lg font-medium mt-1">{weatherData.risk_assessment.recommendations}</p>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Hourly Forecast */}
                  <div className="mt-6">
                    <h3 className="font-semibold text-lg mb-4">24-Hour Forecast</h3>
                    <div className="overflow-x-auto">
                      <div className="flex space-x-4 pb-4">
                        {weatherData.weather_data.detailed_forecast.hourly.map((hour, index) => (
                          <div key={index} className="flex-shrink-0 w-24 p-3 border rounded-lg text-center">
                            <div className="text-sm text-neutral-600">
                              {new Date(hour.time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </div>
                            <div className="text-lg font-medium mt-1">{hour.temperature}°C</div>
                            <div className="text-sm text-neutral-500 mt-1">{hour.precipitation}mm</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* AI Analysis */}
                  <div className="mt-6">
                    <h3 className="font-semibold text-lg mb-4">AI Analysis</h3>
                    <Card className="p-4 border border-neutral-200">
                      <p className="text-lg">{weatherData.ai_analysis.weather_summary}</p>
                      {weatherData.ai_analysis.weather_implications.length > 0 && (
                        <ul className="list-disc list-inside mt-4 space-y-2">
                          {weatherData.ai_analysis.weather_implications.map((implication, index) => (
                            <li key={index} className="text-neutral-600">{implication}</li>
                          ))}
                        </ul>
                      )}
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

