import { EarthquakePredictionTool } from "@/components/earthquake-prediction-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

const apiInfo = {
  toolName: "Earthquake Prediction Tool",
  endpoint: "https://pga-curve-715610578807.us-central1.run.app",
  requestExample: {
    target_latitude: -15.02,
    target_longitude: -72.15,
    window: 1,
    Vs30: 760
  },
  responseExample: {
    data: [
      {
        pga_value: 0.1,
        probability_of_exceedance: 85.2
      },
      {
        pga_value: 0.2,
        probability_of_exceedance: 45.8
      },
      {
        pga_value: 0.3,
        probability_of_exceedance: 22.1
      }
    ],
    metadata: {
      target_latitude: -15.02,
      target_longitude: -72.15,
      window: 1,
      vs30: 760
    }
  }
}

const modelInfo = {
  title: "Enhanced Earthquake Prediction Tool",
  description: "Comprehensive seismic risk assessment tool that generates probabilistic earthquake curves and calculates financial risk for insurance underwriting, featuring asset-specific analysis and location intelligence.",
  type: "tool" as const,
  purpose: [
    "Assess seismic risk for insurance underwriting",
    "Calculate financial exposure and expected losses",
    "Generate probabilistic earthquake hazard curves",
    "Evaluate asset-specific damage probabilities",
    "Support property risk assessment and premium calculations"
  ],
  methodology: [
    "Uses seismic hazard models based on geological data",
    "Incorporates site-specific soil conditions (Vs30 values)",
    "Applies probabilistic seismic hazard analysis (PSHA)",
    "Leverages USGS geological survey data when Vs30 not specified",
    "Performs linear interpolation for precise risk calculations",
    "Integrates reverse geocoding for location intelligence"
  ],
  outputs: [
    "Probabilistic curve of PGA vs exceedance probability",
    "Asset-specific financial risk assessment",
    "Expected monetary losses and damage probabilities",
    "Risk level classification (Low/Medium/High)",
    "Interactive visualization with PGA threshold indicators",
    "Location information (city, country) from coordinates",
    "Professional risk assessment summary"
  ]
}

export default function EarthquakePredictionPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Enhanced Earthquake Prediction Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <EarthquakePredictionTool />
    </div>
  )
}
