"use client"

import { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileUploader } from "@/components/file-uploader";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast";
import { Plus, ImageIcon, Loader2, FileText, X } from 'lucide-react';
import { APIDialog } from "@/components/api-dialog";
import { ModelInfoDialog } from "@/components/model-info-dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const apiInfo = {
  toolName: "OCR Tool",
  endpoint: "ocr",
  requestExample: {
    document: "base64_encoded_document",
    templateId: "template123" // Optional template ID
  },
  responseExample: {
    text: "Extracted text from the document...",
    markdown: "Markdown version of the extracted text...",
    templateData: {
      // Extracted data based on the template
      fieldName1: "value1",
      fieldName2: "value2"
    }
  }
}

const modelInfo = {
  title: "OCR Tool",
  description: "This tool extracts text from scanned documents and supports custom templates for structured data extraction.",
  type: "tool" as const,
  purpose: [
    "Convert scanned documents to text",
    "Extract data from templated documents",
    "Automate document processing workflows"
  ],
  methodology: [
    "Uses optical character recognition (OCR) to extract text",
    "Supports various document formats (PDF, images)",
    "Allows creation of custom templates for structured data extraction"
  ],
  outputs: [
    "Extracted text (plain text or Markdown)",
    "Structured data based on template (if provided)",
    "Confidence scores for extracted text"
  ]
}

export default function OCRPage() {
  const [document, setDocument] = useState<File | null>(null);
  const [extractedText, setExtractedText] = useState("");
  const [markdownText, setMarkdownText] = useState("");
  const [isExtracting, setIsExtracting] = useState(false);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const { toast } = useToast();
  const [selectedTemplate, setSelectedTemplate] = useState<string | undefined>(undefined);


  // Define field types
  const fieldTypes = ["bool", "number", "text"]

  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      setDocument(files[0]);
    }
  };

  const handleTemplateChange = (templateId: string | null) => {
    setSelectedTemplate(templateId ?? undefined);
  };

  const handleExtractText = async () => {
    if (!document) return;

    setIsExtracting(true);
    setExtractedText("");
    setMarkdownText("");

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const mockExtractedText = "This is the extracted text from the scanned document. It contains various information, including names, addresses, dates, and other relevant details.";
      const mockMarkdownText = `# Extracted Text

This is the extracted text from the scanned document. It contains various information, including names, addresses, dates, and other relevant details.

**Key Information:**

* Name: John Doe
* Address: 123 Main St, Anytown, USA
* Date: 2023-12-07`;

      setExtractedText(mockExtractedText);
      setMarkdownText(mockMarkdownText);

      if (selectedTemplate) {
        const template = templates.find(t => t.name === selectedTemplate);
        if (template) {
          toast({
            title: "Data Extracted with Template",
            description: `Used template "${selectedTemplate}" to structure extracted data.`,
          });
          // Here you would typically process the extracted text with the selected template
          console.log("Extracted data:", extractDataWithTemplate(mockExtractedText, template.fields));
        }
      } else {
        toast({
          title: "Text Extracted",
          description: "Text extracted successfully from the document.",
        });
      }
    } catch (error) {
      toast({
        title: "Extraction Failed",
        description: "There was an error extracting text from the document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExtracting(false);
    }
  };

  const handleCreateTemplate = () => {
    const newTemplate = {
      name: templateName,
      description: templateDescription,
      fields: templateFields
    }
    setTemplates(prev => [...prev, newTemplate])
    toast({
      title: "Template Created",
      description: `Template "${newTemplate.name}" created successfully.`,
    })
    // Close the dialog and reset the form
    setIsTemplateDialogOpen(false)
    setTemplateName('')
    setTemplateDescription('')
    setTemplateFields([])
  }

  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [templateFields, setTemplateFields] = useState<{ name: string, type: string }[]>([])

  const handleAddField = () => {
    setTemplateFields([...templateFields, { name: '', type: 'text' }])
  }

  const handleRemoveField = (index: number) => {
    setTemplateFields(templateFields.filter((_, i) => i !== index))
  }

  const handleTemplateFieldChange = (index: number, field: 'name' | 'type', value: string) => {
    setTemplateFields(prevFields => prevFields.map((f, i) =>
      i === index ? { ...f, [field]: value } : f
    ))
  }

  // Helper function to simulate data extraction with template
  const extractDataWithTemplate = (text: string, fields: { name: string, type: string }[]) => {
    return fields.reduce((data, field) => {
      // Simple mock extraction - replace with actual logic
      data[field.name] = `Extracted value for ${field.name}`;
      return data;
    }, {} as Record<string, string>);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">OCR Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Document Upload</CardTitle>
          <CardDescription>Upload a scanned document (PDF or image) for text extraction</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <FileUploader
              id="document-upload"
              accept=".pdf,.png,.jpg,.jpeg"
              onFilesSelected={handleFileUpload}
            />
            <Select
              onValueChange={handleTemplateChange}
              value={selectedTemplate}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a template (optional)" />
              </SelectTrigger>
              <SelectContent>
                {templates.map((template) => (
                  <SelectItem key={template.name} value={template.name}>
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button onClick={handleExtractText} disabled={isExtracting || !document}>
              {isExtracting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Extracting...
                </>
              ) : (
                "Extract Text"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {extractedText && (
        <Card>
          <CardHeader>
            <CardTitle>Extracted Text</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="text">
              <TabsList>
                <TabsTrigger value="text">Text</TabsTrigger>
                <TabsTrigger value="markdown">Markdown</TabsTrigger>
              </TabsList>
              <TabsContent value="text">
                <Textarea
                  value={extractedText}
                  readOnly
                  className="h-64"
                />
              </TabsContent>
              <TabsContent value="markdown">
                <Textarea
                  value={markdownText}
                  readOnly
                  className="h-64"
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-between items-center mb-4 mt-8">
        <h2 className="text-2xl font-semibold">OCR Templates</h2>
        <Button onClick={() => setIsTemplateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Template Dialog */}
      <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
        <DialogContent className="sm:max-w-xl">
          <DialogHeader>
            <DialogTitle>Create OCR Template</DialogTitle>
            <DialogDescription>
              Define a template to extract structured data from documents.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="template-name">Template Name</Label>
              <Input
                id="template-name"
                placeholder="Enter template name"
                value={templateName}
                onChange={e => setTemplateName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-description">Description</Label>
              <Textarea
                id="template-description"
                placeholder="Describe the template"
                value={templateDescription}
                onChange={e => setTemplateDescription(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="fields">Fields to Extract</Label>
              <div className="space-y-2">
                {templateFields.map((field, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    <Input
                      placeholder="Field Name"
                      value={field.name}
                      onChange={e => handleTemplateFieldChange(index, 'name', e.target.value)}
                      className="w-1/3"
                    />
                    <div className="w-1/3">
                    <Select
                      value={field.type}
                      onValueChange={value => handleTemplateFieldChange(index, 'type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Field Type" />
                      </SelectTrigger>
                      <SelectContent>
                        {fieldTypes.map(type => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveField(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={handleAddField}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Field
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleCreateTemplate}>Create Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Display Templates */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {templates.map((template, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle>{template.name}</CardTitle>
              <CardDescription>{template.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted rounded-md p-4 overflow-x-auto text-sm">
                <code>{JSON.stringify(template.fields, null, 2)}</code>
              </pre>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

