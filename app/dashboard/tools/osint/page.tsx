"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { OSINTResultCard } from "@/components/osint-result-card"
import { Twitter, Instagram, Linkedin, Github, LucideIcon } from 'lucide-react'
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

const entityTypes = ["nickname", "email"]

interface OSINTResult {
platform: string
nickname: string
url: string
icon: LucideIcon
}

const apiInfo = {
toolName: "OSINT Tool",
endpoint: "osint",
requestExample: {
searchTerm: "johndoe",
entityType: "nickname"
},
responseExample: [
// Example OSINT results
]
}

export default function OSINTPage() {
const [inputText, setInputText] = useState('')
const [entityType, setEntityType] = useState<string>('')
const [isLoading, setIsLoading] = useState(false)
const [results, setResults] = useState<OSINTResult[]>([])

const handleSearch = async () => {
setIsLoading(true)
setResults([]) // Clear previous results

// Simulate API call
await new Promise(resolve => setTimeout(resolve, 1500))

// Simulate search results
const simulatedResults: OSINTResult[] = [
{ platform: "Twitter", nickname: inputText, url: `https://twitter.com/${inputText}`, icon: Twitter },
{ platform: "Instagram", nickname: inputText, url: `https://instagram.com/${inputText}`, icon: Instagram },
{ platform: "LinkedIn", nickname: inputText, url: `https://linkedin.com/in/${inputText}`, icon: Linkedin },
{ platform: "GitHub", nickname: inputText, url: `https://github.com/${inputText}`, icon: Github },
]

setResults(simulatedResults)
setIsLoading(false)
}

const modelInfo = {
title: "OSINT Search Tool",
description: "This tool performs open-source intelligence (OSINT) searches to gather information about individuals or entities.",
type: "tool" as const,
purpose: [
"Gather information from publicly available sources",
"Identify social media presence and online activity",
"Support investigations and due diligence"
],
methodology: [
"Searches various online platforms and databases",
"Collects and aggregates relevant information",
"Provides links to identified profiles and sources"
],
outputs: [
"List of identified online profiles",
"Links to relevant sources",
"Summary of gathered information"
]
}

return (
<div className="space-y-6">
<div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">OSINT Search</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
<Card>
<CardHeader>
<CardTitle>Search Parameters</CardTitle>
</CardHeader>
<CardContent className="space-y-4">
<div className="space-y-2">
<label htmlFor="osintInput" className="text-sm font-medium">
  Enter Search Term
</label>
<Input
  id="osintInput"
  placeholder="Enter nickname or email"
  value={inputText}
  onChange={(e) => setInputText(e.target.value)}
/>
</div>

<div className="space-y-2">
<label htmlFor="entityType" className="text-sm font-medium">
  Select Entity Type
</label>
<Select value={entityType} onValueChange={setEntityType}>
  <SelectTrigger id="entityType">
    <SelectValue placeholder="Select entity type" />
  </SelectTrigger>
  <SelectContent>
    {entityTypes.map((type) => (
      <SelectItem key={type} value={type}>{type}</SelectItem>
    ))}
  </SelectContent>
</Select>
</div>

<Button 
onClick={handleSearch}
disabled={!inputText || !entityType || isLoading}
>
{isLoading ? 'Searching...' : 'Search'}
</Button>
</CardContent>
</Card>

{results.length > 0 && (
<Card>
<CardHeader>
<CardTitle>Search Results</CardTitle>
</CardHeader>
<CardContent>
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
  {results.map((result, index) => (
    <OSINTResultCard
      key={index}
      platform={result.platform}
      nickname={result.nickname}
      url={result.url}
      icon={result.icon}
    />
  ))}
</div>
</CardContent>
</Card>
)}
</div>
)
}

