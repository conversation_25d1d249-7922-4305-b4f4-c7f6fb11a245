import { AppraisalAuditTool } from "@/components/appraisal-audit-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

export default function AppraisalAuditPage() {
  const apiInfo = {
    toolName: "Appraisal Audit Tool",
    endpoint: "appraisal-audit",
    requestExample: {
      appraisalId: "APR-2023-001",
      appraisalType: "auto", // or "home"
      additionalFiles: ["base64_encoded_file_1", "base64_encoded_file_2"]
    },
    responseExample: {
      issuesDetected: [
        { issue: "Overcharging for labor", severity: "High", details: "Labor cost exceeds industry average by 20%" },
        { issue: "Unnecessary part replacement", severity: "Medium", details: "The bumper could have been repaired instead of replaced" },
        { issue: "Missing documentation", severity: "Low", details: "Photos of the damaged parts are missing" }
      ],
      overallScore: 65,
      recommendations: [
        "Request revised estimate from repair shop",
        "Review documentation for completeness"
      ]
    }
  }

  const modelInfo = {
    title: "Appraisal Audit Tool",
    description: "This tool analyzes appraisals for potential issues like overcharging, excessive labor hours, and unnecessary part replacements.",
    type: "tool" as const,
    purpose: [
      "Identify potential issues in appraisals",
      "Ensure fair and accurate pricing",
      "Improve appraisal quality and consistency"
    ],
    methodology: [
      "Compares appraisal data with industry benchmarks and historical data",
      "Uses machine learning models to detect anomalies and patterns",
      "Considers repair vs. replace decisions based on damage severity"
    ],
    outputs: [
      "List of detected issues with severity levels",
      "Overall appraisal score",
      "Actionable recommendations"
    ]
  }

  return (
    <div className="container mx-auto py-6">
<div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Appraisal Audit Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <AppraisalAuditTool />
    </div>
  )
}

