"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { log, sendLogToBackend } from '@/lib/logger'
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"

// Add these types to handle the new response format
interface CoverageDetails {
  applicable_coverage: string;
  coverage_limits: string;
  deductible_amount: number;
  exclusions: string[];
}

const apiInfo = {
  toolName: "FNOL Speech-to-Text Tool",
  endpoint: "fnol-speech-to-text",
  requestExample: {
    audioFile: "base64_encoded_audio"
  },
  responseExample: {
    transcription: "On July 15th, I was involved in a car accident...",
    claimDetails: {
      date: "2023-07-15",
      location: "Intersection of Main St and 5th Ave, New York, NY",
      vehiclesInvolved: 2,
      injuries: "Minor"
    },
    detectedFlags: [
      { flag: "Potential Fraud", confidence: 0.15 },
      { flag: "Potential Litigation", confidence: 0.35 },
      { flag: "Unhappy Customer", confidence: 0.75 }
    ]
  }
}

interface RequiredDocumentation {
  essential_documents: string[];
  optional_supporting_evidence: string[];
  submission_instructions: string;
}

interface SimilarClaim {
  claim_id: string;
  scenario: string;
  outcome: string;
  key_factors: string[];
  url: string;
}

interface PolicyImpact {
  premium_effect: string;
  coverage_changes: string;
  renewal_considerations: string;
}

interface GlossaryItem {
  term: string;
  definition: string;
}

interface ApiResponse {
  accept: boolean;
  reason: string;
  next_steps: string[];
  context?: string[];
  confidence_score: number;
  coverage_details: CoverageDetails;
  required_documentation: RequiredDocumentation;
  alternative_options?: any[];
  similar_claims?: SimilarClaim[];
  policy_impact: PolicyImpact;
  glossary?: GlossaryItem[];
  message_to_insured: string;
}

// Add utility function to convert file to base64 format
const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

// Add a function to copy text to clipboard
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    console.log('Copied to clipboard');
  }).catch(err => {
    console.error('Failed to copy to clipboard', err);
  });
};

export default function ClaimAcceptancePage() {
  const { data: session } = useSession();
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ApiResponse | null>(null);
  const [isContextOpen, setIsContextOpen] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setResult(null);

    if (!inputText.trim() && !pdfFile) {
      setError("Please enter a claim description or upload a PDF file.");
      setIsLoading(false);
      return;
    }

    try {
      const formData = new FormData();
      if (pdfFile) {
        // Validate file type
        if (!pdfFile.type || !pdfFile.type.includes('pdf')) {
          throw new Error("Please upload a valid PDF file.");
        }
        formData.append("pdf_file", pdfFile);
      }
      formData.append("claim_description", inputText);

      // Fetch user data by ID
      const userId = session?.user?.id;
      if (!userId) {
        throw new Error("User ID is missing. Please log in.");
      }

      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        method: "GET",
        headers: { accept: "application/json" },
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();

      // Log user access check
      const accessLogData = {
        action: 'user_access_checked',
        user_id: session?.user?.id,
        level: 'info',
        component: 'claim-acceptance',
        details: {
          userRole: userData.role,
          hasAccess: userData.role === 'admin' || userData.tools?.claim_acceptance,
          timestamp: new Date().toISOString()
        }
      };

      log.info(accessLogData);
      try {
        await sendLogToBackend(accessLogData);
      } catch (error) {
        console.log(error)
      }

      // Check if user is admin or has access to the tool
      if (userData.role !== "admin") {
        const { tools } = userData;
        if (!tools.claim_acceptance) {
          throw new Error("You do not have access to the claim acceptance tool.");
        }
      }

      // Call the claim acceptance API
      const apiUrl = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claim-acceptance/process/?user_id=${userId}&external_api_id=67b9a897383420fe8690dfca`;
      console.log("Calling claim acceptance API:", apiUrl);

      const response = await fetch(apiUrl, {
        method: "POST",
        body: formData,
        headers: {
          'Accept': 'application/json',
          // Removed Content-Type header to let the browser set it with the proper boundary for multipart/form-data
        }
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log("API response data:", data);

      // Extract the actual result data from the nested structure
      if (data.message === "Success" && data.data) {
        setResult(data.data);
      } else {
        throw new Error("Invalid response format from API");
      }

    } catch (err) {
      console.error("Full error object:", err);

      // Try to extract more information from the error
      let errorMessage = "An unknown error occurred";
      if (err instanceof Error) {
        errorMessage = err.message;
        console.error("Error name:", err.name);
        console.error("Error message:", err.message);
        console.error("Error stack:", err.stack);
      } else if (typeof err === 'object' && err !== null) {
        errorMessage = JSON.stringify(err);
      }

      setError(`Error: ${errorMessage}`);

      // Log error
      console.error('Claim evaluation error', {
        userId: session?.user?.id,
        error: errorMessage,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const modelInfo = {
    title: "Claim Acceptance Tool",
    description: "This tool evaluates claim PDFs and additional information to determine if a claim should be accepted or rejected.",
    type: "tool" as const,
    purpose: [
      "Automate initial claim evaluation",
      "Reduce manual review workload",
      "Improve claim processing efficiency"
    ],
    methodology: [
      "Extracts text from uploaded PDF using OCR",
      "Analyzes claim details and additional information",
      "Applies predefined rules and AI models to assess claim validity",
      "Provides an acceptance recommendation with justification"
    ],
    outputs: [
      "Acceptance Status (true/false)",
      "Observation and Justification"
    ]
  }

  // Add print styles when component mounts
  useEffect(() => {
    // Create a style element for print media
    const style = document.createElement('style');
    style.innerHTML = `
      @media print {
        /* Hide everything except the result container */
        body > *:not(.print-container) {
          display: none !important;
        }
        
        /* Make sure the result container is visible and takes full width */
        .print-container {
          display: block !important;
          width: 100% !important;
          max-width: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
        }
        
        /* Hide any buttons or non-essential elements within the result */
        .no-print {
          display: none !important;
        }
        
        /* Ensure proper page breaks */
        .page-break-inside-avoid {
          page-break-inside: avoid;
        }
        
        /* Reset any fixed positioning */
        .print-container * {
          position: static !important;
        }
      }
    `;
    document.head.appendChild(style);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Create a printable version of the result
  const printResult = () => {
    if (!result) return;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print the report');
      return;
    }

    // Generate HTML content for the print window
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Claim Acceptance Report</title>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              line-height: 1.5;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 10px;
              border-bottom: 1px solid #e5e7eb;
            }
            .decision-banner {
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 20px;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .accept {
              background-color: #d1fae5;
              border: 1px solid #6ee7b7;
            }
            .reject {
              background-color: #fee2e2;
              border: 1px solid #fca5a5;
            }
            .confidence {
              background-color: white;
              border-radius: 20px;
              padding: 5px 10px;
              font-size: 14px;
            }
            .section {
              margin-bottom: 25px;
              page-break-inside: avoid;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .card {
              background-color: white;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              padding: 15px;
              margin-bottom: 15px;
            }
            .message-box {
              background-color: #eff6ff;
              border: 1px solid #bfdbfe;
              border-radius: 8px;
              padding: 15px;
            }
            .message-content {
              background-color: white;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              padding: 15px;
              white-space: pre-line;
            }
            .grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 15px;
            }
            .list {
              padding-left: 20px;
            }
            .footer {
              margin-top: 40px;
              text-align: center;
              font-size: 12px;
              color: #6b7280;
              border-top: 1px solid #e5e7eb;
              padding-top: 20px;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Claim Acceptance Report</h1>
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          </div>
          
          <div class="decision-banner ${result.accept ? 'accept' : 'reject'}">
            <div>
              <h2 style="font-size: 20px; font-weight: bold; margin: 0;">
                ${result.accept ? "Claim Accepted" : "Claim Rejected"}
              </h2>
            </div>
            <div class="confidence">
              Confidence: ${(result.confidence_score * 100).toFixed(0)}%
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">Decision Reason</div>
            <p>${result.reason}</p>
          </div>
          
          <div class="section">
            <div class="section-title">Next Steps</div>
            // <ul class="list">
            //    ${result.next_steps.map(step => `<li>${step}</li>`).join('')}
            // </ul>
          </div>
          
          <div class="section message-box">
            <div class="section-title">Message to Customer</div>
            <div class="message-content">${result.message_to_insured.replace(/\n/g, '<br>')}</div>
          </div>
          
          <div class="section">
            <div class="section-title">Coverage Details</div>
            <div><strong>Applicable Coverage:</strong> ${result.coverage_details.applicable_coverage}</div>
            <div><strong>Coverage Limits:</strong> ${result.coverage_details.coverage_limits}</div>
            <div><strong>Deductible Amount:</strong> $${result.coverage_details.deductible_amount}</div>
            <div>
              <strong>Exclusions:</strong>
              <ul class="list">
                ${result.coverage_details.exclusions.map(item => `<li>${item}</li>`).join('')}
              </ul>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">Required Documentation</div>
            <div><strong>Essential Documents:</strong>
              <ul class="list">
                ${result.required_documentation.essential_documents.map(item => `<li>${item}</li>`).join('')}
              </ul>
            </div>
            <div><strong>Optional Supporting Evidence:</strong>
              <ul class="list">
                ${result.required_documentation.optional_supporting_evidence.map(item => `<li>${item}</li>`).join('')}
              </ul>
            </div>
            <p><strong>Submission Instructions:</strong> ${result.required_documentation.submission_instructions}</p>
          </div>
          
          <div class="section">
            <div class="section-title">Policy Impact</div>
            <p><strong>Premium Effect:</strong> ${result.policy_impact.premium_effect}</p>
            <p><strong>Coverage Changes:</strong> ${result.policy_impact.coverage_changes}</p>
            <p><strong>Renewal Considerations:</strong> ${result.policy_impact.renewal_considerations}</p>
          </div>
          
          ${result.similar_claims && result.similar_claims.length > 0 ? `
          <div class="section">
            <div class="section-title">Similar Claims</div>
            ${result.similar_claims.map(claim => `
              <div class="card">
                <p><strong>${claim.claim_id}</strong></p>
                <p>${claim.scenario}</p>
                <p><strong>Outcome:</strong> ${claim.outcome}</p>
                <div>
                  <p><strong>Key Factors:</strong></p>
                  <ul class="list">
                    ${claim.key_factors.map(factor => `<li>${factor}</li>`).join('')}
                  </ul>
                </div>
              </div>
            `).join('')}
          </div>
          ` : ''}
          
          <div class="footer">
            <p>This is an automated assessment. Final determination may require additional review.</p>
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for content to load before printing
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
    }, 500);
  };

  return (
    <div className="container mx-auto px-4 py-8">

<div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Claim Acceptance Evaluation</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="claim-description" className="block text-sm font-medium text-gray-700 mb-1">
              Claim Description
            </label>
            <textarea
              id="claim-description"
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter the claim description here..."
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
            ></textarea>
          </div>

          <div className="mb-4">
            <label htmlFor="pdf-upload" className="block text-sm font-medium text-gray-700 mb-1">
              Upload PDF (Optional)
            </label>
            <input
              type="file"
              id="pdf-upload"
              accept=".pdf"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              onChange={(e) => {
                const files = e.target.files;
                if (files && files.length > 0) {
                  setPdfFile(files[0]);
                }
              }}
            />
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isLoading}
              className={`px-4 py-2 rounded-md text-white font-medium ${isLoading ? "bg-gray-400" : "bg-blue-600 hover:bg-blue-700"
                }`}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                "Evaluate Claim"
              )}
            </button>
          </div>
        </form>

        {error && (
          <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {result && (
          <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
            {/* Accept/Reject Banner */}
            <div className={`p-4 mb-6 rounded-lg flex items-center justify-between ${result.accept ? "bg-green-100 border border-green-300" : "bg-red-100 border border-red-300"
              }`}>
              <div className="flex items-center">
                {result.accept ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
                <h2 className="text-xl font-bold">
                  {result.accept ? "Claim Accepted" : "Claim Rejected"}
                </h2>
              </div>
              <div className="bg-white rounded-full px-3 py-1 text-sm font-medium">
                Confidence: {(result.confidence_score * 100).toFixed(0)}%
              </div>
            </div>

            {/* Reason */}
            <div className="mb-6">
              <h3 className="font-bold text-lg mb-2">Decision Reason</h3>
              <p className="text-gray-700">{result.reason}</p>
            </div>

            {/* Next Steps */}
            <div className="mb-6">
              <h3 className="font-bold text-lg mb-2">Next Steps</h3>
              <ul className="list-disc pl-5 space-y-1">
                {result.next_steps.map((step, index) => (
                  <li key={index} className="text-gray-700">{step}</li>
                ))}
              </ul>
            </div>

            {/* Message to Insured */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="font-bold text-lg mb-2 flex items-center justify-between">
                <span>Message to Customer</span>
                <button
                  onClick={() => copyToClipboard(result.message_to_insured)}
                  className="bg-blue-500 hover:bg-blue-600 text-white text-sm px-2 py-1 rounded flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                  Copy
                </button>
              </h3>
              <div className="bg-white p-4 rounded border border-blue-200 whitespace-pre-line">
                {result.message_to_insured}
              </div>
            </div>

            {/* Coverage Details */}
            <div className="mt-6 p-4 rounded-lg border border-gray-200 page-break-inside-avoid">
              <h3 className="font-bold text-lg mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                Coverage Details
              </h3>
              <div className="space-y-3">
                <p><span className="font-semibold">Applicable Coverage:</span> {result.coverage_details.applicable_coverage}</p>
                <p><span className="font-semibold">Coverage Limits:</span> {result.coverage_details.coverage_limits}</p>
                <p><span className="font-semibold">Deductible Amount:</span> ${result.coverage_details.deductible_amount}</p>
                <div>
                  <p className="font-semibold">Exclusions:</p>
                  <ul className="list-disc pl-5">
                    {result.coverage_details.exclusions.map((exclusion, index) => (
                      <li key={index} className="text-gray-700">{exclusion}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Required Documentation */}
            <div className="mt-6 p-4 rounded-lg border border-gray-200 page-break-inside-avoid">
              <h3 className="font-bold text-lg mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                </svg>
                Required Documentation
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Essential Documents:</h4>
                  <ul className="list-disc pl-5">
                    {result.required_documentation.essential_documents.map((doc, index) => (
                      <li key={index} className="text-gray-700">{doc}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Supporting Evidence (Optional):</h4>
                  <ul className="list-disc pl-5">
                    {result.required_documentation.optional_supporting_evidence.map((doc, index) => (
                      <li key={index} className="text-gray-700">{doc}</li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="mt-3 p-3 bg-white rounded border border-gray-200">
                <p className="text-sm">{result.required_documentation.submission_instructions}</p>
              </div>
            </div>

            {/* Policy Impact */}
            <div className="mt-6 p-4 rounded-lg border border-gray-200 page-break-inside-avoid">
              <h3 className="font-bold text-lg mb-3 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                  <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                </svg>
                Policy Impact
              </h3>
              <div className="space-y-3">
                <p><span className="font-semibold">Premium Effect:</span> {result.policy_impact.premium_effect}</p>
                <p><span className="font-semibold">Coverage Changes:</span> {result.policy_impact.coverage_changes}</p>
                <p><span className="font-semibold">Renewal Considerations:</span> {result.policy_impact.renewal_considerations}</p>
              </div>
            </div>

            {/* Similar Claims */}
            {result.similar_claims && result.similar_claims.length > 0 && (
              <div className="mt-6 p-4 rounded-lg border border-gray-200 page-break-inside-avoid">
                <h3 className="font-bold text-lg mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                  </svg>
                  Similar Claims
                </h3>
                <div className="space-y-4">
                  {result.similar_claims.map((claim, index) => (
                    <div key={index} className="bg-white p-3 rounded border border-gray-200">
                      <p className="font-semibold text-gray-700">{claim.claim_id}</p>
                      <p className="text-sm mb-2">{claim.scenario}</p>
                      <p className="text-sm mb-2"><span className="font-semibold">Outcome:</span> {claim.outcome}</p>
                      <div className="text-sm">
                        <p className="font-semibold">Key Factors:</p>
                        <ul className="list-disc pl-5">
                          {claim.key_factors.map((factor, idx) => (
                            <li key={idx}>{factor}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Context/Policy Excerpts (Collapsible) */}
            {result.context && result.context.length > 0 && (
              <div className="mt-6">
                <button
                  onClick={() => setIsContextOpen(!isContextOpen)}
                  className="flex w-full justify-between rounded-lg bg-gray-100 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none"
                >
                  <span className="font-bold">View Policy Excerpts</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`${isContextOpen ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                {isContextOpen && (
                  <div className="px-4 pt-4 pb-2 text-sm text-gray-500 max-h-96 overflow-auto bg-gray-50 rounded-lg mt-2">
                    {result.context.map((excerpt, index) => (
                      <div key={index} className="mb-4 p-3 bg-white rounded border border-gray-200">
                        <pre className="whitespace-pre-wrap font-sans text-xs">{excerpt}</pre>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-6 flex justify-end space-x-3">
              <button
                className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex items-center"
                onClick={printResult}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd" />
                </svg>
                Print Report
              </button>
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex items-center"
                onClick={() => {
                  const jsonStr = JSON.stringify(result, null, 2);
                  copyToClipboard(jsonStr);
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                  <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                </svg>
                Copy JSON
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}