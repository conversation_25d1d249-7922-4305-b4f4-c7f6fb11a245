import { CommercialDueDiligenceTool } from "@/components/commercial-due-diligence-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

export default function CommercialDueDiligencePage() {
  const apiInfo = {
    toolName: "Commercial Due Diligence Tool",
    endpoint: "commercial-due-diligence",
    requestExample: {
      vatNumber: "GB123456789",
      additionalFiles: ["base64_encoded_file_1", "base64_encoded_file_2"]
    },
    responseExample: {
      companyName: "Example Corp Ltd",
      summary: "Example Corp Ltd shows strong financial health with moderate risk factors...",
      financialHealthScore: 85,
      riskScore: 35,
      keyFindings: [
        "Consistent revenue growth over the past 3 years",
        "Debt-to-equity ratio within industry norms",
        "Potential supply chain vulnerabilities identified"
      ],
      recommendations: [
        "Consider diversifying supplier base",
        "Explore opportunities for cost optimization"
      ]
    }
  }

  const modelInfo = {
    title: "Commercial Due Diligence Tool",
    description: "This tool performs a comprehensive due diligence analysis on a company based on its VAT number and additional provided documents.",
    type: "tool" as const,
    purpose: [
      "Assess company's financial health",
      "Identify potential risks and opportunities",
      "Provide actionable insights for decision-making"
    ],
    methodology: [
      "Analyzes company financial statements and reports",
      "Utilizes machine learning models for risk assessment",
      "Compares key metrics against industry benchmarks",
      "Incorporates natural language processing for document analysis"
    ],
    outputs: [
      "Comprehensive due diligence report",
      "Financial health score",
      "Risk assessment score",
      "Key findings and recommendations"
    ]
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Commercial Due Diligence Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <CommercialDueDiligenceTool />
    </div>
  )
}

