"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { VehicleSpecsForm } from "@/components/vehicle-specs-form"
import { VehicleSpecsResults } from "@/components/vehicle-specs-results"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"

export default function VehicleSpecsPage() {
  const [vehicleSpecs, setVehicleSpecs] = useState<any>(null);

  const handleSubmit = (formData: { licensePlate: string }) => {
    // Mock results
    setVehicleSpecs({
      brand: "Toyota",
      model: "Corolla",
      fuelType: "Gasoline",
      vin: "1NXBR32E35Z123456",
      version: "LE",
      hp: 132,
      kw: 98,
      cilindrada: 1798,
      segment: "Compact"
    });
  };

  const modelInfo = {
    title: "Vehicle Specs Model",
    description: "A model that retrieves detailed vehicle specifications based on the license plate.",
    type: "lookup" as const,
    accuracy: 99,
    purpose: [
      "Identify vehicle specifications for insurance purposes",
      "Assist in vehicle valuation and assessment",
      "Support regulatory compliance and vehicle registration processes",
      "Aid in maintenance and repair planning"
    ],
    methodology: [
      "Queries comprehensive vehicle registration databases",
      "Cross-references license plate with manufacturer specifications",
      "Utilizes VIN decoding for additional vehicle details",
      "Ensures data accuracy through multiple source verification"
    ],
    outputs: [
      "Brand",
      "Model",
      "Fuel Type",
      "VIN (Vehicle Identification Number)",
      "Version",
      "Horsepower (HP)",
      "Kilowatts (kW)",
      "Engine Displacement (Cilindrada)",
      "Vehicle Segment"
    ]
  };

  const apiInfo = {
    modelName: "Vehicle Specs Model",
    endpoint: "vehicle-specs",
    requestExample: {
      license_plate: "ABC123"
    },
    responseExample: {
      brand: "Toyota",
      model: "Corolla",
      fuel_type: "Gasoline",
      vin: "1NXBR32E35Z123456",
      version: "LE",
      hp: 132,
      kw: 98,
      cilindrada: 1798,
      segment: "Compact",
      processing_time: "0.05s"
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Vehicle Specs Model</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Information</CardTitle>
            <CardDescription>Enter the license plate to retrieve vehicle specifications</CardDescription>
          </CardHeader>
          <CardContent>
            <VehicleSpecsForm onSubmit={handleSubmit} />
          </CardContent>
        </Card>
        {vehicleSpecs && (
          <VehicleSpecsResults specs={vehicleSpecs} />
        )}
      </div>
    </div>
  )
}

