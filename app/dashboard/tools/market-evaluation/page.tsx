"use client";

import React, { useState, useMemo } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, FileDown } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { VehicleCard } from "@/components/vehicle-card";
import { PriceStatsInfographic } from "@/components/price-stats-infographic";
import { AiNotes } from "@/components/ai-notes";
import { PriceVsMileagePlot } from "@/components/charts/price-vs-mileage-plot";
import { PriceVsYearPlot } from "@/components/charts/price-vs-year-plot";
import { SearchSummary } from "@/components/search-summary";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { APIDialog } from "@/components/api-dialog";
import { ModelInfoDialog } from "@/components/model-info-dialog";
import { FeedbackDialog } from "@/components/feedback-dialog";
import { useSession } from "next-auth/react";
import { log, sendLogToBackend } from '@/lib/logger'

// Mock data for dropdowns (unchanged)
// const carBrands = ["Toyota", "Honda", "Ford", "Chevrolet", "BMW"];
// const carModels = {
//   Toyota: ["Corolla", "Camry", "RAV4", "Prius", "Yaris"],
//   Honda: ["Civic", "Accord", "CR-V", "Pilot"],
//   Ford: ["F-150", "Mustang", "Explorer", "Focus"],
//   Chevrolet: ["Silverado", "Malibu", "Equinox", "Camaro"],
//   BMW: ["3 Series", "5 Series", "X3", "X5"],
// };

const carBrands = ['AUDI', 'BMW', 'CITROEN', 'FORD', 'HYUNDAI', 'KIA', 'MERCEDES',
       'NISSAN', 'OPEL', 'PEUGEOT', 'RENAULT', 'SEAT', 'TOYOTA', 'VOLKSWAGEN',
       'ABARTH', 'ALFAROMEO', 'CHEVROLET', 'CUPRA', 'DACIA', 'DS', 'FIAT',
       'HONDA', 'JAGUAR', 'JEEP', 'LANDROVER', 'LEXUS', 'MAZDA', 'MG', 'MINI',
       'MITSUBISHI', 'PORSCHE', 'SKODA', 'SUZUKI', 'TESLA', 'VOLVO'];
const carModels = {
AUDI: ["100", "200", "80", "90", "A1", "A2", "A3", "A4", "A4 Allroad Quattro", "A5", "A6", "A6 allroad quattro", "A7", "A8", "Allroad Quattro", "Cabriolet", "Coupe", "e-tron", "e-tron GT", "e-tron Sportback", "Q2", "Q3", "Q3 Sportback", "Q4 e-tron", "Q4 Sportback e-tron", "Q5", "Q5 SPORTBACK", "Q6 e-tron", "Q7", "Q8", "Q8 e-tron", "Q8 e-tron Sportback", "Q8 Sportback e-tron", "R8", "RS2", "RS3", "RS4", "RS5", "RS6", "RS7", "RS Q3", "S1", "S2", "S3", "S4", "S5", "S6", "S7", "S8", "SQ5", "SQ7", "TT", "TT RS", "TTS", "V8"],

BMW: ["Compact", "i3", "i4", "i5", "i7", "i8", "iX", "iX1", "ix2", "iX3", "Serie 1", "Serie 2", "Serie 2 Active Tourer", "Serie 2 Gran Tourer", "Serie 3", "Serie 4", "Serie 5", "Serie 6", "Serie 7", "Serie 8", "X1", "X2", "X3", "X4", "X5", "X6", "X7", "XM", "Z1", "Z3", "Z4", "Z8"],

CITROEN : ["2CV", "AMI", "AX", "Berlingo", "Berlingo First", "BX", "C1", "C15", "C2", "C25", "C3", "C3 Aircross", "C3 Origin", "C3 Picasso", "C3 Pluriel", "C4", "C4 Aircross", "C4 Cactus", "C4 Picasso", "C4 Sedán", "C4 Spacetourer", "C4 X", "C5", "C5 Aircross", "C5 Aircross Hybrid", "C5 X", "C6", "C8", "C-Crosser", "C-Elysée", "CX", "C-Zero", "DS3", "DS4", "DS5", "ë-Berlingo", "ë-C3", "ë-C3 Aircross", "ë-C4", "ëC4 X", "Ë-Jumper", "ë-Jumpy", "E-Mehari", "ë-Spacetourer", "Evasion", "Grand C4 Picasso", "Grand C4 Spacetourer", "GSA", "Jumper", "Jumpy", "LNA", "Nemo", "Saxo", "Spacetourer", "Visa", "Xantia", "XM", "Xsara", "Xsara Picasso", "ZX"],

FORD : ["B-MAX", "Bronco", "Capri", "C-Max", "Connect", "Cougar", "Courier", "EcoSport", "Edge", "Escort", "Explorer", "Fiesta", "Fiesta Van", "Focus", "Focus C-MAX", "Fusion", "Galaxy", "Granada", "Grand C-Max", "Grand Tourneo Connect", "Grand Tourneo Custom", "Ka+", "KA", "Kuga", "Maverick", "Mondeo", "Mustang", "Mustang Mach-E", "Orion", "Probe", "Puma", "Ranger", "Scorpio", "Sierra", "S-MAX", "StreetKa", "Tourneo Connect", "Tourneo Courier", "Tourneo Custom", "Transit", "Transit Connect", "Transit Courier", "Transit Custom"],

HYUNDAI : ["Accent", "Atos", "Atos Prime", "Bayon", "Coupe", "Elantra", "Genesis", "Getz", "Grandeur", "Grand Santa Fe", "H-1", "H100", "H-1 Travel", "H-1 Van", "H350", "i10", "i20", "i20 Active", "i30", "i40", "i800", "IONIQ", "IONIQ 5", "IONIQ 6", "ix20", "ix35", "ix55", "Kona", "Lantra", "Matrix", "Nexo", "Pony", "Santa Fe", "Scoupe", "Sonata", "Sonata FL", "Staria", "Terracan", "Trajet", "TUCSON", "Veloster", "XG"],

KIA : ["Besta", "Carens", "Carnival", "Ceed", "cee'd Sportswagon", "cee'd Sporty Wagon", "Ceed Tourer", "Cerato", "Clarus", "e-Niro", "e-Soul", "EV6", "EV9", "Joice", "k2500", "K2500 Frontier", "Magentis", "Niro", "Niro Hïbrido Enchufable", "Niro PHEV", "Opirus", "Optima", "Optima GT", "Optima Hïbrido Enchufable", "Optima PHEV", "Optima SW", "Optima SW GT", "Optima SW Hïbrido Enchufable", "Optima SW PHEV", "Picanto", "Pregio", "Pride", "pro_cee'd", "ProCeed", "pro_cee'd GT", "Rio", "Sephia", "Sephia II", "Shuma", "Sorento", "Soul", "Soul EV", "Sportage", "Stinger", "Stonic", "Venga", "XCeed"],

MERCEDES : ["100D", "140D", "180D", "190", "200", "208D", "210D", "220", "230", "260", "280", "300", "308D", "310D", "400", "420", "500", "600", "AMG GT", "Citan", "CLA", "Clase A", "Clase B", "Clase C", "Clase CL", "Clase CLA", "Clase CLC", "Clase CLK", "Clase CLS", "Clase E", "Clase G", "Clase GL", "Clase GLA", "Clase GLC", "Clase GLE", "Clase GLE Coupé", "Clase GLK", "Clase GLS", "Clase M", "Clase R", "Clase S", "Clase SL", "Clase SLC", "Clase SLK", "Clase SL R129", "Clase T", "Clase V", "Clase X", "CLE", "CLS", "EQA", "EQB", "EQC", "EQE", "EQE SUV", "EQS", "EQS SUV", "EQT", "EQV", "GLA", "GLB", "GLC", "GLC Coupé", "GLE", "GLE Coupé", "GLS", "Mercedes-AMG GT", "Mercedes-AMG SL", "SL-Klasse", "SLR McLaren", "SLS AMG", "Sprinter", "Transporter", "Vaneo", "Viano", "Vito"],

NISSAN : ["100 NX", "200 SX", "300 ZX", "350Z", "370Z", "Almera", "Almera Tino", "Ariya", "Bluebird", "Cabstar", "Cabstar E", "CAMION", "CUBE", "e-NV200", "e-NV200 EVALIA", "EVALIA", "GT-R", "Interstar", "JUKE", "Kubistar", "LEAF", "Maxima", "Maxima QX", "Micra", "Murano", "Navara", "NOTE", "NP300 Navara", "NP300 Pick Up", "NT400", "NT400 Cabstar", "NV200", "NV200 EVALIA", "NV250", "NV300", "NV400", "Pathfinder", "Patrol", "Patrol GR", "Pick-up", "Pixo", "Prairie", "Primastar", "Primera", "PULSAR", "QASHQAI", "QASHQAI+2", "Serena", "Sunny", "Terrano", "Terrano II", "Tiida", "Townstar", "Trade", "Vanette", "Vanette Cargo", "Vanette E", "X-TRAIL"],

OPEL : ["Adam", "Agila", "Ampera", "Antara", "Ascona", "Astra", "Astra Electric", "Astra PHEV", "Cabrio", "Calibra", "Combo", "Combo-e", "Combo Electric", "Combo-e Life", "Combo Life", "Corsa", "Corsa-e", "Corsa Electric", "Corsa-e Van", "Corsa Van", "Crossland", "Crossland X", "Frontera", "Frontera Electric", "Grandland", "Grandland PHEV", "Grandland X", "Grandland X PHEV", "GT", "GTC", "Insignia", "Kadett", "Karl", "Manta", "Meriva", "Mokka", "Mokka-e", "Mokka X", "Monterey", "Monza", "Movano", "Movano-e", "Omega", "Rekord", "Senator", "Signum", "Sintra", "Speedster", "Tigra", "Vectra", "Vivaro", "Vivaro-e", "Vivaro Electric", "Zafira", "Zafira Electric", "Zafira-e Life", "Zafira Life", "Zafira Tourer"],

PEUGEOT : ["1007", "106", "107", "108", "2008", "205", "206", "206 +", "206 SW", "207", "207 +", "208", "208 XAD", "3008", "3008 Hybrid", "3008 Plug-in Hybrid", "306", "307", "307 SW", "308", "309", "4007", "4008", "405", "406", "407", "407 SW", "408", "5008", "5008 Hybrid", "5008 Plug-in Hybrid", "505", "508", "508 Hybrid", "604", "605", "607", "806", "807", "Bipper", "Boxer", "e-2008", "e-208", "E-3008", "E-308", "E-5008", "e-Boxer", "e-Expert", "e-Partner", "e-Rifter", "e-Traveller", "Expert", "ion", "J5", "Partner", "Partner Origin", "RCZ", "Rifter", "Traveller"],

RENAULT : ["5", "Alpine", "Alpine", "Arkana", "Austral", "Avantime", "Captur", "Clio", "Clio 4", "Clio Campus", "Clio III", "Espace", "Express", "Fluence", "Fuego", "Grand Espace", "Grand Kangoo Combi", "Grand Modus", "Grand Scénic", "Kadjar", "Kangoo", "Kangoo Combi", "Kangoo Combi E-Tech", "Kangoo Express", "Kangoo Furgón", "Kangoo Furgón Compact", "Kangoo Furgón E-Tech", "Kangoo Z.E.", "Koleos", "Laguna", "Latitude", "Mascott", "Master", "Master E-Tech", "Master Propulsión", "Master Z.E.", "Maxity", "Mégane", "Megane E-Tech", "Modus", "Nevada", "R11", "R18", "R19", "R21", "R25", "R4", "R5", "R6", "R9", "Rafale", "Safrane", "Scénic", "Scenic E-Tech", "Spider", "Symbioz", "Talisman", "Trafic", "Trafic E-Tech", "Twingo", "Twingo E-Tech", "Twizy", "Vel Satis", "Wind", "ZOE"],

SEAT : ["Alhambra", "ALTEA", "Altea Freetrack", "Altea XL", "Arona", "Arosa", "Ateca", "Cordoba", "Exeo", "Ibiza", "Inca", "León", "Malaga", "Marbella", "Mii", "Ronda", "Tarraco", "Terra", "Toledo"],

TOYOTA : ["4Runner", "Auris", "Avensis", "Avensis Verso", "Aygo", "Aygo X Cross", "BZ4X", "Camry", "Carina E", "Celica", "C-HR", "Corolla", "Corolla Cross", "Corolla Sedan", "Corolla Verso", "Dyna", "GR86", "GR Supra", "GR Yaris", "GT86", "Hiace", "Highlander", "Hilux", "iQ", "Land Cruiser", "Land Cruiser 100", "Land Cruiser 200", "Land Cruiser 80", "Land Cruiser 90", "Mirai", "MR2", "Paseo", "Picnic", "Previa", "Prius", "Prius+", "Proace", "Proace City", "Proace City Verso", "Proace Verso", "Rav4", "Supra", "Urban Cruiser", "Verso", "Yaris", "Yaris Cross", "Yaris Verso"],

VOLKSWAGEN : ["Amarok", "Arteon", "Beetle", "Bora", "Caddy", "California", "Caravelle", "CC", "Corrado", "Crafter", "Eos", "e-up!", "Fox", "Golf", "Golf Plus", "Golf Sportsvan", "Grand California", "ID.3", "ID.4", "ID.5", "ID.7", "ID. BUZZ", "Jetta", "LT", "Lupo", "Multivan", "New Beetle", "Passat", "Passat CC", "Phaeton", "Polo", "Santana", "Scirocco", "Sharan", "Taigo", "Taro", "T-Cross", "Tiguan", "Tiguan Allspace", "Touareg", "Touran", "Transporter", "T-Roc", "up!", "Vento"],

ABARTH : ["124 Spider", "500", "500C", "Grande Punto", "Punto", "Punto EVO"],

ALFAROMEO : ["145", "146", "147", "155", "156", "159", "164", "166", "33", "4C", "75", "90", "Brera", "Crosswagon", "Giulia", "Giulietta", "GT", "GTV", "Junior", "Milano", "MiTo", "Spider", "Sprint", "Stelvio", "Tonale"],

CHEVROLET : ["Alero", "Astro", "Aveo", "Blazer", "Camaro", "Captiva", "Corvette", "Corvette Stingray", "Cruze", "Epica", "Evanda", "HHR", "Kalos", "Lacetti", "Malibu", "Matiz", "Nubira", "Orlando", "Spark", "Tacuma", "Tahoe", "TrailBlazer", "Trans Sport", "Trax", "Volt"],

CUPRA : ["Ateca", "Born", "Formentor", "León", "Tavascan"],

DACIA : ["Contac", "Dokker", "Duster", "Jogger", "Lodgy", "Logan", "Sandero", "Spring"],

DS : ["DS 3", "DS 3 Crossback", "DS 3 Crossback E-Tense", "DS 4", "DS 4 Crossback", "DS 5", "DS 7", "DS 7 Crossback", "DS 7 Crossback E-Tense", "DS 9"],

FIAT : ["124 Spider", "500", "500C", "500L", "500X", "600", "Argenta", "Barchetta", "Brava", "Bravo", "Cinquecento", "Coupe", "Croma", "Doblò", "Doblò Cargo", "Doblò Furgón", "Doblò Panorama", "Ducato", "e-Doblò", "e-Ducato", "E-Ulysse", "Fiorino", "Freemont", "Fullback", "Grande Punto", "Idea", "Linea", "Marea", "Multipla", "Palio Weekend", "Panda", "Panda Classic", "Punto", "Punto Classic", "Punto EVO", "Qubo", "Regata", "Ritmo", "Scudo", "Sedici", "Seicento", "Stilo", "Strada", "Talento", "Tempra", "Tipo", "Ulysse", "Uno"],

HONDA : ["Accord", "Civic", "Concerto", "CR-V", "CRX", "CR-Z", "e:NY1", "FR-V", "Honda e", "HR-V", "Insight", "Jazz", "Legend", "Logo", "NSX", "Prelude", "S2000", "Stream", "ZR-V"],

JAGUAR : ["E-Pace", "F-Pace", "F-Type", "i-Pace", "Serie XJ", "Serie XK", "S-Type", "XE", "XF", "XJ", "X-Type"],

JEEP : ["Avenger", "Cherokee", "Commander", "Compass", "Gladiator", "Grand Cherokee", "Patriot", "Renegade", "Wrangler", "Wrangler Unlimited"],

LANDROVER : ["Defender", "Discovery", "Discovery 4", "Discovery Sport", "Freelander", "Range Rover", "Range Rover Evoque", "Range Rover Sport", "Range Rover Velar"],

LEXUS : ["CT", "ES", "GS", "GS300", "GS430", "GS450h", "GS460", "IS", "IS200", "IS220d", "IS250", "IS300", "LBX", "LC", "LM", "LS", "LS400", "LS430", "LS460", "LS600h", "NX", "RC", "RX", "RX300", "RX350", "RX400h", "RZ", "SC", "SC430", "UX"],

MAZDA : ["121", "323", "626", "929", "B-2500", "BT-50", "CX-3", "CX-30", "CX-5", "CX-60", "CX-7", "CX-80", "CX-9", "Demio", "Mazda2", "Mazda3", "Mazda5", "Mazda6", "MPV", "MX3", "MX-30", "MX5", "MX6", "Premacy", "RX7", "RX-8", "Serie B", "Tribute", "Xedos 6", "Xedos 9"],

MG : ["eHS", "HS", "Marvel R", "MG3 Hybrid+", "MG4", "MG5", "MGF", "TF", "ZR", "ZS", "ZS EV", "ZT", "ZT-T"],

MINI : ["AcEMAN", "CLUBMAN", "COOPER", "COUNTRYMAN", "MINI", "Paceman"],

MITSUBISHI : ["3000 GT", "300 GT", "ASX", "Canter", "Carisma", "Colt", "Eclipse", "Eclipse Cross", "Galant", "Grandis", "i-MiEV", "L200", "L300", "Lancer", "Montero", "Montero iO", "Montero Sport", "Outlander", "Space Gear", "Space Runner", "Space Star", "Space Wagon"],

PORSCHE : ["718", "911", "918", "924", "928", "944", "968", "Boxster", "Carrera GT", "Cayenne", "Cayenne Coupé", "Cayman", "Macan", "Panamera", "Taycan"],

SKODA : ["Citigo", "Enyaq", "Enyaq Coupe", "Fabia", "Favorit", "Felicia", "Forman", "Kamiq", "Karoq", "Kodiaq", "Octavia", "Octavia Tour", "Pickup", "Praktik", "Rapid", "Roomster", "S", "Scala", "Scout", "Spaceback", "Superb", "Yeti"],

SUZUKI : ["Across", "Alto", "Baleno", "Carry", "Celerio", "Grand Vitara", "Grand Vitara XL-7", "Ignis", "Jimny", "Kizashi", "Liana", "Maruti", "Samurai", "S-Cross", "Splash", "Swace", "Swift", "SX4", "SX4 S-Cross", "Vitara", "Wagon R+"],

TESLA : ["Model 3", "Model S", "Model X", "Model Y"],

VOLVO : ["240", "340", "360", "440", "460", "480", "740", "760", "850", "940", "960", "C30", "C40", "C70", "Classic", "EC40", "EX30", "EX40", "EX90", "S40", "S60", "S60 Cross Country", "S70", "S80", "S90", "V40", "V40 Cross Country", "V50", "V60", "V60 Cross Country", "V70", "V70 Classic", "V70 XC", "V90", "V90 Cross Country", "XC40", "XC60", "XC70", "XC90"],

};


const years = ["2025", "2024", "2023", "2022", "2021", "2020", "2019", "2018", "2017", "2016", "2015", "2014",
    "2013", "2012", "2011", "2010", "2009", "2008", "2007", "2006", "2005", "2004",
    "2003", "2002", "2001", "2000", "1999", "1998", "1997", "1996", "1995", "1994",
    "1993", "1992", "1991", "1990", "1989", "1988", "1987", "1986", "1985", "1984",
    "1983", "1982", "1981", "1980", "1979", "1978", "1977", "1976", "1975", "1974",
    "1973", "1972", "1971", "1970", "1969", "1968", "1967", "1966", "1965", "1964",
    "1963", "1962", "1961", "1960", "1959", "1958", "1957", "1956", "1955", "1954",
    "1953", "1952", "1951", "1950"];
const fuelTypes = ["Gasoline", "Diesel", "Electric", "Hybrid"];
const colors = ["Black", "White", "Silver", "Red", "Blue", "Green"];
const states = ["New", "Used", "Certified Pre-Owned"];
const doorOptions = ["2", "3", "4", "5"];

const ITEMS_PER_PAGE = 5;

interface Car {
  Source_Url: string;
  Title?: string;
  Model?: string;
  Brand?: string;
  Year?: number;
  Year_Produced?: number;
  Mileage?: number;
  Price: number;
  Photo_Links?: string[];
  Image?: string[];
  Location?: string;
  Milage_Value?: number;
  Fuel?: string;
  Gearbox?: string;
  Description?: string;
  selected?: boolean;
  similarity_score?: number;
}

interface Stats {
  nr_of_cars: number;
  min_price: number;
  max_price: number;
  median_price: number;
  mean_price: number;
}

interface ApiResponse {
  cars: Car[];
  stats: Stats;
  report: string;
}

const apiInfo = {
  toolName: "Market Evaluation Tool",
  endpoint: "market-evaluation",
  requestExample: {
    brand: "Toyota",
    model: "Camry",
    year: 2023,
    fuelType: "Gasoline",
  },
  responseExample: {
    vehicles: [
      // Example vehicle data
    ],
    stats: {
      // Example stats data
    },
  },
};

const modelInfo = {
  title: "Market Evaluation Tool",
  description:
    "This tool evaluates the market value of vehicles based on various criteria.",
  type: "tool" as const,
  purpose: [
    "Provide accurate market valuations",
    "Support informed purchasing decisions",
    "Track market trends and fluctuations",
  ],
  methodology: [
    "Collects and analyzes real-time market data",
    "Considers various factors like brand, model, year, mileage, and condition",
    "Uses machine learning algorithms to predict market value",
  ],
  outputs: [
    "Estimated market value",
    "Price range",
    "Comparable vehicles",
    "Market trend analysis",
  ],
};

export default function MarketEvaluationPage() {
  type CarBrand = keyof typeof carModels;

  const [selectedBrand, setSelectedBrand] = useState<CarBrand | "">("");
  const [selectedVariant, setSelectedVariant] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedYear, setSelectedYear] = useState<string>("");
  const [selectedFuelType, setSelectedFuelType] = useState<string>("");
  const [kmRange, setKmRange] = useState<[number, number]>([0, 200000]);
  const [selectedColor, setSelectedColor] = useState<string>("");
  const [selectedState, setSelectedState] = useState<string>("");
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  const [freeSearchQuery, setFreeSearchQuery] = useState<string>("");
  const [searchResults, setSearchResults] = useState<ApiResponse | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [reportDate, setReportDate] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();
  const [horsepowerValue, setHorsepowerValue] = useState<number>(100);
  const [selectedDoors, setSelectedDoors] = useState<number>(0);
  const [allYearsData, setAllYearsData] = useState<ApiResponse | null>(null);
  const [selectedVehicles, setSelectedVehicles] = useState<Set<string>>(new Set());

  const handleSearch = async () => {
    if (!selectedBrand) {
      setError("Please select a brand to perform the search.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResults(null);
    setAllYearsData(null);

    // Log search initiation
    const initialLogData = {
      action: 'market_evaluation_initiated',
      user_id: session?.user?.id,
      level: 'info',
      component: 'market-evaluation',
      details: {
        brand: selectedBrand,
        model: selectedModel,
        year: selectedYear,
        fuelType: selectedFuelType,
        variant: selectedVariant,
        pages: 50,
        timestamp: new Date().toISOString()
      }
    };
    log.info(initialLogData);
    await sendLogToBackend(initialLogData);
    const url = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-api-call/market-evaluation/?user_id=${session?.user?.id}&external_api_id=67b9a82e383420fe8690dfc8`;

    // Original request with all filters including year
    const requestData = {
      brand: selectedBrand,
      ...(selectedModel && { model: selectedModel }),
      ...(selectedYear && { year: selectedYear }),
      ...(selectedFuelType && { fuel: selectedFuelType }),
      ...(selectedVariant && { variant: selectedVariant }),
      pages: 50,
    };

    // Request for all years (without year filter)
    const allYearsRequestData = {
      brand: selectedBrand,
      ...(selectedModel && { model: selectedModel }),
      ...(selectedFuelType && { fuel: selectedFuelType }),
      ...(selectedVariant && { variant: selectedVariant }),
      pages: 50,
    };

    try {
      // Fetch user data by ID
      const userId = session?.user?.id;
      if (!userId) {
        throw new Error("User ID is missing. Please log in.");
      }

      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        method: "GET",
        headers: { accept: "application/json" },
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();

      // Log user access check
      const accessLogData = {
        action: 'market_evaluation_access_checked',
        user_id: session?.user?.id,
        level: 'info',
        component: 'market-evaluation',
        details: {
          userRole: userData.role,
          hasAccess: userData.role === 'admin' || userData.tools?.market_evaluation,
          timestamp: new Date().toISOString()
        }
      };
      log.info(accessLogData);
      await sendLogToBackend(accessLogData);

      // Check if user is admin or not
      if (userData.role !== "admin") {
        const { tools } = userData;

        // Perform checks for users
        if (!tools.market_evaluation) {
          throw new Error("You do not have access to the market evaluation tool.");
        }

      }
      console.log(requestData)

      // Make the main API request with all filters including year
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch data from the API");
      }

      const data = await response.json();
      setSearchResults(data.data)
      console.log(data.data.cars)

      // If a year is selected, make a second API call without the year filter
      if (selectedYear) {
        // Log the secondary request
        const secondaryLogData = {
          action: 'market_evaluation_all_years_request',
          user_id: session?.user?.id,
          level: 'info',
          component: 'market-evaluation',
          details: {
            brand: selectedBrand,
            model: selectedModel,
            fuelType: selectedFuelType,
            variant: selectedVariant,
            timestamp: new Date().toISOString()
          }
        };
        log.info(secondaryLogData);
        await sendLogToBackend(secondaryLogData);

        const allYearsResponse = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(allYearsRequestData),
        });

        if (allYearsResponse.ok) {
          const allYearsResult = await allYearsResponse.json();
          setAllYearsData(allYearsResult.data);
        }
      } else {
        // If no year filter is applied, both datasets are the same
        setAllYearsData(data.data);
      }

      // Update km range based on the search results
      if (data.data?.cars?.length > 0) {
        const kmValues = data.data.cars
          .map((car: Car) => car.Milage_Value)
          .filter((val: number | undefined | null) =>
            val !== undefined &&
            val !== null &&
            !isNaN(val)
          );

        if (kmValues.length > 0) {
          const minKm = Math.min(...kmValues);
          const maxKm = Math.max(...kmValues);
          setKmRange([minKm, maxKm]);
        }
      }

      setCurrentPage(1);
      setReportDate(new Date());

      // Log successful search
      const successLogData = {
        action: 'market_evaluation_completed',
        user_id: session?.user?.id,
        level: 'info',
        component: 'market-evaluation',
        details: {
          brand: selectedBrand,
          model: selectedModel,
          year: selectedYear,
          fuelType: selectedFuelType,
          variant: selectedVariant,
          resultsCount: data.data?.stats?.nr_of_cars,
          priceRange: {
            min: data.data?.stats?.min_price,
            max: data.data?.stats?.max_price,
            median: data.data?.stats?.median_price
          },
          timestamp: new Date().toISOString()
        }
      };
      log.info(successLogData);
      await sendLogToBackend(successLogData);
      console.log("Search Results:", searchResults);

    } catch (err) {
      // Log error
      const errorLogData = {
        action: 'market_evaluation_error',
        user_id: session?.user?.id,
        level: 'error',
        component: 'market-evaluation',
        details: {
          brand: selectedBrand,
          model: selectedModel,
          year: selectedYear,
          fuelType: selectedFuelType,
          variant: selectedVariant,
          error: err instanceof Error ? err.message : "An unexpected error occurred",
          timestamp: new Date().toISOString()
        }
      };
      log.error(errorLogData);
      await sendLogToBackend(errorLogData);

      const errorMessage = err instanceof Error ? err.message : "An error occurred while fetching the debtor status. Please try again.";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };


  const handleFreeSearch = async () => {
    if (!freeSearchQuery.trim()) {
      setError("Please enter a valid query for free search.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchResults(null);

    // Log free search initiation
    const initialLogData = {
      action: 'market_evaluation_free_search_initiated',
      user_id: session?.user?.id,
      level: 'info',
      component: 'market-evaluation',
      details: {
        query: freeSearchQuery,
        timestamp: new Date().toISOString()
      }
    };
    log.info(initialLogData);
    await sendLogToBackend(initialLogData);
    const external_api_id = `${process.env.MARKET_EVALUATION_API_ID}`
    const url = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-api-call/market-evaluation/?user_id=${session?.user?.id}&external_api_id=67b9a82e383420fe8690dfc8`;
    const requestData = {
      query: freeSearchQuery,
      pages: 50,
    };

    try {
      // Fetch user data by ID
      const userId = session?.user?.id;
      if (!userId) {
        throw new Error("User ID is missing. Please log in.");
      }

      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        method: "GET",
        headers: { accept: "application/json" },
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();

      // Log user access check
      const accessLogData = {
        action: 'market_evaluation_free_search_access_checked',
        user_id: session?.user?.id,
        level: 'info',
        component: 'market-evaluation',
        details: {
          userRole: userData.role,
          hasAccess: userData.role === 'admin' || userData.tools?.market_evaluation,
          timestamp: new Date().toISOString()
        }
      };
      log.info(accessLogData);
      await sendLogToBackend(accessLogData);

      //Check if user is admin or not
      if (userData.role !== "admin") {
        const { tools, user_token } = userData;

        // Perform checks for users
        if (!tools.osint) {
          throw new Error("You do not have access to the OSINT tool.");
        }

        if (user_token < 1) {
          throw new Error("Insufficient tokens to perform this action.");
        }
      }

      // Make the API request
      const response = await fetch(url, {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch data from the API");
      }

      const data = await response.json();
      setSearchResults(data.data);
      setCurrentPage(1);

      setReportDate(new Date());

      // Log successful free search
      const successLogData = {
        action: 'market_evaluation_free_search_completed',
        user_id: session?.user?.id,
        level: 'info',
        component: 'market-evaluation',
        details: {
          query: freeSearchQuery,
          resultsCount: data.stats.nr_of_cars,
          priceRange: {
            min: data.stats.min_price,
            max: data.stats.max_price,
            median: data.stats.median_price
          },
          timestamp: new Date().toISOString()
        }
      };
      log.info(successLogData);
      await sendLogToBackend(successLogData);


    } catch (err) {
      // Log error
      const errorLogData = {
        action: 'market_evaluation_free_search_error',
        user_id: session?.user?.id,
        level: 'error',
        component: 'market-evaluation',
        details: {
          query: freeSearchQuery,
          error: err instanceof Error ? err.message : "An unexpected error occurred",
          timestamp: new Date().toISOString()
        }
      };
      log.error(errorLogData);
      await sendLogToBackend(errorLogData);

      const errorMessage = err instanceof Error ? err.message : "An error occurred while fetching the debtor status. Please try again.";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  // Filter vehicles by km range before pagination
  const filteredVehicles = searchResults?.cars?.filter((vehicle: Car) => {
    if (vehicle.Milage_Value === undefined || vehicle.Milage_Value === null) return true;
    return vehicle.Milage_Value >= kmRange[0] && vehicle.Milage_Value <= kmRange[1];
  }) || [];

  const totalPages = filteredVehicles.length
    ? Math.ceil(filteredVehicles.length / ITEMS_PER_PAGE)
    : 0;

  const paginatedVehicles = filteredVehicles.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const toggleVehicleSelection = (vehicleUrl: string) => {
    setSelectedVehicles(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(vehicleUrl)) {
        newSelection.delete(vehicleUrl);
      } else {
        newSelection.add(vehicleUrl);
      }
      return newSelection;
    });
  };

  const getSelectedVehicles = () => {
    return filteredVehicles.filter(vehicle => selectedVehicles.has(vehicle.Source_Url));
  };

  const handleExportPDF = () => {
    if (!searchResults || !searchResults.stats) return;

    const vehiclesToInclude = selectedVehicles.size > 0 ? getSelectedVehicles() : filteredVehicles;

    // Create default stats object to ensure we have values
    const defaultStats = {
      nr_of_cars: 0,
      min_price: 0,
      max_price: 0,
      median_price: 0,
      mean_price: 0
    };

    // Use the displayed stats or default to the stats from search results or use defaultStats
    const statsToDisplay = (displayStats || searchResults?.stats || defaultStats);

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Generate HTML content for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${selectedBrand} ${selectedModel} Market Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1, h2, h3 { color: #333; }
            .stats { display: flex; flex-wrap: wrap; margin: 20px 0; }
            .stat-box {
              background: #f5f5f5;
              padding: 15px;
              border-radius: 5px;
              margin-right: 15px;
              margin-bottom: 15px;
              width: calc(25% - 20px);
              box-sizing: border-box;
            }
            .stat-value { font-size: 24px; font-weight: bold; color: #0066cc; }
            .stat-label { font-size: 14px; color: #666; }
            .vehicle-list { margin-top: 30px; }
            .vehicle-card {
              border: 1px solid #ddd;
              border-radius: 5px;
              padding: 15px;
              margin-bottom: 20px;
              display: flex;
            }
            .vehicle-image {
              width: 200px;
              height: 150px;
              object-fit: cover;
              margin-right: 20px;
              border-radius: 3px;
            }
            .vehicle-details h3 {
              margin-top: 0;
              color: #0066cc;
            }
            .vehicle-price {
              font-size: 22px;
              font-weight: bold;
              color: #009900;
              margin: 10px 0;
            }
            .vehicle-specs {
              color: #666;
              display: flex;
              flex-wrap: wrap;
            }
            .vehicle-spec {
              margin-right: 15px;
              margin-bottom: 5px;
            }
            .footer {
              margin-top: 40px;
              color: #666;
              font-size: 12px;
              border-top: 1px solid #ddd;
              padding-top: 15px;
            }
          </style>
        </head>
        <body>
          <h1>${selectedBrand} ${selectedModel} Market Report</h1>
          <p>Report generated on: ${new Date().toLocaleDateString()}</p>

          <h2>Market Statistics</h2>
          <div class="stats">
            <div class="stat-box">
              <div class="stat-value">$${Math.round(statsToDisplay.mean_price).toLocaleString()}</div>
              <div class="stat-label">Average Price</div>
            </div>
            <div class="stat-box">
              <div class="stat-value">$${Math.round(statsToDisplay.median_price).toLocaleString()}</div>
              <div class="stat-label">Median Price</div>
            </div>
            <div class="stat-box">
              <div class="stat-value">$${Math.round(statsToDisplay.min_price).toLocaleString()} - $${Math.round(statsToDisplay.max_price).toLocaleString()}</div>
              <div class="stat-label">Price Range</div>
            </div>
            <div class="stat-box">
              <div class="stat-value">${statsToDisplay.nr_of_cars}</div>
              <div class="stat-label">Total Listings</div>
            </div>
          </div>

          <h2>Vehicle Listings${selectedVehicles.size > 0 ? ` (${selectedVehicles.size} selected)` : ""}</h2>
          <div class="vehicle-list">
            ${vehiclesToInclude.map(car => `
              <div class="vehicle-card">
                <img class="vehicle-image" src="${car.Image?.[0] || 'https://via.placeholder.com/200x150?text=No+Image'}" alt="${car.Brand} ${car.Model}">
                <div class="vehicle-details">
                  <h3>${car.Brand} ${car.Model} (${car.Year})</h3>
                  <div class="vehicle-price">$${Math.round(car.Price).toLocaleString()}</div>
                  <div class="vehicle-specs">
                    ${car.Milage_Value?.toLocaleString() || '?'} km | ${car.Fuel || 'N/A'} | ${car.Gearbox || 'N/A'}
                  </div>
                  <p>${car.Description ? car.Description.substring(0, 150) + '...' : 'No description available.'}</p>
                  <a href="${car.Source_Url}" target="_blank">View Original Listing</a>
                </div>
              </div>
            `).join('')}
          </div>

          <div class="footer">
            <p>Data source: This report is based on current market data for ${selectedBrand} ${selectedModel} listings available as of ${new Date().toLocaleDateString()}.</p>
          </div>
        </body>
      </html>
    `;

    // Write the content to the new window and print
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Delay printing to ensure content is loaded
    setTimeout(() => {
      printWindow.print();
    }, 500);
  };

  // Add this function to calculate stats from selected vehicles
  const calculateSelectedStats = () => {
    if (selectedVehicles.size === 0) return null;

    const selectedCars = getSelectedVehicles();

    if (selectedCars.length === 0) return null;

    const prices = selectedCars.map(car => car.Price);

    // Calculate statistics
    const nr_of_cars = selectedCars.length;
    const min_price = Math.min(...prices);
    const max_price = Math.max(...prices);
    const mean_price = prices.reduce((sum, price) => sum + price, 0) / nr_of_cars;

    // Calculate median
    const sortedPrices = [...prices].sort((a, b) => a - b);
    const middle = Math.floor(sortedPrices.length / 2);
    const median_price = sortedPrices.length % 2 === 0
      ? (sortedPrices[middle - 1] + sortedPrices[middle]) / 2
      : sortedPrices[middle];

    return {
      nr_of_cars,
      min_price,
      max_price,
      median_price,
      mean_price
    };
  };

  // Add this to get either selected stats or original stats
  const displayStats = useMemo(() => {
    if (selectedVehicles.size > 0) {
      return calculateSelectedStats() || (searchResults?.stats || null);
    }
    return searchResults?.stats || null;
  }, [searchResults, selectedVehicles, filteredVehicles]);

  // Use this for the charts
  const vehiclesForCharts = useMemo(() => {
    return {
      all: filteredVehicles,
      highlighted: selectedVehicles.size > 0 ?
        Array.from(selectedVehicles) : []
    };
  }, [filteredVehicles, selectedVehicles]);

  return (
    <div className="space-y-6 w-full overflow-x-hidden">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-3xl font-bold">Market Evaluation</h1>
        <div className="flex space-x-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
          <FeedbackDialog toolName="Market Evaluation Tool" />
        </div>
      </div>
      <Card className="border border-neutral-200 rounded-xl shadow-sm">
        <CardContent className="p-6">
          <Tabs defaultValue="structured">
            <TabsList className="w-full bg-transparent border-b border-neutral-200 rounded-none space-x-4">
              <TabsTrigger
                value="structured"
                className="px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-neutral-900 data-[state=active]:shadow-none"
              >
                <span className="text-sm font-semibold">Structured Search</span>
              </TabsTrigger>
              <TabsTrigger
                value="free"
                className="px-4 py-2 data-[state=active]:border-b-2 data-[state=active]:border-neutral-900 data-[state=active]:shadow-none"
              >
                <span className="text-sm font-semibold">Free Search</span>
              </TabsTrigger>
            </TabsList>

            {/* Structured Search Content */}
            <TabsContent value="structured" className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-neutral-700">Brand</label>
                  <Select value={selectedBrand} onValueChange={(value: string) => {
                      setSelectedBrand(value as CarBrand);
                      setSelectedModel("");
                      setSelectedVariant("");
                    }}>
                    <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                      <SelectValue placeholder="Select brand" />
                    </SelectTrigger>
                    <SelectContent>
                      <div className="px-2 py-2">
                        <Input
                          placeholder="Search brands..."
                          className="mb-2"
                          onChange={(e) => {
                            const searchBox = e.currentTarget;
                            const items = document.querySelectorAll('[data-brand-item]');
                            items.forEach(item => {
                              const text = item.textContent?.toLowerCase() || '';
                              const match = text.includes(searchBox.value.toLowerCase());
                              (item as HTMLElement).style.display = match ? 'block' : 'none';
                            });
                          }}
                        />
                      </div>
                      {carBrands.map((brand) => (
                        <SelectItem key={brand} value={brand} data-brand-item>
                          {brand}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-neutral-700">Model</label>
                  <Select
                    value={selectedModel}
                    onValueChange={(value) => {
                      setSelectedModel(value);
                      setSelectedVariant("");
                    }}
                    disabled={!selectedBrand}
                  >
                    <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <div className="px-2 py-2">
                        <Input
                          placeholder="Search models..."
                          className="mb-2"
                          onChange={(e) => {
                            const searchBox = e.currentTarget;
                            const items = document.querySelectorAll('[data-model-item]');
                            items.forEach(item => {
                              const text = item.textContent?.toLowerCase() || '';
                              const match = text.includes(searchBox.value.toLowerCase());
                              (item as HTMLElement).style.display = match ? 'block' : 'none';
                            });
                          }}
                        />
                      </div>
                      {selectedBrand &&
                        carModels[selectedBrand].map((model) => (
                          <SelectItem key={model} value={model} data-model-item>
                            {model}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-neutral-700">Variant (Optional)</label>
                   <Input
                      type="text"
                      placeholder="e.g., Avant 2.0 TDI"
                      value={selectedVariant}
                      onChange={(e) => setSelectedVariant(e.target.value)}
                      className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300"
                      disabled={!selectedModel}
                    />
                </div>

                <div className="space-y-1">
                  <label className="text-sm font-medium text-neutral-700">Year</label>
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                      <SelectValue placeholder="Select year" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <div className="flex space-x-2 mt-7">
                    <Button
                      onClick={handleSearch}
                      disabled={isLoading}
                      className="h-11 bg-neutral-900 text-white hover:bg-neutral-800 focus:ring-2 focus:ring-neutral-300 flex-1"
                    >
                      {isLoading ? "Searching..." : "Start Analysis"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedBrand("");
                        setSelectedModel("");
                        setSelectedYear("");
                        setSelectedFuelType("");
                        setSelectedVariant("");
                        setKmRange([0, 200000]);
                        setSelectedColor("");
                        setSelectedState("");
                        setHorsepowerValue(100);
                        setSelectedDoors(0);
                        setSearchResults(null);
                        setAllYearsData(null);
                        setError(null);
                      }}
                      className="h-11 border-neutral-300 text-neutral-700 hover:bg-neutral-50 flex-1"
                    >
                      Reset Filters
                    </Button>
                  </div>
                </div>

              </div>

              <Collapsible open={isAdvancedFiltersOpen} onOpenChange={setIsAdvancedFiltersOpen} className="mt-6">
                <CollapsibleTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full h-11 border-neutral-300 text-neutral-700 hover:bg-neutral-50 justify-between"
                  >
                    <span className="text-sm font-medium">Advanced Filters</span>
                    {isAdvancedFiltersOpen ? (
                      <ChevronUp className="h-4 w-4 text-neutral-600" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-neutral-600" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    {/* Kilometers Range */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-neutral-700">
                        Kilometers Range: {kmRange[0].toLocaleString()} - {kmRange[1].toLocaleString()} km
                      </label>
                      <Slider
                        min={0}
                        max={Math.max(200000, kmRange[1])}
                        step={1000}
                        value={kmRange}
                        onValueChange={(value: number[]) => {
                          if (Array.isArray(value) && value.length === 2) {
                            setKmRange(value as [number, number]);
                            setCurrentPage(1);
                          }
                        }}
                        className="w-full [&>span:first-child]:h-2 [&>span:first-child>span]:h-2 [&>span:nth-child(2)]:w-5 [&>span:nth-child(2)]:h-5 [&>span:nth-child(2)]:-top-1.5 [&>span:nth-child(3)]:w-5 [&>span:nth-child(3)]:h-5 [&>span:nth-child(3)]:-top-1.5"
                      />
                      <div className="flex justify-between text-xs text-neutral-500">
                        <span>{kmRange[0].toLocaleString()} km</span>
                        <span>{kmRange[1].toLocaleString()} km</span>
                      </div>
                    </div>

                    {/* Fuel Type */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-neutral-700">Fuel Type</label>
                      <Select value={selectedFuelType} onValueChange={setSelectedFuelType}>
                        <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                          <SelectValue placeholder="Select fuel type" />
                        </SelectTrigger>
                        <SelectContent>
                          {fuelTypes.map((fuelType) => (
                            <SelectItem key={fuelType} value={fuelType}>
                              {fuelType}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Number of Doors */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-neutral-700">Number of Doors</label>
                      <Select
                        value={selectedDoors ? String(selectedDoors) : ""}
                        onValueChange={(value) => setSelectedDoors(value ? parseInt(value) : 0)}
                      >
                        <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                          <SelectValue placeholder="Doors" />
                        </SelectTrigger>
                        <SelectContent>
                          {doorOptions.map((doors) => (
                            <SelectItem key={doors} value={doors}>
                              {doors} Doors
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Color */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-neutral-700">Color</label>
                      <Select value={selectedColor} onValueChange={setSelectedColor}>
                        <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                          <SelectValue placeholder="Color" />
                        </SelectTrigger>
                        <SelectContent>
                          {colors.map((color) => (
                            <SelectItem key={color} value={color}>
                              {color}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Vehicle State */}
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-neutral-700">State</label>
                      <Select value={selectedState} onValueChange={setSelectedState}>
                        <SelectTrigger className="h-11 border-neutral-300 hover:border-neutral-400 focus:ring-2 focus:ring-neutral-300">
                          <SelectValue placeholder="State" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state} value={state}>
                              {state}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </TabsContent>

            {/* Free Search Content */}
            <TabsContent value="free" className="pt-6">
              <div className="flex gap-4">
                <Input
                  type="text"
                  placeholder="Enter vehicle search query..."
                  value={freeSearchQuery}
                  onChange={(e) => setFreeSearchQuery(e.target.value)}
                  className="h-11 border-neutral-300 focus:ring-2 focus:ring-neutral-300 flex-1"
                />
                <Button
                  onClick={handleFreeSearch}
                  disabled={isLoading}
                  className="h-11 bg-neutral-900 text-white hover:bg-neutral-800 focus:ring-2 focus:ring-neutral-300"
                >
                  {isLoading ? "Searching..." : "Analyze Market"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFreeSearchQuery("");
                    setSearchResults(null);
                    setAllYearsData(null);
                    setError(null);
                  }}
                  className="h-11 border-neutral-300 text-neutral-700 hover:bg-neutral-50"
                >
                  Clear
                </Button>
              </div>
            </TabsContent>
          </Tabs>
          <div className="flex justify-end mb-4"></div>
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {searchResults && reportDate && (
            <div className="mt-6 space-y-6 w-full overflow-hidden relative">
              <div className="flex justify-between items-start">
                <SearchSummary
                  filters={{
                    brand: selectedBrand,
                    model: selectedModel,
                    year: selectedYear,
                    fuelType: selectedFuelType,
                    kmRange,
                    color: selectedColor,
                    state: selectedState,
                  }}
                  date={reportDate}
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 border-2 hover:border-primary"
                  onClick={handleExportPDF}
                >
                  <FileDown className="h-4 w-4" />
                  <span>Export PDF</span>
                </Button>
              </div>

              {searchResults.stats?.nr_of_cars == 0 ? (
                <div className="text-center my-8">
                  <h2 className="text-2xl font-bold mb-2">Search Results</h2>
                  <p className="text-lg">
                    No vehicles found matching your criteria
                  </p>
                </div>
              ) : (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 items-center">
                    <div className="flex flex-col items-center justify-center h-full">
                      <h1 className="text-4xl font-bold">Search Results</h1>
                      {filteredVehicles.length !== searchResults?.stats?.nr_of_cars && (
                        <p className="text-lg text-center">
                          {filteredVehicles.length} vehicles matching your filters
                        </p>
                      )}
                      {selectedVehicles.size > 0 && (
                        <p className="text-md text-blue-600 mt-1">
                          {selectedVehicles.size} vehicles selected for analysis
                          {selectedVariant && ` (Sorted by similarity to "${selectedVariant}")`}
                        </p>
                      )}
                    </div>
                    <div className="flex justify-center md:justify-end">
                      <PriceStatsInfographic
                        stats={displayStats || {
                          nr_of_cars: 0,
                          min_price: 0,
                          max_price: 0,
                          median_price: 0,
                          mean_price: 0
                        }}
                        safeZoneRange={[
                          (displayStats?.median_price || 0) * 0.9,
                          (displayStats?.median_price || 0) * 1.1
                        ]}
                        estimatedPrice={
                          selectedVehicles.size > 0
                            ? getSelectedVehicles().reduce((sum, car) => sum + car.Price, 0) /
                              Math.max(1, getSelectedVehicles().length)
                            : filteredVehicles.length > 0 ? filteredVehicles.reduce((sum, car) => sum + car.Price, 0) / filteredVehicles.length : 0
                        }
                      />
                    </div>
                  </div>

                  <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 w-full">
                    {(paginatedVehicles || []).map((vehicle) => (
                      <div key={vehicle.Source_Url} className="relative">
                        <div className="absolute top-2 right-2 z-10 p-1 bg-white rounded-full shadow-md">
                          <input
                            type="checkbox"
                            id={`select-${vehicle.Source_Url}`}
                            checked={selectedVehicles.has(vehicle.Source_Url)}
                            onChange={() => toggleVehicleSelection(vehicle.Source_Url)}
                            className="h-5 w-5 cursor-pointer accent-blue-600"
                          />
                        </div>
                        <VehicleCard vehicle={vehicle as any} />
                      </div>
                    ))}
                  </div>

                  <Pagination className="mt-3 mb-3">
                    <PaginationContent>
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => setCurrentPage((prev) => prev - 1)}
                            className="cursor-pointer"
                          />
                        </PaginationItem>
                      )}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        if (totalPages <= 5) return i + 1;
                        if (currentPage <= 3) return i + 1;
                        if (currentPage >= totalPages - 2)
                          return totalPages - 4 + i;
                        return currentPage - 2 + i;
                      }).map((page: number) => (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      {totalPages > 5 && currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis className="cursor-default" />
                        </PaginationItem>
                      )}
                      {totalPages > 5 && currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis className="cursor-default" />
                        </PaginationItem>
                      )}
                      {currentPage < totalPages && (
                        <PaginationItem>
                          <PaginationNext
                            onClick={() => setCurrentPage((prev) => prev + 1)}
                            className="cursor-pointer"
                          />
                        </PaginationItem>
                      )}
                    </PaginationContent>
                  </Pagination>
                  <div className="flex justify-between items-center my-4">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="select-all-vehicles"
                        checked={selectedVehicles.size === filteredVehicles.length}
                        onChange={() => {
                          if (selectedVehicles.size === filteredVehicles.length) {
                            // Deselect all
                            setSelectedVehicles(new Set());
                          } else {
                            // Select all
                            setSelectedVehicles(new Set(filteredVehicles.map(v => v.Source_Url)));
                          }
                        }}
                        className="h-5 w-5 cursor-pointer accent-blue-600"
                      />
                      <label htmlFor="select-all-vehicles" className="text-sm font-medium">
                        {selectedVehicles.size === filteredVehicles.length ? "Deselect All" : "Select All"} Vehicles
                      </label>
                    </div>

                    <p className="text-sm text-gray-500">
                      {selectedVehicles.size} of {filteredVehicles.length} selected
                    </p>
                  </div>
                  <div className="grid gap-6 md:grid-cols-2 mt-3 mb-3">
                    <Card className="overflow-hidden">
                      <CardHeader>
                        <CardTitle>
                          Price vs Mileage
                          {selectedVehicles.size > 0 &&
                            <span className="text-sm font-normal text-blue-600 ml-2">
                              ({selectedVehicles.size} vehicles highlighted)
                            </span>
                          }
                          {selectedVariant &&
                            <span className="text-sm font-normal text-gray-500 ml-2 block">
                              (Sorted by similarity to "{selectedVariant}")
                            </span>
                          }
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <PriceVsMileagePlot
                          data={filteredVehicles.map(car => ({
                            Source_Url: car.Source_Url,
                            Milage_Value: car.Milage_Value || 0,
                            Price: car.Price
                          }))}
                          highlightedVehicles={vehiclesForCharts.highlighted}
                        />
                      </CardContent>
                    </Card>
                    <Card className="overflow-hidden">
                      <CardHeader>
                        <CardTitle>
                          Price vs Year
                          {selectedVehicles.size > 0 &&
                            <span className="text-sm font-normal text-blue-600 ml-2">
                              ({selectedVehicles.size} vehicles highlighted)
                            </span>
                          }
                          {selectedVariant &&
                            <span className="text-sm font-normal text-gray-500 ml-2 block">
                              (Sorted by similarity to "{selectedVariant}")
                            </span>
                          }
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <PriceVsYearPlot
                          data={filteredVehicles.map(car => ({
                            Source_Url: car.Source_Url,
                            Year: car.Year || 0,
                            Price: car.Price
                          }))}
                          highlightedVehicles={vehiclesForCharts.highlighted}
                        />
                      </CardContent>
                    </Card>
                  </div>
                  <AiNotes
                    notes={searchResults.report}
                    structured={true}
                  />
                </div>
              )}

            </div>
          )}


        </CardContent>
      </Card>
    </div>
  );
}
