"use client"

import { useState } from 'react';
import { Card, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2, MapPin } from 'lucide-react';
import { APIDialog } from "@/components/api-dialog";
import { ModelInfoDialog } from "@/components/model-info-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";

const apiInfo = {
  toolName: "Satellite Imagery Tool",
  endpoint: "satellite-imagery",
  requestExample: {
    address: "123 Main St, Anytown, USA",
    latitude: 40.7128,
    longitude: -74.0060
  },
  responseExample: {
    roofCondition: "Good",
    floodZone: "Moderate",
    vegetationOvergrowth: "Low",
    nearbyWaterSources: ["River 1km away"],
    constructionType: "Brick",
    roofType: "Asphalt Shingles",
    buildingFootprint: 1500,
    propertySize: 5000,
    otherStructures: ["Detached garage"],
    potentialHazards: ["Tree close to house"]
  }
}

const modelInfo = {
  title: "Satellite Imagery Tool",
  description: "This tool analyzes satellite imagery to assess property characteristics and potential risks for insurance underwriting.",
  type: "tool" as const,
  purpose: [
    "Assess roof condition and type",
    "Determine flood zone risk",
    "Identify vegetation overgrowth and nearby water sources",
    "Analyze building footprint and property size",
    "Detect other structures and potential hazards"
  ],
  methodology: [
    "Accesses high-resolution satellite imagery",
    "Employs computer vision and machine learning algorithms",
    "Integrates with geospatial data and flood maps"
  ],
  outputs: [
    "Roof condition assessment",
    "Flood zone classification",
    "Vegetation overgrowth analysis",
    "Proximity to water sources",
    "Building and property dimensions",
    "Identification of other structures",
    "List of potential hazards"
  ]
}

export default function SatelliteImageryPage() {
  const [address, setAddress] = useState("")
  const [latitude, setLatitude] = useState("")
  const [longitude, setLongitude] = useState("")
  const [imageryData, setImageryData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [inputMethod, setInputMethod] = useState<"address" | "geolocation">("address")

  const handleAnalyze = async () => {
    if ((inputMethod === "address" && !address) || (inputMethod === "geolocation" && (!latitude || !longitude))) return

    setIsLoading(true)
    setError(null)
    setImageryData(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      const mockImageryData = {
        roofCondition: "Good",
        floodZone: "Moderate",
        vegetationOvergrowth: "Low",
        nearbyWaterSources: ["River 1km away"],
        constructionType: "Brick",
        roofType: "Asphalt Shingles",
        buildingFootprint: 1500,
        propertySize: 5000,
        otherStructures: ["Detached garage"],
        potentialHazards: ["Tree close to house"],
        imageUrl: "/placeholder.svg?height=300&width=400"
      }
      setImageryData(mockImageryData)
    } catch (err) {
      setError("Failed to analyze satellite imagery. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
            <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Satellite Imagery Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Analyze Property</CardTitle>
          <CardDescription>Enter an address or coordinates to analyze satellite imagery</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={inputMethod} onValueChange={(value) => setInputMethod(value as "address" | "geolocation")}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="address">Address</TabsTrigger>
              <TabsTrigger value="geolocation">Geolocation</TabsTrigger>
            </TabsList>
            <TabsContent value="address">
              <div className="space-y-2 mt-4">
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Enter address"
                  required={inputMethod === "address"}
                />
              </div>
            </TabsContent>
            <TabsContent value="geolocation">
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="latitude">Latitude</Label>
                  <Input
                    id="latitude"
                    type="number"
                    step="any"
                    value={latitude}
                    onChange={(e) => setLatitude(e.target.value)}
                    placeholder="Enter latitude"
                    required={inputMethod === "geolocation"}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="longitude">Longitude</Label>
                  <Input
                    id="longitude"
                    type="number"
                    step="any"
                    value={longitude}
                    onChange={(e) => setLongitude(e.target.value)}
                    placeholder="Enter longitude"
                    required={inputMethod === "geolocation"}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
          <Button onClick={handleAnalyze} disabled={isLoading} className="mt-4 w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <MapPin className="mr-2 h-4 w-4" />
                Analyze
              </>
            )}
          </Button>
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {imageryData && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Satellite Imagery</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="relative aspect-video">
                <Image
                  src={imageryData.imageUrl}
                  alt="Satellite imagery"
                  fill
                  className="object-cover rounded-md"
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Analysis Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Property Characteristics</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Construction Type</p>
                      <Badge variant="secondary">{imageryData.constructionType}</Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Roof Type</p>
                      <Badge variant="secondary">{imageryData.roofType}</Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Building Footprint</p>
                      <p className="font-medium">{imageryData.buildingFootprint} sq ft</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Property Size</p>
                      <p className="font-medium">{imageryData.propertySize} sq ft</p>
                    </div>
                  </div>
                  {imageryData.otherStructures.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-muted-foreground">Other Structures</p>
                      <ul className="list-disc list-inside">
                        {imageryData.otherStructures.map((structure: string, index: number) => (
                          <li key={index} className="text-sm">{structure}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <div>
                  <h4 className="font-medium mb-2">Risk Assessment</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Roof Condition</p>
                      <Badge variant={imageryData.roofCondition === "Good" ? "default" : "destructive"}>
                        {imageryData.roofCondition}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Flood Zone</p>
                      <Badge variant={imageryData.floodZone === "High" ? "destructive" : "default"}>
                        {imageryData.floodZone}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Vegetation Overgrowth</p>
                      <Badge variant={imageryData.vegetationOvergrowth === "High" ? "destructive" : "default"}>
                        {imageryData.vegetationOvergrowth}
                      </Badge>
                    </div>
                  </div>
                  {imageryData.nearbyWaterSources.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-muted-foreground">Nearby Water Sources</p>
                      <ul className="list-disc list-inside">
                        {imageryData.nearbyWaterSources.map((source: string, index: number) => (
                          <li key={index} className="text-sm">{source}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {imageryData.potentialHazards.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-muted-foreground">Potential Hazards</p>
                      <ul className="list-disc list-inside">
                        {imageryData.potentialHazards.map((hazard: string, index: number) => (
                          <li key={index} className="text-sm">{hazard}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

