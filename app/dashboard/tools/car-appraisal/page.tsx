import { CarAppraisalTool } from "@/components/car-appraisal-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { InfoCircledIcon } from "@radix-ui/react-icons" 
import { CodeIcon } from "@radix-ui/react-icons"

export default function CarAppraisalPage() {
    const modelInfo = {
        title: "Car Appraisal Tool",
        description: "AI-powered visual analysis tool that evaluates vehicle damage from photos",
        purpose: [
            "Estimate repair costs based on vehicle damage",
            "Identify damaged parts and required repairs",
            "Provide detailed breakdown of labor and parts costs"
        ],
        methodology: [
            "Computer vision for damage detection",
            "Part identification and segmentation",
            "Cost estimation based on damage severity and repair complexity"
        ],
        outputs: [
            "Damage assessment with severity ratings",
            "Repair recommendations for affected parts",
            "Detailed cost breakdown",
            "Insurance risk score"
        ],
        type: "tool" as const
    }

    const apiInfo = {
        toolName: "Car Appraisal Tool",
        endpoint: `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-api-call/tool/?external_api_id=67b9a727383420fe8690dfc7`,
        requestExample: {
            make: "Toyota",
            model: "Camry",
            year: "2020",
            files: ["image1.jpg", "image2.jpg"]
        },
        responseExample: {
            damage_assessment: [
                { description: "Dented rear quarter panel", severity: "medium" },
                { description: "Scratched rear bumper", severity: "low" }
            ],
            repair_recommendations: [
                { part: "Rear quarter panel", repair_or_replace: "repair", labor_hours: 4, part_cost: 150 },
                { part: "Rear bumper", repair_or_replace: "replace", labor_hours: 3, part_cost: 200 }
            ],
            cost_breakdown: [
                { cost_type: "Parts", cost_amount: 350 },
                { cost_type: "Labor", cost_amount: 300 }
            ],
            insurance_risk_score: 42.6,
            user_explanation: "Analysis showed detected accident with moderate damage."
        }
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">Car Appraisal Tool</h1>
                <div className="flex space-x-2">
                <ModelInfoDialog {...modelInfo} />
                <APIDialog {...apiInfo} />
                </div>
            </div>
            <Card>
                <CardContent className="p-6">
                    <CarAppraisalTool compact={true} />
                </CardContent>
            </Card>
        </div>
    )
}

