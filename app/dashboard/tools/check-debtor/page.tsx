"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { log, sendLogToBackend } from '@/lib/logger'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, XCircle, Building2, User, CreditCard, BadgeDollarSign } from 'lucide-react'
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { useSession } from 'next-auth/react'

interface DebtRange {
  start: number;
  end: number;
  currency: string;
}

interface DebtItem {
  name: string;
  source: string;
  debt_date: string;
  range: DebtRange;
  status: string;
}

interface DebtorStatus {
  debts: DebtItem[];
}

// Add this constant at the top of the file, after the imports
const COUNTRIES = [
  'PT', // Portugal
  'ES', // Spain
  'FR', // France
  'GB', // United Kingdom
  'DE', // Germany
  'IT', // Italy
  'NL', // Netherlands
  'BE', // Belgium
  'CH', // Switzerland
  'AT', // Austria
  'SE', // Sweden
  'NO', // Norway
  'DK', // Denmark
  'FI', // Finland
  'IE', // Ireland
  'PL', // Poland
  'GR'  // Greece
] as const;

export default function CheckDebtorPage() {
  const { data: session } = useSession();
  const [idNumber, setIdNumber] = useState('')
  const [country, setCountry] = useState('PT')
  const [debtorStatus, setDebtorStatus] = useState<DebtorStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const apiInfo = {
    toolName: "Check Debtor Tool",
    endpoint: "https://debtors-8r13.onrender.com/debts",
    requestExample: {
      country: "PT",
      tax_number: "*********"
    },
    responseExample: [
      {
        "name": "COOPERATIVA AGRÍCOLA DE ESTARREJA CRL",
        "source": "ss",
        "debt_date": "2025-02-25T00:12:50.819000",
        "range": {
          "start": 10000,
          "end": 50000,
          "currency": "EUR"
        },
        "status": "active"
      }
    ]
  } 

  const handleSearch = async () => {
    setIsLoading(true);
    setError(null);
    setDebtorStatus(null);
   
    // Add logging for search initiation
    const initialLogData = {
      action: 'debtor_search_initiated',
      user_id: session?.user?.id,
      level: 'info',
      component: 'check-debtor',
      details: {
        idNumber: idNumber,
        country: country,
        timestamp: new Date().toISOString()
      }
    };
   
    log.info(initialLogData);
    try {
      await sendLogToBackend(initialLogData);
     
    } catch (error) {
      console.log(error)
    }

    try {
      //Fetch user data by ID
      const userId = session?.user?.id;
      if (!userId) {
        throw new Error("User ID is missing. Please log in.");
      }

      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        method: "GET",
        headers: { accept: "application/json" },
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();
   

      // Log user access check
      const accessLogData = {
        action: 'user_access_checked',
        user_id: session?.user?.id,
        level: 'info',
        component: 'check-debtor',
        details: {
          userRole: userData.role,
          hasAccess: userData.role === 'admin' || userData.tools?.check_debtor,
          timestamp: new Date().toISOString()
        }
      };
  
      log.info(accessLogData);
      try {
        await sendLogToBackend(accessLogData);
       
      } catch (error) {
       console.log(error)
      }

      // Check if user is admin or not
      if (userData.role !== "admin") {
        const { tools} = userData;

        // Perform checks for users
        if (!tools.check_debtor) {
          throw new Error("You do not have access to the debtor check tool.");
        }

      }

      // Make the API request for debtor status
      const queryParams = encodeURIComponent(JSON.stringify({ 
        country: country, 
        tax_number: idNumber 
      }));

      const debtorResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-api-call/check-debtor/?user_id=${userId}&external_api_id=67b9a871383420fe8690dfc9&query_params=${queryParams}`,
        {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        }
      );
      
      if (!debtorResponse.ok) {
        throw new Error(`Failed to fetch debtor status: ${debtorResponse.status}`);
      }

      const debtorData = await debtorResponse.json();
     
      // Log successful debtor check
      const logData = {
        action: 'debtor_check_completed',
        user_id: session?.user?.id,
        level: 'info',
        component: 'check-debtor',
        details: {
          idNumber: idNumber,
          country: country,
          result: {
            hasDebts: debtorData.data.length > 0,
            debtCount: debtorData.data.length
          },
          timestamp: new Date().toISOString()
        }
      };
      
      log.info(logData);
      try {
        await sendLogToBackend(logData);
       
      } catch (error) {
        console.error("[DEBUG] Failed to send debtor check log:", error);
      }
      
      //Update debtor status in the state
      setDebtorStatus({
        debts: debtorData.data
      });
    } catch (err) {
      console.error("Error occurred while handling search:", err);
      const errorMessage = err instanceof Error ? err.message : "An error occurred while fetching the debtor status. Please try again.";
      
      // Log error
      const errorLogData = {
        action: 'debtor_check_error',
        user_id: session?.user?.id,
        level: 'error',
        component: 'check-debtor',
        details: {
          idNumber: idNumber,
          error: errorMessage,
          timestamp: new Date().toISOString()
        }
      };
      
      log.error(errorLogData)
      try {
        await sendLogToBackend(errorLogData);
      } catch (error) {
        console.error("[DEBUG] Failed to send error log:", error);
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      console.log("API request completed");
    }
  };
  

  const modelInfo = {
    title: "Check Debtor Tool",
    description: "This tool checks if a given ID number is listed as a debtor in Social Security and Finances databases.",
    type: "tool" as const,
    purpose: [
      "Verify debtor status before offering services or products",
      "Assess financial risk associated with a customer",
      "Make informed decisions about creditworthiness"
    ],
    methodology: [
      "Connects to Social Security and Finances databases",
      "Checks if the provided ID number is present in the debtor lists",
      "Returns the debtor status for each database"
    ],
    outputs: [
      "Social Security Debtor Status (true/false)",
      "Finances Debtor Status (true/false)"
    ]
  }

  return (
    <div className="max-w-3xl mx-auto space-y-6">


<div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Check Debtor</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Debtor Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Input
              type="text"
              placeholder="Enter tax number (eg. *********)"
              value={idNumber}
              onChange={(e) => setIdNumber(e.target.value)}
              className="flex-1"
              aria-label="Tax number"
            />
            <select
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-24 px-3 py-2 border rounded-md"
              aria-label="Country"
            >
              {COUNTRIES.map((code) => (
                <option key={code} value={code}>{code}</option>
              ))}
            </select>
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? 'Searching...' : 'Search'}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {debtorStatus && (
            <div className="mt-6">
              {debtorStatus.debts.length === 0 ? (
                <Alert variant="default" className="text-green-500">
                  <AlertTitle className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5" />
                    <span>No Debts Found</span>
                  </AlertTitle>
                  <AlertDescription>
                    No debts were found for the provided tax number and country.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Found {debtorStatus.debts.length} debt record(s)</h3>
                  {debtorStatus.debts.map((debt, index) => (
                    <Alert
                      key={index}
                      variant="destructive"
                    >
                      <AlertTitle className="flex items-center gap-2 mb-2">
                        {debt.source === 'ss' ? (
                          <Building2 className="h-4 w-4" />
                        ) : (
                          <CreditCard className="h-4 w-4" />
                        )}
                        <span>
                          {debt.source === 'ss' ? 'Social Security' : 'Finances'} Debt
                        </span>
                      </AlertTitle>
                      <AlertDescription className="space-y-2">
                        <div className="flex items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-500" />
                          <span>Status: {debt.status}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{debt.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <BadgeDollarSign className="h-4 w-4" />
                          <span>
                            {debt.range.start.toLocaleString()} - {debt.range.end.toLocaleString()} {debt.range.currency}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          Debt date: {new Date(debt.debt_date).toLocaleDateString()}
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
