"use client"

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileUploader } from "@/components/file-uploader";
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, ImageIcon, FilmIcon as MovieIcon, XCircle, AlertCircle, Loader2 } from 'lucide-react'; // Importing AlertCircle from lucide-react
import { APIDialog } from "@/components/api-dialog";
import { ModelInfoDialog } from "@/components/model-info-dialog";
import { Badge } from "@/components/ui/badge"; // Import Badge component

const apiInfo = {
  toolName: "Image Analyser",
  endpoint: "image-analysis",
  requestExample: {
    media: ["base64_encoded_image_1", "base64_encoded_video_1"]
  },
  responseExample: [
    {
      filename: "image1.jpg",
      aiGenerated: true,
      edited: false,
      extractedFromInternet: true
    },
    {
      filename: "video1.mp4",
      aiGenerated: false,
      edited: true,
      extractedFromInternet: false
    }
  ]
}

const modelInfo = {
  title: "Image Analyser",
  description: "This tool analyzes images and videos to determine if they were AI-generated, edited, or extracted from the internet.",
  type: "tool" as const,
  purpose: [
    "Verify media authenticity",
    "Detect manipulated or synthetic media",
    "Identify potential copyright infringement"
  ],
  methodology: [
    "Uses computer vision and machine learning algorithms",
    "Checks for AI generation artifacts and editing traces",
    "Compares media against a database of known internet sources"
  ],
  outputs: [
    "AI Generated: True/False",
    "Edited: True/False",
    "Extracted from Internet: True/False",
    "Confidence Score (for each analysis)"
  ]
}

type AnalysisResult = {
  filename: string;
  aiGenerated: boolean;
  edited: boolean;
  extractedFromInternet: boolean;
};

export default function ImageAnalyserPage() {
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleFileUpload = (files: File[]) => {
    setMediaFiles(files);
  };

  const handleAnalyze = async () => {
    if (mediaFiles.length === 0) return;

    setIsAnalyzing(true);
    setAnalysisResults([]);

    const results: AnalysisResult[] = [];
    for (const file of mediaFiles) {
      const aiGenerated = Math.random() < 0.5;
      const edited = Math.random() < 0.3;
      const extractedFromInternet = Math.random() < 0.7;

      results.push({
        filename: file.name,
        aiGenerated,
        edited,
        extractedFromInternet,
      });
    }

    setTimeout(() => {
      setAnalysisResults(results);
      setIsAnalyzing(false);
    }, 1500);
  };

return (
  <div className="space-y-6">
    <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Image Analyser</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>

    <Card>
      <CardHeader>
        <CardTitle>Media Upload</CardTitle>
        <CardDescription>Upload images or videos for analysis</CardDescription>
      </CardHeader>
      <CardContent>
        <FileUploader
          id="media-upload"
          accept="image/*,video/*"
          onFilesSelected={handleFileUpload}
          multiple
        />
        <Button onClick={handleAnalyze} disabled={isAnalyzing || mediaFiles.length === 0}>
          {isAnalyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing...
            </>
          ) : (
            "Analyze"
          )}
        </Button>
      </CardContent>
    </Card>

    {analysisResults.length > 0 && (
      <Card>
        <CardHeader>
          <CardTitle>Analysis Results</CardTitle>
        </CardHeader>
        <CardContent>
          {analysisResults.map((result, index) => (
            <div key={index} className="border-b p-4 last:border-b-0">
              <div className="flex items-center mb-2">
                {result.filename.endsWith('.mp4') || result.filename.endsWith('.mov') ? (
                  <MovieIcon className="h-5 w-5 mr-2" />
                ) : (
                  <ImageIcon className="h-5 w-5 mr-2" />
                )}
                <span className="font-medium">{result.filename}</span>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">AI Generated:</p>
                  <Badge variant={result.aiGenerated ? "default" : "secondary"}>
                    {result.aiGenerated ? <CheckCircle className="mr-1 h-4 w-4" /> : <XCircle className="mr-1 h-4 w-4" />}
                    {result.aiGenerated ? "Yes" : "No"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Edited:</p>
                  <Badge variant={result.edited ? "default" : "secondary"}>
                    {result.edited ? <CheckCircle className="mr-1 h-4 w-4" /> : <XCircle className="mr-1 h-4 w-4" />}
                    {result.edited ? "Yes" : "No"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Extracted from Internet:</p>
                  <Badge variant={result.extractedFromInternet ? "default" : "secondary"}>
                    {result.extractedFromInternet ? <CheckCircle className="mr-1 h-4 w-4" /> : <XCircle className="mr-1 h-4 w-4" />}
                    {result.extractedFromInternet ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    )}

    {!isAnalyzing && analysisResults.length === 0 && mediaFiles.length > 0 && (
      <Alert className="mt-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No Results</AlertTitle>
        <AlertDescription>Click "Analyze" to process the uploaded media.</AlertDescription>
      </Alert>
    )}
  </div>
);
}

