import { HomeAppraisalTool } from "@/components/home-appraisal-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

export default function HomeAppraisalPage() {
const apiInfo = {
toolName: "Home Appraisal Tool",
endpoint: "home-appraisal",
requestExample: {
address: "123 Main St",
yearBuilt: 2000,
constructionType: "Brick",
media: ["base64_encoded_image_1", "base64_encoded_image_2"]
},
responseExample: {
// Example appraisal result
}
}

const modelInfo = {
title: "Home Appraisal Tool",
description: "This tool appraises a home's value based on provided details and media (photos/videos).",
type: "tool" as const,
purpose: [
"Estimate home repair costs",
"Assess damage severity",
"Generate repair recommendations"
],
methodology: [
"Uses computer vision to analyze damage from uploaded media",
"Considers home address, year built, and construction type",
"Calculates repair costs using market rates"
],
outputs: [
"Damage assessment",
"Repair recommendations",
"Cost breakdown"
]
}

return (
<div className="container mx-auto py-6">
    <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Home Appraisal Tool</h1>
        <div className="flex gap-2">
        <ModelInfoDialog {...modelInfo} />
        <APIDialog {...apiInfo} />
        </div>
    </div>
<HomeAppraisalTool />
{/*<APIDialog {...apiInfo} />*/}
</div>
)
}

