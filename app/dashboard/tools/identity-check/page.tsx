import { IdentityCheckTool } from "@/components/identity-check-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { FeedbackDialog } from "@/components/feedback-dialog"

export default function IdentityCheckPage() {
  const apiInfo = {
    toolName: "Identity Check Tool",
    endpoint: "identity-check",
    requestExample: {
      document: "base64_encoded_document"
    },
    responseExample: {
      documentType: "Driver's License",
      extractedInfo: {
        name: "<PERSON>",
        address: "123 Main St, Anytown, USA",
        idNumber: "*********",
        dateOfBirth: "1990-01-01",
        expirationDate: "2025-01-01"
      },
      isAuthentic: true,
      confidenceScore: 0.95
    }
  }

  const modelInfo = {
    title: "Identity Check Tool",
    description: "This tool analyzes identification documents to extract information and assess authenticity.",
    type: "tool" as const,
    purpose: [
      "Detect the type of identification document",
      "Extract key information from the document",
      "Assess the authenticity of the document"
    ],
    methodology: [
      "Uses computer vision and OCR to analyze document images",
      "Compares extracted data with known document formats and security features",
      "Employs machine learning models trained on authentic and falsified documents"
    ],
    outputs: [
      "Document type identification",
      "Extracted personal information",
      "Authenticity assessment",
      "Confidence score"
    ]
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Identity Check Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
          <FeedbackDialog toolName="Identity Check Tool" />
        </div>
      </div>
      <IdentityCheckTool />
    </div>
  )
}

