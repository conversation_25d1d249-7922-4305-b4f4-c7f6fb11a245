"use client"

import { useState, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Plus, X, AlertTriangle } from 'lucide-react'
import dynamic from 'next/dynamic'
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), { ssr: false })

const entityTypes = ["NIF", "Phone", "Email", "Vehicle"]

interface Entity {
id: number;
value: string;
type: string;
}

interface Relationship {
source: string;
target: string;
}

interface GraphData {
nodes: { id: string; group: number; type: string }[];
links: { source: string; target: string }[];
}

interface Node extends Record<string, unknown> {
  id: string;
  group: number;
  type: string;
}



const apiInfo = {
toolName: "Network Analysis Tool",
endpoint: "network-analysis",
requestExample: {
entities: [
{ value: "123456789", type: "NIF" },
{ value: "987654321", type: "Phone" }
]
},
responseExample: {
relationships: [
// Example relationships data
]
}
}


export default function NetworkAnalysisPage() {
const [entities, setEntities] = useState<Entity[]>([{ id: 1, value: "", type: "" }])
const [relationships, setRelationships] = useState<Relationship[]>([])
const [isLoading, setIsLoading] = useState(false)
const [error, setError] = useState<string | null>(null)
const [networkAnalysis, setNetworkAnalysis] = useState<string | null>(null)

const addEntity = () => {
const newId = entities.length > 0 ? Math.max(...entities.map(e => e.id)) + 1 : 1
setEntities([...entities, { id: newId, value: "", type: "" }])
}

const removeEntity = (id: number) => {
if (entities.length > 1) {
setEntities(entities.filter(entity => entity.id !== id))
}
}

const updateEntity = (id: number, field: 'value' | 'type', newValue: string) => {
setEntities(entities.map(entity =>
entity.id === id ? { ...entity, [field]: newValue } : entity
))
}

const analyzeNetwork = (entities: Entity[], relationships: Relationship[]): string => {
let analysis = "Network Analysis:\n\n"

// Check for direct connections between NIFs
const nifs = entities.filter(e => e.type === "NIF")
const directNifConnections = relationships.filter(
r => nifs.some(n => n.value === r.source) && nifs.some(n => n.value === r.target)
)

if (directNifConnections.length > 0) {
analysis += "- Direct connections found between NIFs. This could indicate potential fraud through familiar relationships.\n"
}

// Check for shared contact information
const sharedContacts = relationships.filter(
r => entities.some(e => (e.type === "Phone" || e.type === "Email") && (e.value === r.source || e.value === r.target))
)

if (sharedContacts.length > 0) {
analysis += "- Shared contact information detected. This might suggest a connection between the customer and the victim.\n"
}

// Check for common vehicles
const sharedVehicles = relationships.filter(
r => entities.some(e => e.type === "Vehicle" && (e.value === r.source || e.value === r.target))
)

if (sharedVehicles.length > 0) {
analysis += "- Common vehicles found in the network. This could indicate a shared asset or potential staged accident.\n"
}

// General analysis
if (relationships.length > entities.length * 2) {
analysis += "- High number of connections detected. This complex network might be indicative of organized fraud.\n"
}

if (analysis === "Network Analysis:\n\n") {
analysis += "No significant fraud indicators detected in the current network."
}

return analysis
}

const handleGenerateNetwork = async () => {
setIsLoading(true)
setError(null)

try {
// Enforce relationships between all entities
const newRelationships: Relationship[] = []
for (let i = 0; i < entities.length; i++) {
for (let j = i + 1; j < entities.length; j++) {
newRelationships.push({
source: entities[i].value,
target: entities[j].value,
})
}
}
setRelationships(newRelationships)

// Generate network analysis
const analysis = analyzeNetwork(entities, newRelationships)
setNetworkAnalysis(analysis)

// Simulate API delay
await new Promise(resolve => setTimeout(resolve, 1000))
} catch (err) {
setError('An error occurred while generating the network. Please try again.')
} finally {
setIsLoading(false)
}
}

const isGenerateDisabled = entities.some(entity => !entity.value || !entity.type) || isLoading

const graphData: GraphData = useMemo(() => ({
nodes: entities.map((entity, index) => ({ id: entity.value, group: index, type: entity.type })),
links: relationships,
}), [entities, relationships])

const getNodeColor = useCallback((node: any) => {
  const typeIndex = entityTypes.indexOf(node.type);
  return `hsl(${typeIndex * 90}, 70%, 50%)`;
}, []);



const nodeCanvasObject = useCallback(
  (node: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
    const label = node.id as string;
    const fontSize = 12 / globalScale;
    ctx.font = `${fontSize}px Sans-Serif`;
    ctx.fillStyle = getNodeColor(node);
    ctx.beginPath();
    ctx.arc(node.x || 0, node.y || 0, 20, 0, 2 * Math.PI, false);
    ctx.fill();

    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = "white";
    ctx.fillText(label, node.x || 0, node.y || 0);
  },
  [getNodeColor]
);



const modelInfo = {
title: "Network Analysis Tool",
description: "This tool analyzes relationships between entities to identify potential fraud indicators.",
type: "tool" as const,
purpose: [
"Detect fraud rings and suspicious connections",
"Visualize relationships between entities",
"Generate insights for fraud investigations"
],
methodology: [
"Creates a network graph based on provided entity data",
"Analyzes connections and patterns in the graph",
"Identifies potential fraud indicators based on network characteristics"
],
outputs: [
"Network graph visualization",
"List of potential fraud indicators",
"Analysis summary"
]
}

return (
<div className="space-y-6">
<div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Network Analysis</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
<Card>
<CardHeader>
<CardTitle>Generate Network</CardTitle>
</CardHeader>
<CardContent className="space-y-4">
{entities.map((entity, index) => (
<div key={entity.id} className="flex items-end space-x-2">
<div className="flex-grow space-y-2">
  <label htmlFor={`entityInput-${entity.id}`} className="text-sm font-medium">
    Enter Entity Identifier
  </label>
  <Input
    id={`entityInput-${entity.id}`}
    placeholder="Enter NIF, phone, email, or vehicle identifier"
    value={entity.value}
    onChange={(e) => updateEntity(entity.id, 'value', e.target.value)}
  />
</div>

<div className="flex-grow space-y-2">
  <label htmlFor={`entityType-${entity.id}`} className="text-sm font-medium">
    Select Entity Type
  </label>
  <Select value={entity.type} onValueChange={(value) => updateEntity(entity.id, 'type', value)}>
    <SelectTrigger id={`entityType-${entity.id}`}>
      <SelectValue placeholder="Select entity type" />
    </SelectTrigger>
    <SelectContent>
      {entityTypes.map((type) => (
        <SelectItem key={type} value={type}>{type}</SelectItem>
      ))}
    </SelectContent>
  </Select>
</div>

{entities.length > 1 && (
  <Button
    variant="ghost"
    size="icon"
    onClick={() => removeEntity(entity.id)}
    className="mb-2"
    aria-label="Remove entity"
  >
    <X className="h-4 w-4" />
  </Button>
)}
</div>
))}

<div className="flex justify-between items-center">
<Button
variant="outline"
size="sm"
onClick={addEntity}
className="flex items-center"
>
<Plus className="h-4 w-4 mr-2" />
Add Entity
</Button>

<Button
onClick={handleGenerateNetwork}
disabled={isGenerateDisabled}
>
{isLoading ? 'Generating...' : 'Generate Network'}
</Button>
</div>
</CardContent>
</Card>

{error && (
<Alert variant="destructive">
<AlertTriangle className="h-4 w-4" />
<AlertTitle>Error</AlertTitle>
<AlertDescription>{error}</AlertDescription>
</Alert>
)}

{relationships.length > 0 ? (
<>
<Card>
<CardHeader>
<CardTitle>Network Graph</CardTitle>
</CardHeader>
<CardContent>
<div className="h-[500px] w-full">
  <ForceGraph2D
    graphData={graphData}
    nodeLabel="id"
    nodeColor={getNodeColor}
    linkColor={() => 'rgba(0,0,0,0.2)'}
    nodeCanvasObject={nodeCanvasObject}
    nodeCanvasObjectMode={() => 'replace'}
  />
</div>
<div className="mt-4">
  <h3 className="text-lg font-semibold mb-2">Legend</h3>
  <div className="flex flex-wrap gap-4">
    {entityTypes.map((type, index) => (
      <div key={type} className="flex items-center">
        <div
          className="w-4 h-4 rounded-full mr-2"
          style={{ backgroundColor: `hsl(${index * 90}, 70%, 50%)` }}
        ></div>
        <span>{type}</span>
      </div>
    ))}
  </div>
</div>
</CardContent>
</Card>

<Card>
<CardHeader>
<CardTitle>Network Analysis</CardTitle>
</CardHeader>
<CardContent>
<Alert>
  <AlertTriangle className="h-4 w-4" />
  <AlertTitle>Potential Fraud Indicators</AlertTitle>
  <AlertDescription>
    <pre className="whitespace-pre-wrap font-sans">{networkAnalysis}</pre>
  </AlertDescription>
</Alert>
</CardContent>
</Card>
</>
) : relationships.length === 0 && !isLoading && !error ? (
<Card>
<CardContent>
<p className="text-center py-4">No relationships found between the entities.</p>
</CardContent>
</Card>
) : null}
</div>
)
}

