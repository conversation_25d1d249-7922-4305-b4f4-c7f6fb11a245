"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { HistoricCaseSearch } from "@/components/complex-claims/historic-case-search"
import { SimilarClaimsSearch } from "@/components/complex-claims/similar-claims-search"
import { CaseAnalysisDashboard } from "@/components/complex-claims/case-analysis-dashboard"

export default function ComplexClaimsAnalysisPage() {
  const [activeTab, setActiveTab] = useState<string>("historic-case")

  const modelInfo = {
    title: "Complex Claims Analysis Tool",
    description: "AI-powered tool for analyzing complex insurance claims and finding similar historical cases",
    purpose: [
      "Search for historical cases by URL to retrieve detailed information",
      "Find similar claims based on description to support decision-making",
      "Generate comprehensive case analysis dashboard with strategic insights",
      "Analyze patterns and outcomes in complex insurance claims"
    ],
    methodology: [
      "Natural language processing for claim description analysis",
      "Semantic similarity matching for finding related cases",
      "Document retrieval and information extraction from legal databases",
      "Strategic case analysis with AI-powered insights and recommendations"
    ],
    outputs: [
      "Detailed case information with key facts and decisions",
      "Similar cases ranked by relevance",
      "Comprehensive case analysis dashboard with strategic insights",
      "Analysis of legal reasoning and outcomes"
    ],
    type: "tool" as const
  }

  const apiInfo = {
    toolName: "Complex Claims Analysis Tool",
    endpoint: "complex-claims-analysis",
    requestExample: {
      type: "url_search",
      url: "https://www.dgsi.pt/jtrl.nsf/33182fc732316039802565fa00497eec/672fb9b269be9b2980258b63004fc517?OpenDocument"
    },
    responseExample: {
      case_info: {
        process_number: "21154/19.7T8LSB.L1-2",
        court_name: "Tribunal da Relacao de Lisboa",
        decision_date: "2024-11-07",
        decision_type: "IMPROCEDENTE",
        judge: "CARLOS CASTELO BRANCO",
        keywords: ["RESPONSABILIDADE CIVIL", "PERDA DE CHANCE", "ADVOGADO", "SEGURO", "INDEMNIZACAO"]
      },
      parties: [
        { name: "A", role: "plaintiff", type: "person" },
        { name: "B", role: "defendant", type: "lawyer" },
        { name: "MAPFRE - SEGUROS GERAIS, S.A.", role: "third_party", type: "insurance_company" }
      ],
      summary: "Insurance case where the court ruled against the insurance company, stating it was responsible for compensating the plaintiff for damages resulting from the negligence of the defendant lawyer."
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Complex Claims Analysis Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>

      <Tabs defaultValue="historic-case" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="historic-case">Historic Case Search</TabsTrigger>
          <TabsTrigger value="similar-claims">Similar Claims Search</TabsTrigger>
          <TabsTrigger value="case-analysis">Case Analysis Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="historic-case" className="space-y-6">
          <HistoricCaseSearch />
        </TabsContent>

        <TabsContent value="similar-claims" className="space-y-6">
          <SimilarClaimsSearch />
        </TabsContent>

        <TabsContent value="case-analysis" className="space-y-6">
          <CaseAnalysisDashboard />
        </TabsContent>
      </Tabs>
    </div>
  )
}
