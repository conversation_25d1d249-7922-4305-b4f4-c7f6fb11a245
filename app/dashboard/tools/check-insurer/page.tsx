"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckInsurerForm } from "@/components/check-insurer-form"
import { CheckInsurerResults } from "@/components/check-insurer-results"
import { ModelInfoDialog } from "@/components/model-info-dialog"
import { APIDialog } from "@/components/api-dialog"

export default function CheckInsurerPage() {
  const [insurerResults, setInsurerResults] = useState<any>(null);

  const handleSubmit = (checkInfo: any) => {
    // Mock results
    const today = new Date();
    const endDate = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
    setInsurerResults({
      insuranceCompany: "SafeDrive Insurance Co.",
      policyNumber: "PL-" + Math.floor(100000 + Math.random() * 900000),
      startDate: today.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    });
  };

  const modelInfo = {
    title: "Check Insurer Model",
    description: "A model that retrieves insurance information for a vehicle based on the license plate and date.",
    type: "lookup" as const,
    accuracy: 99,
    purpose: [
      "Verify insurance coverage for specific vehicles",
      "Retrieve policy details for claims processing",
      "Assist in fraud detection by cross-referencing insurance information",
      "Support law enforcement and regulatory compliance checks"
    ],
    methodology: [
      "Queries a comprehensive database of vehicle insurance records",
      "Cross-references license plate with insurance policy databases",
      "Verifies active policy status for the given date",
      "Ensures data privacy and security in compliance with regulations"
    ],
    outputs: [
      "Insurance Company Name",
      "Policy Number",
      "Policy Start Date",
      "Policy End Date"
    ]
  };

  const apiInfo = {
    modelName: "Check Insurer Model",
    endpoint: "check-insurer",
    requestExample: {
      license_plate: "ABC123",
      check_date: "2023-05-15"
    },
    responseExample: {
      insurance_company: "SafeDrive Insurance Co.",
      policy_number: "PL-123456",
      start_date: "2023-01-01",
      end_date: "2023-12-31",
      status: "Active",
      processing_time: "0.05s"
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Check Insurer Model</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Information</CardTitle>
            <CardDescription>Enter the license plate and date to check insurance</CardDescription>
          </CardHeader>
          <CardContent>
            <CheckInsurerForm onSubmit={handleSubmit} />
          </CardContent>
        </Card>
        {insurerResults && (
          <CheckInsurerResults results={insurerResults} />
        )}
      </div>
    </div>
  )
}

