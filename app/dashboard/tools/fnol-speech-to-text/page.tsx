import { FNOLSpeechToTextTool } from "@/components/fnol-speech-to-text-tool"
import { APIDialog } from "@/components/api-dialog"
import { ModelInfoDialog } from "@/components/model-info-dialog"

export default function FNOLSpeechToTextPage() {
  const apiInfo = {
    toolName: "FNOL Speech-to-Text Tool",
    endpoint: "fnol-speech-to-text",
    requestExample: {
      audioFile: "base64_encoded_audio"
    },
    responseExample: {
      transcription: "On July 15th, I was involved in a car accident...",
      claimDetails: {
        date: "2023-07-15",
        location: "Intersection of Main St and 5th Ave, New York, NY",
        vehiclesInvolved: 2,
        injuries: "Minor"
      },
      detectedFlags: [
        { flag: "Potential Fraud", confidence: 0.15 },
        { flag: "Potential Litigation", confidence: 0.35 },
        { flag: "Unhappy Customer", confidence: 0.75 }
      ]
    }
  }

  const modelInfo = {
    title: "FNOL Speech-to-Text Tool",
    description: "This tool transcribes FNOL call recordings, extracts key claim details, and detects potential flags.",
    type: "tool" as const,
    purpose: [
      "Transcribe FNOL call recordings",
      "Extract key claim information",
      "Identify potential flags for further investigation"
    ],
    methodology: [
      "Uses advanced speech recognition to transcribe audio",
      "Applies natural language processing to extract claim details",
      "Utilizes sentiment analysis and keyword detection for flag identification"
    ],
    outputs: [
      "Call transcription",
      "Structured claim details (date, location, etc.)",
      "Detected flags with confidence scores"
    ]
  }

  return (


    <div className="container mx-auto py-6">
          <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">FNOL Speech-to-Text Tool</h1>
        <div className="flex gap-2">
          <ModelInfoDialog {...modelInfo} />
          <APIDialog {...apiInfo} />
        </div>
      </div>
      <FNOLSpeechToTextTool />
    </div>
  )
}

