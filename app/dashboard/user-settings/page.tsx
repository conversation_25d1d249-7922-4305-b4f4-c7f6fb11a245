'use client'

import { useState, useEffect } from 'react'
import axios from 'axios'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useSearchParams } from 'next/navigation';
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useSession } from 'next-auth/react'
import { ApiKey } from '@/types/user'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import ProfilePic from '@/components/User/profile-pic'
import BuyTokens from '@/components/settings/buy-tokens'
import { useToast } from "@/hooks/use-toast"

export default function UserSettingsPage() {
  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [externalApis, setExternalApis] = useState<{ id: string; name: string }[]>([]);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  
  const [selectedApi, setSelectedApi] = useState('')
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [userId, setUserId] = useState('') 
  const { data: session } = useSession()

  const { toast } = useToast()

  const searchParams = useSearchParams();

  useEffect(() => {
    console.log(searchParams)
    if (searchParams?.get('success') === 'true') {
      alert('Payment completed successfully! Tokens have been added to your account.');
    }

    if (searchParams?.get('canceled') === 'true') {
      alert('Payment was canceled. No tokens were added to your account.');
    }
  }, [searchParams]);

  const fetchUserData = async (id: string) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${id}`)
      // setOldPassword(response.data.password)
      setUsername(response.data.username)
      setEmail(response.data.email)
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  useEffect(() => {
    if (session?.user?.id) {
      fetchUserData(session.user.id)
      setUserId(session.user.id)
    }
  }, [session])

  const handleUpdateProfile = async (event: React.FormEvent) => {
    event.preventDefault()
    try {
      const response = await axios.put(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        username,
        email,
      })
      console.log('Profile updated:', response.data)
      alert('Profile updated successfully')
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile')
    }
  }


  const handleChangePassword = async (event: React.FormEvent) => {
    event.preventDefault()
    if (newPassword !== confirmPassword) {
      alert('Passwords do not match')
      return
    }
  
    try {
      const response = await axios.put(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/update-password`, {
        user_id: userId,
        old_password: currentPassword,
        new_password: newPassword
      })
      console.log('Password changed:', response.data)
      alert('Password changed successfully')
      // Clear the password fields
      setCurrentPassword('')
      setNewPassword('')
      setConfirmPassword('')
    } catch (error: any) {
      console.error('Error changing password:', error)
      if (error.response?.data?.detail) {
        alert(error.response.data.detail)
      } else {
        alert('Failed to change password')
      }
    }
  }


  useEffect(() => {
    // Fetch all external APIs
    const fetchExternalApis = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-apis/all`)
        setExternalApis(response.data)
      } catch (error) {
        console.error('Error fetching external APIs:', error)
      }
    }

    // Fetch API keys for the user
    const fetchApiKeys = async () => {
      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/api-keys/user/${userId}`)
        setApiKeys(response.data)
      } catch (error) {
        console.error('Error fetching API keys:', error)
      }
    }

    if (userId) {
      fetchExternalApis()
      fetchApiKeys()
    }
  }, [userId])

  const handleGenerateApiKey = async () => {
    if (!selectedApi) {
      alert('Please select an API')
      return
    }

    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/api-keys`, {
        external_api_id: selectedApi,
        user_id: userId,
      })
      setApiKeys([...apiKeys, response.data])
    } catch (error) {
      console.error('Error generating API key:', error)
    }
  }

  const handleDeleteApiKey = async (apiKeyId: string) => {
    try {
      await axios.delete(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/api-keys/${apiKeyId}`)
      setApiKeys(apiKeys.filter((key: any) => key.id !== apiKeyId))
    } catch (error) {
      console.error('Error deleting API key:', error)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">User Settings</h1>
      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="buy-tokens">Buy API Tokens</TabsTrigger>

        </TabsList>
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your profile details here.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdateProfile} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="avatar">Profile Picture</Label>
                  <ProfilePic />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input id="username" value={username} onChange={(e) => setUsername(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                </div>
                <Button type="submit">Update Profile</Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Ensure your account is secure by using a strong password.</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleChangePassword} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <Input id="current-password" type="password" value={currentPassword} onChange={(e) => setCurrentPassword(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <Input id="new-password" type="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <Input id="confirm-password" type="password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} />
                </div>
                <Button type="submit">Change Password</Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        {/* API Key managment */}
        <TabsContent value="api-keys">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>Manage your API keys here. Keep these secret and secure.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Label htmlFor="api-dropdown">Select API</Label>
                    <Select value={selectedApi} onValueChange={setSelectedApi}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an API" />
                      </SelectTrigger>
                      <SelectContent>
                        {externalApis.map((api) => (
                          <SelectItem key={api.id} value={api.id}>
                            {api.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button 
                    onClick={handleGenerateApiKey}
                    className="mt-6"
                  >
                    Generate New API Key
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>API Name</TableHead>
                        <TableHead>API Key</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {apiKeys.map((key) => (
                        <TableRow key={key.id}>
                          <TableCell>{key.external_api_name}</TableCell>
                          <TableCell>
                            <code className="rounded bg-muted px-2 py-1">
                              {key.api_key}
                            </code>
                          </TableCell>
                          <TableCell>
                            {new Date(key.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteApiKey(key.id)}
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="buy-tokens" className="space-y-6">
          <BuyTokens />
        </TabsContent>
      </Tabs>
    </div>
  )
}
