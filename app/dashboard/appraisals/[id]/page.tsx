import { AppraisalDetails } from "@/components/appraisal-details"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft } from 'lucide-react'

export default function AppraisalPage({ params }: { params: { id: string } }) {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button asChild variant="outline">
            <Link href="/appraisals">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Appraisals
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">Appraisal Details</h1>
        </div>
      </div>
      <AppraisalDetails appraisalId={params.id} />
    </div>
  )
}

