import ClientsDashboard from '@/components/clients-dashboard'
import { getServerSession } from 'next-auth'
import { options } from '../../api/auth/[...nextauth]/options'

export default async function Page() {

  const session = await getServerSession(options)

  return (
    <>
      {session ? (
        <div>
          <h1 className="text-3xl font-bold mb-6">Clients Dashboard</h1>
          <ClientsDashboard />
        </div>
      ) : (
        <div>
          <h1 className="text-3xl font-bold mb-6">You are not logged in.</h1>
        </div>
      )}
    </>
  )
}


