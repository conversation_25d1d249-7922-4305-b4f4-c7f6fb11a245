import { UsageDashboard } from "@/components/usage-dashboard"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import UserManagement from "@/components/admin/UserManagement"
import CreditManagement from "@/components/admin/CreditManagement"
import PermissionManagement from "@/components/admin/PermissionManagement"
import { getServerSession } from "next-auth"
import { options } from "@/app/api/auth/[...nextauth]/options"
import { redirect } from "next/navigation"
import type { UserRole } from '@/types/user'


export default async function DashboardPage() {
    const session = await getServerSession(options)
    
    if (!session) {
        redirect('/login')
    }

    const userRole = session.user?.role as UserRole

    return (
        // User panel
        userRole === 'user' ? (
            <div className="container mx-auto py-6">
                <UsageDashboard />
            </div>
        ) : (
            // admin panel
            <div className="container mx-auto py-10">
                <h1 className="text-3xl font-bold mb-6">Admin Panel</h1>
                <Tabs defaultValue="users">
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="users">User Management</TabsTrigger>
                        <TabsTrigger value="credits">Credit Management</TabsTrigger>
                        <TabsTrigger value="permissions">Permission Management</TabsTrigger>
                    </TabsList>
                    <TabsContent value="users">
                        <UserManagement />
                    </TabsContent>
                    <TabsContent value="credits">
                        <CreditManagement />
                    </TabsContent>
                    <TabsContent value="permissions">
                        <PermissionManagement />
                    </TabsContent>
                </Tabs>
            </div>
        )
    )
}
