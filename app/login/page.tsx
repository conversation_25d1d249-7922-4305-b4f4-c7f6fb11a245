"use client"

import Image from "next/image"
import { useState, FormEvent } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { FcGoogle } from 'react-icons/fc'
import { BorderBeam } from "@/components/ui/border-beam"
import { GridPattern } from "@/components/ui/retro-background"
import { cn } from "@/lib/utils"

export default function LoginPage() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    remember: false
  })
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    try {
      const result = await signIn('credentials', {
        username: formData.username,
        password: formData.password,
        redirect: false,
      })

      if (result?.error) {
        setError('Invalid username or password')
      } else if (result?.ok) {
        router.replace('/dashboard')
      }
    } catch (error) {
      setError('An error occurred during sign in')
      console.error('Sign in error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = () => {
    signIn('google', { 
      redirect: true,
      callbackUrl: '/dashboard'
    });
  };

  return (
    <div className="min-h-screen bg-[#162338] relative overflow-hidden flex items-center justify-center p-4">
      {/* Adjusted GridPattern to fill the page with more visible lines */}
      <GridPattern 
        width={30}
        height={30}
        squares={[
          [1, 1], [3, 3], [5, 5], [7, 7], [9, 9], [11, 11],
          [2, 6], [4, 8], [6, 10], [8, 12], [10, 14],
          [12, 4], [14, 6], [16, 8], [18, 10], [20, 12],
          [5, 15], [10, 5], [15, 20], [20, 10], [25, 5],
          [7, 22], [14, 18], [21, 14], [28, 10],
        ]}
        className={cn(
          "[mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]",
          "absolute inset-0 h-[200%] skew-y-12 -translate-y-1/4",
          "stroke-teal-500/30 fill-transparent opacity-70"
        )}
        strokeDasharray="2 2"
      />
      
      <div className="relative w-full max-w-md bg-slate-900/80 p-8 rounded-lg shadow-xl backdrop-blur-sm overflow-hidden z-10">
        <div className="flex justify-center mb-6">
          <Image 
            src="/login/logo.png" 
            alt="Logo" 
            width={40} 
            height={40} 
            className="h-8 w-auto" 
          />
        </div>
        
        <div className="text-center mb-6">
          <h1 className="text-2xl font-semibold text-indigo-100">Welcome Back</h1>
          <p className="text-sm text-slate-400 mt-1">Sign in to continue to your account</p>
        </div>

        {error && (
          <div className="p-3 mb-4 text-sm text-red-400 bg-red-950/50 border border-red-800 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username" className="text-slate-300">Username</Label>
            <Input
              id="username"
              type="text"
              placeholder="Enter your username"
              value={formData.username}
              onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              required
              className="bg-slate-800 border-slate-700 text-slate-200 focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="password" className="text-slate-300">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter your password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required
                className="bg-slate-800 border-slate-700 text-slate-200 focus:border-indigo-500 focus:ring-indigo-500"
              />
              <Button
                type="button"
                variant="ghost"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent text-slate-400"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="w-4 h-4">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" className="w-4 h-4">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={formData.remember}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, remember: checked as boolean })
                }
                className="border-slate-600 data-[state=checked]:bg-indigo-600"
              />
              <label
                htmlFor="remember"
                className="text-sm text-slate-300 leading-none"
              >
                Keep me logged in
              </label>
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium py-2" 
            disabled={loading}
          >
            {loading ? 'Signing in...' : 'Sign in'}
          </Button>
        </form>

        <div className="relative mt-6 mb-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-slate-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-slate-900 px-2 text-slate-500">
              OR CONTINUE WITH
            </span>
          </div>
        </div>
        <Button
          type="button"
          variant="outline"
          className="w-full border-slate-700 text-slate-300 hover:bg-slate-800 hover:text-slate-200"
          onClick={handleGoogleSignIn}
        >
          
          <FcGoogle className="mr-2 h-5 w-5" />
          <span className="text-slate-900">Sign in with Google</span>
        </Button>
        
        <BorderBeam 
          duration={6} 
          size={400}
          colorFrom="transparent"
          colorTo="#14b8a6"
        />
        <BorderBeam 
          duration={6}
          delay={3}
          size={400}
          colorFrom="transparent"
          colorTo="#fb923c"
        />
      </div>
    </div>
  )
}
