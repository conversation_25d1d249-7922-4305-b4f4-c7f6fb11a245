'use client'

import React, { useEffect } from 'react';
import <PERSON><PERSON> from '@studio-freight/lenis'
import { Car, FileCheck, Zap, Shield} from 'lucide-react';
import { motion } from 'framer-motion';
import Header from "@/components/layout/Header";

const assistants = [
  {
    title: "Fraud Detection Assistant",
    subtitle: "Uncover Fraud Before It Happens",
    icon: Shield,
    description: "Our Fraud Detection Assistant performs deep analysis to identify fraudulent claims with precision. It connects to a wide array of internal and external data sources, analyzing policies, appraisals, historical claims, weather reports, geographical data, vehicle history databases, debt records, and even scans for social media activity and public records.",
    features: [
      "Cross-references policyholder information with external databases",
      "Detects manipulated documents, including photos and invoices",
      "Conducts network analysis to uncover hidden relationships",
      "Real-time risk scoring and fraud probability assessment"
    ],
    color: "blue"
  },
  {
    title: "Vehicle Appraisal Assistant",
    subtitle: "Accurate, Instant Vehicle Appraisals",
    icon: Car,
    description: "This Assistant leverages AI to streamline vehicle appraisals, ensuring fast, accurate evaluations. It uses image recognition to assess car damage, scrapes market data to determine real-time vehicle value, and provides repair cost predictions based on historical data and market rates.",
    features: [
      "Integrates with repair shop systems to validate cost estimates",
      "Uses historical appraisal data to provide benchmarks",
      "Compares vehicle condition with similar cases in the database",
      "Generates comprehensive damage assessment reports"
    ],
    color: "emerald"
  },
  {
    title: "FNOL Assistant",
    subtitle: "Effortless First Notice of Loss Reporting",
    icon: FileCheck,
    description: "Our FNOL Assistant simplifies the claims initiation process by automating and enriching data collection. It guides customers to provide necessary claim details via forms or chat, pulls in external data like weather and location details, and automatically checks the claim against policy coverage.",
    features: [
      "Automates the entire FNOL process for faster turnaround",
      "Ensures data accuracy by cross-referencing multiple sources",
      "Prepares detailed, ready-to-review claim reports",
      "Intelligent routing based on claim characteristics"
    ],
    color: "purple"
  }
];

const Services = () => {
  
  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    })

    function raf(time: number) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    document.documentElement.classList.add('lenis', 'lenis-smooth')

    return () => {
      lenis.destroy()
      document.documentElement.classList.remove('lenis', 'lenis-smooth')
    }
  }, [])

  return (
    <>
      <Header />
      
      <div className="bg-[#141b2b] min-h-screen pt-24">
        {/* Hero Section */}
        <div className="relative overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 py-24">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                Meet Your AI Assistants
              </h1>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto">
                Specialized AI companions designed to revolutionize operations. Each Assistant is a focused expert in their domain, equipped with cutting-edge AI and deep integrations to deliver unmatched insights and efficiency.
              </p>
            </motion.div>
          </div>

          {/* Background Effects */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-[#0be5a9]/5 mix-blend-overlay" />
            <div className="absolute top-0 right-0 w-[500px] h-[500px] bg-gradient-to-b from-[#0be5a9]/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-gradient-to-t from-[#f69323]/20 to-transparent rounded-full blur-3xl" />
          </div>
        </div>

        {/* Assistants Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-24">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {assistants.map((assistant, index) => (
              <motion.div
                key={assistant.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-[#1a2436] rounded-xl p-8 border border-[#0be5a9]/20 hover:border-[#0be5a9]/40 transition-colors group"
              >
                <div className="w-16 h-16 bg-[#0be5a9]/10 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <assistant.icon className="w-8 h-8 text-[#0be5a9]" />
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-2">{assistant.title}</h3>
                <p className="text-[#0be5a9] mb-4">{assistant.subtitle}</p>
                
                <p className="text-gray-400 mb-6">
                  {assistant.description}
                </p>
                
                <div className="space-y-3">
                  {assistant.features.map((feature, i) => (
                    <div key={i} className="flex items-center gap-2">
                      <Zap className="w-4 h-4 text-[#0be5a9]" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Integration Section */}
          <div className="mt-24 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Operations?
            </h2>
            <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
              Our AI assistants work seamlessly together, creating a powerful ecosystem for your business.
            </p>
            <button className="bg-[#f69323] text-white px-8 py-3 rounded-full font-medium hover:bg-[#f69323]/90 transition-colors">
              Get Started Today
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Services;