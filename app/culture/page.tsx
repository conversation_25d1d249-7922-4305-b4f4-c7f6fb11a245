'use client'

import { useEffect } from 'react'
import Lenis from '@studio-freight/lenis'
import Header from "@/components/layout/Header"
// import HeroSection from "@/components/culture/HeroSection"
import RoadmapSection from "@/components/culture/RoadmapSection"
import TeamSection from "@/components/culture/TeamSection"
import ValuesSection from "@/components/culture/ValuesSection"

export default function CulturePage() {
  
  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    })

    function raf(time: number) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    document.documentElement.classList.add('lenis', 'lenis-smooth')

    return () => {
      lenis.destroy()
      document.documentElement.classList.remove('lenis', 'lenis-smooth')
    }
  }, [])

  return (
    <div className="bg-gray-900 min-h-screen flex flex-col">
      <Header />
      <div className="h-20"></div>
      {/* <HeroSection /> */}
      <div className="h-20"></div>
      <RoadmapSection />
      <div className="h-14"></div>
      <TeamSection />
      <ValuesSection />
    </div>
  )
} 