/* eslint-disable @typescript-eslint/no-unused-vars */
'use client'

import Link from "next/link"
import { useState, useEffect } from 'react'
import Header from "@/components/layout/Header"
import Banner from "@/components/layout/Banner"
import HeroSection from "@/components/layout/HeroSection"
import Lenis from '@studio-freight/lenis'
import LoadingScreen from "@/components/layout/LoadingScreen"
import ProblemSolution from "@/components/layout/ProblemSolution"
import Features from "@/components/homepage/Feature"
import GetStarted from "@/components/homepage/GetStarted"
import {Mail, Phone, Facebook, Twitter, Linkedin} from 'lucide-react'
import FAQ from "@/components/layout/FAQ"
export default function HomePage() {
  const [loadingComplete, setLoadingComplete] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const sectionVisibility = {
    hero: { desktop: true, mobile: true },
    keyBenefits: { desktop: true, mobile: true },
    partnerLogos: { desktop: false, mobile: false },
    coreFeatures: { desktop: true, mobile: true },
    aiTaskAutomation: { desktop: true, mobile: true },
    howItWorks: { desktop: true, mobile: true },
    externalDataSources: { desktop: true, mobile: true },
    aiAssistants: { desktop: true, mobile: true },
    sdk: { desktop: true, mobile: true },
    pricing: { desktop: false, mobile: false },
    customerSuccess: { desktop: true, mobile: true },
    metricsOfSuccess: { desktop: true, mobile: true },
    faqs: { desktop: true, mobile: true },
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 50); // Reduced delay

    // Add a timer for when loading is completely finished (including transitions)
    const loadingCompleteTimer = setTimeout(() => {
      setLoadingComplete(true);
    }, 50); // Reduced delay (500ms for content + 1000ms for transition)

    return () => {
      clearTimeout(timer);
      clearTimeout(loadingCompleteTimer);
    };
  }, []);

  useEffect(() => {
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    })

    function raf(time: number) {
      lenis.raf(time)
      requestAnimationFrame(raf)
    }

    requestAnimationFrame(raf)

    document.documentElement.classList.add('lenis', 'lenis-smooth')

    return () => {
      lenis.destroy()
      document.documentElement.classList.remove('lenis', 'lenis-smooth')
    }
  }, [])

  return (
    <div className="bg-gray-900 min-h-screen flex flex-col">
      {/* <LoadingScreen /> */}
      
      <Header />

      {sectionVisibility.hero.desktop && sectionVisibility.hero.mobile && (
        <HeroSection loadingComplete={loadingComplete} />
      )}

      {(sectionVisibility.keyBenefits.desktop || sectionVisibility.keyBenefits.mobile) && (
        <section className={`${
          sectionVisibility.keyBenefits.desktop ? 'sm:block' : 'sm:hidden'
        } ${
          sectionVisibility.keyBenefits.mobile ? 'block' : 'hidden'
        }`}>
          <ProblemSolution />
          <Banner />
          <Features />
          <GetStarted />
          <FAQ />
        </section>
      )}

      <footer className="bg-gray-900 text-gray-300 py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Navigation</h3>
              <ul className="space-y-2">
                {['Home', 'Features', 'Culture', 'Pricing', 'About Us', 'Blog', 'Contact', 'API Documentation'].map((item) => (
                  <li key={item}>
                    <Link href={`/${item.toLowerCase().replace(' ', '-')}`} className="hover:text-blue-400 transition-colors">
                      {item}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/privacy-policy" className="hover:text-blue-400 transition-colors">
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms-of-service" className="hover:text-blue-400 transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Connect With Us</h3>
              <div className="flex space-x-4">
                <Link href="https://linkedin.com" className="hover:text-blue-400 transition-colors">
                  <Linkedin className="h-6 w-6" />
                  <span className="sr-only">LinkedIn</span>
                </Link>
                <Link href="https://twitter.com" className="hover:text-blue-400 transition-colors">
                  <Twitter className="h-6 w-6" />
                  <span className="sr-only">Twitter</span>
                </Link>
                <Link href="https://facebook.com" className="hover:text-blue-400 transition-colors">
                  <Facebook className="h-6 w-6" />
                  <span className="sr-only">Facebook</span>
                </Link>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white">Contact Us</h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Mail className="h-5 w-5 mr-2" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors"><EMAIL></a>
                </li>
                <li className="flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  <a href="tel:+1234567890" className="hover:text-blue-400 transition-colors">+1 (234) 567-890</a>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p>&copy; {new Date().getFullYear()} Rekover AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
      <div className="mb-16"></div>
    </div>
  )
}

