'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Mail, Calendar, MapPin, MessageSquare, Send, Clock, User, Briefcase } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Calend<PERSON><PERSON>utton } from "@/components/ui/calendly-button"
import Header from '@/components/layout/Header'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ContactPage() {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [company, setCompany] = useState('')
  const [demoDate, setDemoDate] = useState('')
  const [demoTime, setDemoTime] = useState('')

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [submitError, setSubmitError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitError('')

    try {
      const response = await fetch('/api/v1/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          subject,
          message,
          timestamp: new Date().toISOString(),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      // Success
      setSubmitSuccess(true)

      // Reset form
      setName('')
      setEmail('')
      setSubject('')
      setMessage('')

      // Reset success message after 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false)
      }, 5000)
    } catch (error) {
      console.error('Error sending message:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to send message')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDemoSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle demo booking logic here
    console.log({ name, email, company, demoDate, demoTime })
    // Reset form
    setName('')
    setEmail('')
    setCompany('')
    setDemoDate('')
    setDemoTime('')
  }

  // Load Calendly script
  useEffect(() => {
    const head = document.querySelector('head');
    const script = document.createElement('script');
    script.setAttribute('src', 'https://assets.calendly.com/assets/external/widget.js');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('async', 'true');
    head?.appendChild(script);

    return () => {
      // Clean up the script when component unmounts
      if (head?.contains(script)) {
        head.removeChild(script);
      }
    };
  }, []);

  // Background circle animation variants
  const circleVariants = {
    initial: { scale: 0.8, opacity: 0.5 },
    animate: {
      scale: 1,
      opacity: 0.8,
      transition: {
        duration: 8,
        repeat: Infinity,
        repeatType: "reverse" as const,
        ease: "easeInOut"
      }
    }
  }

  // Card animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.2 * i,
        duration: 0.5,
        ease: "easeOut",
        when: "beforeChildren",
      }
    }),
    hover: {
      y: -5,
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
      transition: { duration: 0.2 }
    }
  }

  // Icon animation variants
  const iconVariants = {
    initial: { scale: 1 },
    hover: {
      scale: 1.15,
      rotate: 5,
      transition: { duration: 0.3, type: "spring", stiffness: 300 }
    }
  }

  return (
    <>
      <Header />

      <div className="bg-[#141b2b] min-h-screen flex flex-col items-center justify-center pt-24 pb-16 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-[#0be5a9]/5 mix-blend-overlay" />
          <motion.div
            variants={circleVariants}
            initial="initial"
            animate="animate"
            className="absolute top-0 right-0 w-[600px] h-[600px] bg-gradient-to-b from-[#0be5a9]/20 to-transparent rounded-full blur-3xl"
          />
          <motion.div
            variants={circleVariants}
            initial="initial"
            animate="animate"
            transition={{ delay: 2 }}
            className="absolute bottom-0 left-0 w-[600px] h-[600px] bg-gradient-to-t from-[#f69323]/20 to-transparent rounded-full blur-3xl"
          />
        </div>

        {/* Main Title */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="w-full max-w-7xl mx-auto px-4 sm:px-6 text-center mb-20"
        >
          <motion.h1
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-5xl md:text-6xl font-bold text-white mb-6"
          >
            Contact Us
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-xl text-gray-400 max-w-3xl mx-auto"
          >
            We're here to help. Reach out to our team for support, inquiries, or just to say hello.
          </motion.p>
        </motion.div>

        {/* Contact Cards */}
        <div className="w-full max-w-5xl mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 lg:gap-8">
          {/* Email Card */}
          <Dialog>
            <DialogTrigger asChild>
              <motion.div
                custom={0}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                className="bg-[#1a2436] rounded-xl p-6 border border-[#0be5a9]/20 hover:border-[#0be5a9]/40 transition-colors group cursor-pointer"
              >
                <motion.div
                  className="w-14 h-14 bg-[#0be5a9]/10 rounded-xl flex items-center justify-center mb-4"
                  variants={iconVariants}
                  initial="initial"
                  whileHover="hover"
                >
                  <Mail className="w-7 h-7 text-[#0be5a9]" />
                </motion.div>
                <h3 className="text-xl font-bold text-white mb-1">Email</h3>
                <p className="text-[#0be5a9] text-sm mb-2">Send us a message</p>
                <p className="text-gray-400 text-sm">
                  <span className="hover:text-[#0be5a9] transition-colors">
                    <EMAIL>
                  </span>
                </p>
              </motion.div>
            </DialogTrigger>
            <DialogContent className="bg-[#1a2436] border border-[#0be5a9]/20 text-white">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold">Send Us an Email</DialogTitle>
                <DialogDescription className="text-gray-400">
                  Fill out this form and we'll get back to you as soon as possible.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-6 mt-4">
                <div>
                  <Label htmlFor="name" className="text-gray-300">Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                    className="bg-[#141b2b] border-gray-700 text-white focus:border-[#0be5a9] focus:ring-[#0be5a9]"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-gray-300">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="bg-[#141b2b] border-gray-700 text-white focus:border-[#0be5a9] focus:ring-[#0be5a9]"
                  />
                </div>
                <div>
                  <Label htmlFor="subject" className="text-gray-300">Subject</Label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    required
                    className="bg-[#141b2b] border-gray-700 text-white focus:border-[#0be5a9] focus:ring-[#0be5a9]"
                  />
                </div>
                <div>
                  <Label htmlFor="message" className="text-gray-300">Message</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    rows={5}
                    className="bg-[#141b2b] border-gray-700 text-white focus:border-[#0be5a9] focus:ring-[#0be5a9]"
                  />
                </div>
                {submitSuccess && (
                  <div className="bg-green-500/20 border border-green-500 text-green-500 p-3 rounded-md text-center">
                    Message sent successfully! We'll get back to you soon.
                  </div>
                )}

                {submitError && (
                  <div className="bg-red-500/20 border border-red-500 text-red-500 p-3 rounded-md text-center">
                    {submitError}
                  </div>
                )}

                <motion.div whileHover={{ scale: isSubmitting ? 1 : 1.02 }} whileTap={{ scale: isSubmitting ? 1 : 0.98 }}>
                  <Button
                    type="submit"
                    className="w-full bg-[#0be5a9] hover:bg-[#0be5a9]/90 text-black font-medium"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" /> Send Message
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>
            </DialogContent>
          </Dialog>

          {/* Book a Demo Card */}
          <motion.div
            custom={1}
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            className="bg-[#1a2436] rounded-xl p-6 border border-[#f69323]/20 hover:border-[#f69323]/40 transition-colors group cursor-pointer"
            onClick={() => {
              if (typeof window !== 'undefined' && window.Calendly) {
                window.Calendly.initPopupWidget({
                  url: 'https://calendly.com/francisco-rekover/30min?primary_color=e3af13'
                });
              }
            }}
          >
            <motion.div
              className="w-14 h-14 bg-[#f69323]/10 rounded-xl flex items-center justify-center mb-4"
              variants={iconVariants}
              initial="initial"
              whileHover="hover"
            >
              <Calendar className="w-7 h-7 text-[#f69323]" />
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-1">Book a Demo</h3>
            <p className="text-[#f69323] text-sm mb-2">See our solutions in action</p>
            <p className="text-gray-400 text-sm">
              <span className="hover:text-[#f69323] transition-colors">
                Schedule a time
              </span>
            </p>
          </motion.div>

          {/* Location Card
          <Dialog>
            <DialogTrigger asChild>
              <motion.div
                custom={2}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                className="bg-[#1a2436] rounded-xl p-6 border border-[#0be5a9]/20 hover:border-[#0be5a9]/40 transition-colors group cursor-pointer"
              >
                <motion.div
                  className="w-14 h-14 bg-[#0be5a9]/10 rounded-xl flex items-center justify-center mb-4"
                  variants={iconVariants}
                  initial="initial"
                  whileHover="hover"
                >
                  <MapPin className="w-7 h-7 text-[#0be5a9]" />
                </motion.div>
                <h3 className="text-xl font-bold text-white mb-1">Location</h3>
                <p className="text-[#0be5a9] text-sm mb-2">Visit our office</p>
                <p className="text-gray-400 text-sm">San Francisco, CA</p>
              </motion.div>
            </DialogTrigger>
            <DialogContent className="bg-[#1a2436] border border-[#0be5a9]/20 text-white">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold">Our Locations</DialogTitle>
                <DialogDescription className="text-gray-400">
                  Find the office nearest to you.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6 mt-4">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="bg-[#141b2b] p-6 rounded-lg border border-[#0be5a9]/20"
                >
                  <h4 className="text-lg font-semibold mb-2">San Francisco (HQ)</h4>
                  <p className="text-gray-400 mb-2">123 Tech Avenue, Suite 500<br />San Francisco, CA 94107</p>
                  <p className="text-gray-400">Mon-Fri: 9AM - 6PM PST</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-[#141b2b] p-6 rounded-lg border border-[#0be5a9]/20"
                >
                  <h4 className="text-lg font-semibold mb-2">New York</h4>
                  <p className="text-gray-400 mb-2">456 Madison Ave, 10th Floor<br />New York, NY 10022</p>
                  <p className="text-gray-400">Mon-Fri: 9AM - 6PM EST</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-[#141b2b] p-6 rounded-lg border border-[#0be5a9]/20"
                >
                  <h4 className="text-lg font-semibold mb-2">London</h4>
                  <p className="text-gray-400 mb-2">789 Tech Circle<br />London, UK EC2A 4HE</p>
                  <p className="text-gray-400">Mon-Fri: 9AM - 6PM GMT</p>
                </motion.div>

                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button className="w-full bg-[#0be5a9] hover:bg-[#0be5a9]/90 text-black font-medium">
                    <MapPin className="w-4 h-4 mr-2" /> Get Directions
                  </Button>
                </motion.div>
              </div>
            </DialogContent>
          </Dialog> */}

          {/* Live Chat Card */}
          <Dialog>
            <DialogTrigger asChild>
              <motion.div
                custom={3}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                className="bg-[#1a2436] rounded-xl p-6 border border-[#f69323]/20 hover:border-[#f69323]/40 transition-colors group cursor-pointer"
              >
                <motion.div
                  className="w-14 h-14 bg-[#f69323]/10 rounded-xl flex items-center justify-center mb-4"
                  variants={iconVariants}
                  initial="initial"
                  whileHover="hover"
                >
                  <MessageSquare className="w-7 h-7 text-[#f69323]" />
                </motion.div>
                <h3 className="text-xl font-bold text-white mb-1">Live Chat</h3>
                <p className="text-[#f69323] text-sm mb-2">Real-time support</p>
                <p className="text-gray-400 text-sm">Available 24/7</p>
              </motion.div>
            </DialogTrigger>
            <DialogContent className="bg-[#1a2436] border border-[#f69323]/20 text-white">
              <DialogHeader>
                <DialogTitle className="text-2xl font-bold">Start a Live Chat</DialogTitle>
                <DialogDescription className="text-gray-400">
                  Connect with our support team in real-time.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6 mt-4">
                <div className="bg-[#141b2b] p-6 rounded-lg border border-[#f69323]/20">
                  <h4 className="text-lg font-semibold mb-4">Choose a Department</h4>
                  <div className="space-y-3">
                    <motion.div
                      whileHover={{ x: 5 }}
                      className="flex items-center p-3 bg-[#1a2436] rounded-lg cursor-pointer hover:bg-[#1a2436]/80"
                    >
                      <div className="w-10 h-10 bg-[#f69323]/10 rounded-full flex items-center justify-center mr-3">
                        <MessageSquare className="w-5 h-5 text-[#f69323]" />
                      </div>
                      <div>
                        <p className="font-medium">Technical Support</p>
                        <p className="text-xs text-gray-400">For product issues & troubleshooting</p>
                      </div>
                    </motion.div>

                    <motion.div
                      whileHover={{ x: 5 }}
                      className="flex items-center p-3 bg-[#1a2436] rounded-lg cursor-pointer hover:bg-[#1a2436]/80"
                    >
                      <div className="w-10 h-10 bg-[#f69323]/10 rounded-full flex items-center justify-center mr-3">
                        <Briefcase className="w-5 h-5 text-[#f69323]" />
                      </div>
                      <div>
                        <p className="font-medium">Billing Inquiries</p>
                        <p className="text-xs text-gray-400">For payments and subscription questions</p>
                      </div>
                    </motion.div>

                    <motion.div
                      whileHover={{ x: 5 }}
                      className="flex items-center p-3 bg-[#1a2436] rounded-lg cursor-pointer hover:bg-[#1a2436]/80"
                    >
                      <div className="w-10 h-10 bg-[#f69323]/10 rounded-full flex items-center justify-center mr-3">
                        <User className="w-5 h-5 text-[#f69323]" />
                      </div>
                      <div>
                        <p className="font-medium">Sales Team</p>
                        <p className="text-xs text-gray-400">For product information and pricing</p>
                      </div>
                    </motion.div>
                  </div>
                </div>

                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Button className="w-full bg-[#f69323] hover:bg-[#f69323]/90 text-white font-medium">
                    <MessageSquare className="w-4 h-4 mr-2" /> Start Chat
                  </Button>
                </motion.div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Calendly Widget Section */}
        <div className="w-full max-w-5xl mx-auto px-4 mt-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.4 }}
            className="text-center mb-10"
          >
            <h2 className="text-3xl font-bold text-white mb-4">Schedule a Call</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Book a time directly on our calendar for a personalized consultation
            </p>
          </motion.div>

          {/* Calendly inline widget */}
          <div className="calendly-inline-widget" data-url="https://calendly.com/francisco-rekover/30min?primary_color=ffbf00" style={{minWidth: "320px", height: "700px"}}></div>
        </div>
      </div>
    </>
  )
}