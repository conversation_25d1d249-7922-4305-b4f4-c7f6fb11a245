/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
  domains: ['api.mapbox.com'], 
  domains: ['ireland.apollo.olxcdn.com'],
  domains: ['res.cloudinary.com'],
  domains: ['placehold.co'],
  domains: ['car-appraisal-api.onrender.com'],
  domains: ['i.pravatar.cc'],
},
  reactStrictMode: true,
  webpack: (config) => {
    config.experiments = { ...config.experiments, topLevelAwait: true }
    return config
  },
};

export default nextConfig;
