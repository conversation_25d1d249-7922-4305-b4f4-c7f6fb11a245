import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";
import { JWT } from "next-auth/jwt";

// Extend the JWT type to include our user properties
interface CustomJWT extends JWT {
  user?: {
    role?: string;
  };
}

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token as CustomJWT;
    const isAdminRoute = req.nextUrl.pathname.startsWith('/dashboard/claims');

    // // Protect admin routes
    // if (isAdminRoute && token?.user?.role !== "admin") {
    //   return NextResponse.redirect(new URL("/dashboard", req.url));
    // }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token
    }
  }
);

export const config = {
  matcher: [
    "/dashboard/:path*",
  ]
};
