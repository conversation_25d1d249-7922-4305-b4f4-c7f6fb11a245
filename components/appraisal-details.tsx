"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, Edit, FileText, MessageSquare, DollarSign, PenToolIcon as Tool, Car } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format } from "date-fns"
import { toast } from "@/components/ui/use-toast"
import Link from 'next/link'
import Image from 'next/image'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface Appraisal {
  id: string
  claimId: string
  claimNumber: string
  appraiser: string
  date: Date
  estimatedValue: number
  status: "Pending" | "Completed" | "Disputed"
  repairShop: {
    name: string
    address: string
    phone: string
  }
  vehicle: {
    make: string
    model: string
    year: number
    vin: string
  }
  damages: {
    description: string
    estimatedCost: number
  }[]
  laborCost: number
  partsCost: number
  totalCost: number
  images: string[]
}

interface AppraisalDetailsProps {
  appraisalId: string
}

export function AppraisalDetails({ appraisalId }: AppraisalDetailsProps) {
  const [appraisal, setAppraisal] = useState<Appraisal | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAppraisalDetails = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // In a real application, you would fetch the appraisal data from an API
        // For this example, we'll simulate an API call with setTimeout
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Mock data for the appraisal
        const mockAppraisal: Appraisal = {
          id: appraisalId,
          claimId: "1",
          claimNumber: "CLM-2023-001",
          appraiser: "John Smith",
          date: new Date(),
          estimatedValue: 5000,
          status: "Completed",
          repairShop: {
            name: "AutoFix Repairs",
            address: "123 Mechanic St, Autoville, AV 12345",
            phone: "(*************"
          },
          vehicle: {
            make: "Toyota",
            model: "Camry",
            year: 2020,
            vin: "1HGCM82633A004352"
          },
          damages: [
            { description: "Front bumper replacement", estimatedCost: 800 },
            { description: "Hood repair", estimatedCost: 600 },
            { description: "Headlight replacement", estimatedCost: 400 }
          ],
          laborCost: 1200,
          partsCost: 1600,
          totalCost: 2800,
          images: [
            "/placeholder.svg?height=300&width=400",
            "/placeholder.svg?height=300&width=400",
            "/placeholder.svg?height=300&width=400"
          ]
        }

        setAppraisal(mockAppraisal)
      } catch (err) {
        setError("Failed to fetch appraisal details. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAppraisalDetails()
  }, [appraisalId])

  const handleEditAppraisal = () => {
    toast({
      title: "Edit Appraisal",
      description: "Appraisal editing functionality would be implemented here.",
    })
  }

  const handleAddDocument = () => {
    toast({
      title: "Add Document",
      description: "Document upload functionality would be implemented here.",
    })
  }

  const handleAddComment = () => {
    toast({
      title: "Add Comment",
      description: "Comment addition functionality would be implemented here.",
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-[200px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!appraisal) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Not Found</AlertTitle>
        <AlertDescription>The requested appraisal could not be found.</AlertDescription>
      </Alert>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Appraisal {appraisal.id}</span>
          <Badge variant={appraisal.status === "Completed" ? "default" : appraisal.status === "Pending" ? "secondary" : "destructive"}>
            {appraisal.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="details">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="damages">Damages</TabsTrigger>
            <TabsTrigger value="costs">Costs</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
          </TabsList>
          <TabsContent value="details">
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold">Claim Number</h3>
                  <Link href={`/claims/${appraisal.claimId}`} className="text-blue-500 hover:underline">
                    {appraisal.claimNumber}
                  </Link>
                </div>
                <div>
                  <h3 className="font-semibold">Appraiser</h3>
                  <p>{appraisal.appraiser}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Date</h3>
                  <p>{format(appraisal.date, "PP")}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Estimated Value</h3>
                  <p>{new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(appraisal.estimatedValue)}</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold">Repair Shop</h3>
                <p>{appraisal.repairShop.name}</p>
                <p>{appraisal.repairShop.address}</p>
                <p>{appraisal.repairShop.phone}</p>
              </div>
              <div>
                <h3 className="font-semibold">Vehicle Information</h3>
                <p>{appraisal.vehicle.year} {appraisal.vehicle.make} {appraisal.vehicle.model}</p>
                <p>VIN: {appraisal.vehicle.vin}</p>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="damages">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Estimated Cost</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {appraisal.damages.map((damage, index) => (
                  <TableRow key={index}>
                    <TableCell>{damage.description}</TableCell>
                    <TableCell>{new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(damage.estimatedCost)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>
          <TabsContent value="costs">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold">Labor Cost</h3>
                  <p>{new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(appraisal.laborCost)}</p>
                </div>
                <div>
                  <h3 className="font-semibold">Parts Cost</h3>
                  <p>{new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(appraisal.partsCost)}</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold">Total Cost</h3>
                <p className="text-lg font-bold">{new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(appraisal.totalCost)}</p>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="images">
            <div className="grid grid-cols-2 gap-4">
              {appraisal.images.map((image, index) => (
                <div key={index} className="relative aspect-video">
                  <Image
                    src={image}
                    alt={`Damage image ${index + 1}`}
                    fill
                    className="object-cover rounded-md"
                  />
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div>
          <Button variant="outline" className="mr-2" onClick={handleEditAppraisal}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Appraisal
          </Button>
          <Button variant="outline" className="mr-2" onClick={handleAddDocument}>
            <FileText className="mr-2 h-4 w-4" />
            Add Document
          </Button>
          <Button variant="outline" onClick={handleAddComment}>
            <MessageSquare className="mr-2 h-4 w-4" />
            Add Comment
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

