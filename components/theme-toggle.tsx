// "use client"

// import { Moon, Sun } from 'lucide-react'
// import { useTheme } from "next-themes"

// import { But<PERSON> } from "@/components/ui/button"

// export function ThemeToggle() {
//   const { theme, setTheme } = useTheme()

//   return (
//     <Button
//       variant="outline"
//       size="icon"
//       onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
//     >
//       <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
//       <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
//       <span className="sr-only">Toggle theme</span>
//     </Button>
//   )
// }

// components/ThemeToggle.tsx
"use client"

import { useState, useEffect } from "react"
import { Moon, Sun } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export function ThemeToggle() {
  const [isDarkMode, setIsDarkMode] = useState(false)

  // Check if dark mode is already set in localStorage or by system preference
  useEffect(() => {
    const isSystemDark = window.matchMedia('(prefers-color-scheme: light)').matches
    const savedMode = localStorage.getItem('theme')
    if (savedMode === 'dark' || (savedMode === null && isSystemDark)) {
      setIsDarkMode(true)
      document.documentElement.classList.add('dark')
    } else {
      setIsDarkMode(false)
      document.documentElement.classList.remove('dark')
    }
  }, [])

  // Toggle dark mode and store the preference in localStorage
  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode)
    if (!isDarkMode) {
      document.documentElement.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      document.documentElement.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }
  }

  return (
    <Button variant="ghost" onClick={toggleDarkMode} className="h-8 w-8 rounded-full">
      {isDarkMode ? (
        <Sun className="text-yellow-500" size={20} />
      ) : (
        <Moon className="text-gray-500" size={20} />
      )}
    </Button>
  )
}
