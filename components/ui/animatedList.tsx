"use client";

import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, { ComponentPropsWithoutRef, useEffect, useState, useRef } from "react";
import { 
  FiCheck, 
  FiPackage, 
  FiRefreshCw,
  FiBarChart2,
  FiDatabase,
  FiShare2,
  FiShield,
  FiTruck,
  FiCloud 
} from "react-icons/fi";

interface Item {
  id: string | number;
  name: string;
  description: string;
  icon: string;
  color: string;
  time: string;
}

export interface AnimatedListProps extends Omit<ComponentPropsWithoutRef<"div">, "children"> {
  items: Item[];
  className?: string;
  maxItems?: number;
  delay?: number;
  isVisible?: boolean;
  startDelay?: number;
}

export const AnimatedList = React.memo(
  ({ items, className, maxItems = 10, delay = 2000, isVisible = true, startDelay = 0, ...props }: AnimatedListProps) => {
    const [visibleItems, setVisibleItems] = useState<Item[]>([]);
    const [animationStarted, setAnimationStarted] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const indexRef = useRef(0);
    const isMountedRef = useRef(false);
    
    useEffect(() => {
      isMountedRef.current = true;
      return () => { isMountedRef.current = false; };
    }, []);
    
    useEffect(() => {
      if (!isVisible) {
        setVisibleItems([]);
        setAnimationStarted(false);
        indexRef.current = 0;
        
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        return;
      }
      
      if (animationStarted) return;

      const timer = setTimeout(() => {
        if (isMountedRef.current && isVisible) { 
           setAnimationStarted(true);
        }
      }, startDelay);
      
      return () => clearTimeout(timer);
    }, [isVisible, startDelay, animationStarted]);
    
    useEffect(() => {
      const cleanup = () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };

      if (!animationStarted || !isVisible || items.length === 0) {
        return cleanup;
      }

      const addItem = () => {
         if (indexRef.current < items.length) {
           const currentItem = items[indexRef.current];
           setVisibleItems(prev => {
             if (prev.find(p => p.id === currentItem.id)) return prev; 
             const newItems = [currentItem, ...prev];
             return newItems.slice(0, maxItems);
           });
           indexRef.current++;
         } else {
           indexRef.current = 0;
         }
      };
      
      cleanup();
      intervalRef.current = setInterval(addItem, delay);
      
      return cleanup;
    }, [animationStarted, isVisible, items, delay, maxItems]);
    
    return (
      <div
        className={cn(
          "relative flex flex-col space-y-4 overflow-hidden",
          className
        )}
        {...props}
      >
        <AnimatePresence>
          {visibleItems.map((item) => ( 
            <motion.div
              key={item.id} 
              layout
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, transition: { duration: 0.2 } }} 
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              <Notification 
                {...item} 
                className={item.time.includes('Step') ? 'border-[#0be5a9]/40' : 'border-white/10'}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    );
  }
);

AnimatedList.displayName = "AnimatedList";

const Notification = ({ name, description, icon, color, time, className }: Item & { className?: string }) => {
  const iconMap: { [key: string]: React.ReactNode } = {
    'check': <FiCheck size={20} />,
    'package': <FiPackage size={20} />,
    'refresh': <FiRefreshCw size={20} />,
    'chart': <FiBarChart2 size={20} />,
    'database': <FiDatabase size={20} />,
    'network': <FiShare2 size={20} />,
    'shield': <FiShield size={20} />,
    'truck': <FiTruck size={20} />,
    'cloud': <FiCloud size={20} />,
  };

  const renderIcon = () => {
    if (iconMap[icon]) {
      return iconMap[icon];
    }
    return <span className="text-lg">{icon}</span>;
  };

  return (
    <figure
      className={cn(
        "relative mx-auto min-h-fit w-full max-w-[400px] cursor-pointer overflow-hidden rounded-2xl p-4",
        "transition-all duration-200 ease-in-out hover:scale-[103%]",
        "bg-[#1a2436] border",
        "transform-gpu backdrop-blur-md",
        className
      )}
    >
      <div className="flex flex-row items-center gap-3">
        <div
          className="flex h-10 w-10 min-w-10 min-h-10 items-center justify-center rounded-2xl text-white"
          style={{
            backgroundColor: color,
          }}
        >
          {renderIcon()}
        </div>
        <div className="flex flex-col overflow-hidden">
          <figcaption className="flex flex-row items-center whitespace-pre text-lg font-medium text-white">
            <span className="text-sm sm:text-lg">{name}</span>
            <span className="mx-1">·</span>
            <span className="text-xs text-gray-500">{time}</span>
          </figcaption>
          <p className="text-sm font-normal text-white/60">
            {description}
          </p>
        </div>
      </div>
    </figure>
  );
};
