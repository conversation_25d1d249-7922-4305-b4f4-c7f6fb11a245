"use client";

import { cn } from "@/lib/utils";
import { motion, MotionProps } from "motion/react";
import { useEffect, useRef, useState } from "react";
import React from "react";

interface AnimatedSpanProps extends MotionProps {
  children: React.ReactNode;
  delay?: number;
  className?: string;
  isVisible?: boolean;
}

export const AnimatedSpan = ({
  children,
  delay = 0,
  className,
  isVisible = false,
  ...props
}: AnimatedSpanProps) => (
  <motion.div
    initial={{ opacity: 0, y: -5 }}
    animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : -5 }}
    transition={{ duration: 0.3, delay: delay / 1000 }}
    className={cn("grid text-sm font-normal tracking-tight", className)}
    {...props}
  >
    {children}
  </motion.div>
);

interface TypingAnimationProps extends MotionProps {
  children: string;
  className?: string;
  duration?: number;
  delay?: number;
  as?: React.ElementType;
  isVisible?: boolean;
}

export const TypingAnimation = ({
  children,
  className,
  duration = 60,
  delay = 0,
  as: Component = "span",
  isVisible = false,
  ...props
}: TypingAnimationProps) => {
  if (typeof children !== "string") {
    throw new Error("TypingAnimation: children must be a string. Received:");
  }

  const MotionComponent = motion.create(Component, {
    forwardMotionProps: true,
  });

  const [displayedText, setDisplayedText] = useState<string>("");
  const [started, setStarted] = useState(false);
  const elementRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!isVisible) {
      setDisplayedText("");
      setStarted(false);
      return;
    }

    const startTimeout = setTimeout(() => {
      setStarted(true);
    }, delay);
    return () => clearTimeout(startTimeout);
  }, [delay, isVisible]);

  useEffect(() => {
    if (!started) return;

    let i = 0;
    const typingEffect = setInterval(() => {
      if (i < children.length) {
        setDisplayedText(children.substring(0, i + 1));
        i++;
      } else {
        clearInterval(typingEffect);
      }
    }, duration);

    return () => {
      clearInterval(typingEffect);
    };
  }, [children, duration, started]);

  return (
    <MotionComponent
      ref={elementRef}
      className={cn("text-sm font-normal tracking-tight text-white", className)}
      {...props}
    >
      {displayedText}
    </MotionComponent>
  );
};

interface TerminalProps {
  children: React.ReactNode;
  className?: string;
  isVisible?: boolean;
}

export const Terminal = ({ children, className, isVisible = false }: TerminalProps) => {
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { isVisible } as any);
    }
    return child;
  });

  return (
    <div
      className={cn(
        "z-0 w-full rounded-xl bg-[#141b2b] border border-[#0be5a9]/20",
        className,
      )}
    >
      <div className="flex flex-row items-center justify-between border-b border-[#0be5a9]/10 p-4">
        <div className="flex flex-row gap-x-2">
          <div className="h-2 w-2 rounded-full bg-red-500"></div>
          <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
          <div className="h-2 w-2 rounded-full bg-green-500"></div>
        </div>
        <button 
          className="px-4 py-1 bg-[#1d2739] text-[#0be5a9] text-sm font-mono rounded border border-[#0be5a9]/50 hover:bg-[#243043] transition-colors flex items-center gap-x-2"
          onClick={() => console.log('Running Rekover...')}
        >
          <span className="text-white opacity-70">$</span> run rekover
        </button>
      </div>
      <pre className="p-4 h-full">
        <code className="grid gap-y-1 overflow-hidden font-mono text-sm text-white h-full">
          {childrenWithProps}
        </code>
      </pre>
    </div>
  );
};
