"use client";

import React, { forwardRef, useRef } from "react";
import Image from "next/image";
import { 
  <PERSON>a<PERSON><PERSON><PERSON>, 
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>, 
  FaFacebook<PERSON>essenger, 
  FaUserAlt 
} from "react-icons/fa";
import { SiNotion, SiGoogledrive, SiGoogledocs } from "react-icons/si";

import { cn } from "@/lib/utils";
import { AnimatedBeam } from "@/components/ui/animated-beam";

const Circle = forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "z-10 flex size-12 items-center justify-center rounded-full border-2 border-border !bg-[#3a3e44] p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]",
        className,
      )}
    >
      {children}
    </div>
  );
});

Circle.displayName = "Circle";

export function AnimatedBeamMultipleOutputDemo({
  className,
}: {
  className?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const div1Ref = useRef<HTMLDivElement>(null);
  const div2Ref = useRef<HTMLDivElement>(null);
  const div3Ref = useRef<HTMLDivElement>(null);
  const div4Ref = useRef<HTMLDivElement>(null);
  const div5Ref = useRef<HTMLDivElement>(null);
  const div6Ref = useRef<HTMLDivElement>(null);
  const div7Ref = useRef<HTMLDivElement>(null);
  const div8Ref = useRef<HTMLDivElement>(null);

  return (
    <div
      className={cn(
        "relative flex h-[700px] w-full items-center justify-center overflow-hidden p-10",
        className,
      )}
      ref={containerRef}
    >
      <div className="flex size-full max-w-xl flex-col items-stretch justify-between gap-10">
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div1Ref} className="size-14 border-[#f69323]/40">
          <div className="relative w-full h-full flex items-center justify-center">
              <Image 
                src="/landingpage/icon4.png" 
                alt="Icon" 
                width={120}
                height={120}
                className="object-contain"
              />
            </div>
          </Circle>
          <Circle ref={div5Ref} className="size-14 border-[#f69323]/40">
            <SiGoogledocs className="w-8 h-8 text-[#4285F4]" />
          </Circle>
        </div>
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div2Ref} className="size-14 border-[#f69323]/40">
          <div className="relative w-full h-full flex items-center justify-center">
              <Image 
                src="/landingpage/icon3.png" 
                alt="Icon" 
                width={120}
                height={120}
                className="object-contain"
              />
            </div>
          </Circle>
          <Circle ref={div6Ref} className="size-20 border-[#f69323] bg-[#f5efe0]">
            <div className="relative w-12 h-12 flex items-center justify-center">
              <Image 
                src="/landingpage/favicon.png" 
                alt="Company Logo" 
                width={60} 
                height={60}
                className="object-contain"
              />
            </div>
          </Circle>
          <Circle ref={div3Ref} className="size-14 border-[#f69323]/40">
          <div className="relative w-full h-full flex items-center justify-center overflow-visible">
              <Image 
                src="/landingpage/icon2.png" 
                alt="Icon" 
                width={120}
                height={120}
                className="object-contain"
              />
            </div>
          </Circle>
        </div>
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div4Ref} className="size-14 border-[#f69323]/40">
            <div className="relative w-full h-full flex items-center justify-center overflow-visible">
              <Image 
                src="/landingpage/icon1.png" 
                alt="Icon" 
                width={120}
                height={120}
                className="object-contain"
              />
            </div>
          </Circle>
          <Circle ref={div8Ref} className="size-14 border-[#f69323]">
            <FaUserAlt className="w-8 h-8" />
          </Circle>
        </div>
      </div>
      {/* Drive */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div1Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
        curvature={-75}
        endYOffset={-10}
      />
      {/* Notion */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div2Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
      />
      {/* Whatsapp */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div3Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
        reverse
      />
      {/* Messenger */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div4Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
        curvature={75}
        endYOffset={10}
      />
      {/* Google Docs */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div5Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
        curvature={-75}
        endYOffset={-10}
        reverse
      />
      
      {/* User (rightbottom)  */}
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div8Ref}
        toRef={div6Ref}
        pathWidth={3}
        gradientStartColor="#f69323"
        gradientStopColor="#f69323"
        pathColor="#f69323"
        curvature={75}
        endYOffset={10}
        reverse
      />
    </div>
  );
}
