import { SVGProps, ReactNode } from "react";

type SafariMode = "default" | "simple";

export interface SafariProps extends SVGProps<SVGSVGElement> {
  url?: string;
  imageSrc?: string;
  videoSrc?: string;
  width?: number;
  height?: number;
  mode?: SafariMode;
  content?: ReactNode;
}

export function Safari({
  imageSrc,
  videoSrc,
  content,
  url = "aiinsurtech.com",
  width = 1203,
  height = 753,
  mode = "default",
  ...props
}: SafariProps) {
  // Calculate the precise content area dimensions
  const contentX = 1;
  const contentY = 52; // Height of the top bar
  const contentWidth = width - 2; // Subtract 1px border from each side
  const contentHeight = height - contentY - 1; // Subtract top bar height and 1px bottom border

  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#path0)">
        {/* Main browser window - solid dark color */}
        <path
          d={`M0 ${contentY}H${width}V${height - 1}C${width} ${height - 1 + 6.627} ${width - 5.37} ${height} ${width - 12} ${height}H12C5.37258 ${height} 0 ${height - 5.373} 0 ${height - 12}V${contentY}Z`} // Adjusted path based on contentY
          fill="#202020"
        />
        
        {/* Top bar - darker header color */}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d={`M0 12C0 5.37258 5.37258 0 12 0H${width - 12}C${width - 5.37} 0 ${width} 5.37258 ${width} 12V${contentY}H0V12Z`} // Adjusted path based on width and contentY
          fill="#3b3c3f"
        />
        
        {/* Inner top bar - same as header */}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d={`M${contentX} 12C${contentX} 5.92487 ${contentX + 4.925} 1 ${contentX + 11} 1H${width - 1 - 11}C${width - 1 - 5.925} 1 ${width - 1} 5.92487 ${width - 1} 12V${contentY - 1}H${contentX}V12Z`} // Adjusted path based on contentX, width and contentY
          fill="#3b3c3f"
        />
        
        {/* Traffic light buttons - standard colors */}
        <circle cx="27" cy="25" r="6" fill="#ff5f57" />
        <circle cx="47" cy="25" r="6" fill="#febc2e" />
        <circle cx="67" cy="25" r="6" fill="#28c840" />
        
        {/* URL bar - slightly lighter than header */}
        <path
          d="M286 17C286 13.6863 288.686 11 292 11H946C949.314 11 952 13.6863 952 17V35C952 38.3137 949.314 41 946 41H292C288.686 41 286 38.3137 286 35V17Z"
          fill="#2d2d2d"
        />
        
        {/* Lock icon */}
        <g>
          <path
            d="M566.269 32.0852H572.426C573.277 32.0852 573.696 31.6663 573.696 30.7395V25.9851C573.696 25.1472 573.353 24.7219 572.642 24.6521V23.0842C572.642 20.6721 571.036 19.5105 569.348 19.5105C567.659 19.5105 566.053 20.6721 566.053 23.0842V24.6711C565.393 24.7727 565 25.1917 565 25.9851V30.7395C565 31.6663 565.418 32.0852 566.269 32.0852ZM567.272 22.97C567.272 21.491 568.211 20.6785 569.348 20.6785C570.478 20.6785 571.423 21.491 571.423 22.97V24.6394L567.272 24.6458V22.97Z"
            fill="#6d6e6c"
          />
        </g>
        
        {/* URL text */}
        <g>
          <text
            x="580"
            y="30"
            fill="#6d6e6c"
            fontSize="12"
            fontFamily="Arial, sans-serif"
          >
            {url}
          </text>
        </g>
        
        {mode === "default" ? (
          <>
            {/* Navigation buttons */}
            <g>
              <path
                d="M143.914 32.5938C144.094 32.7656 144.312 32.8594 144.562 32.8594C145.086 32.8594 145.492 32.4531 145.492 31.9375C145.492 31.6797 145.391 31.4453 145.211 31.2656L139.742 25.9219L145.211 20.5938C145.391 20.4141 145.492 20.1719 145.492 19.9219C145.492 19.4062 145.086 19 144.562 19C144.312 19 144.094 19.0938 143.922 19.2656L137.844 25.2031C137.625 25.4062 137.516 25.6562 137.516 25.9297C137.516 26.2031 137.625 26.4375 137.836 26.6484L143.914 32.5938Z"
                fill="#6d6e6c"
              />
            </g>
            <g>
              <path
                d="M168.422 32.8594C168.68 32.8594 168.891 32.7656 169.07 32.5938L175.148 26.6562C175.359 26.4375 175.469 26.2109 175.469 25.9297C175.469 25.6562 175.367 25.4141 175.148 25.2109L169.07 19.2656C168.891 19.0938 168.68 19 168.422 19C167.898 19 167.492 19.4062 167.492 19.9219C167.492 20.1719 167.602 20.4141 167.773 20.5938L173.25 25.9375L167.773 31.2656C167.594 31.4531 167.492 31.6797 167.492 31.9375C167.492 32.4531 167.898 32.8594 168.422 32.8594Z"
                fill="#6d6e6c"
              />
            </g>
            
            {/* Other toolbar buttons */}
            <g>
              <path
                d="M936.273 24.9766C936.5 24.9766 936.68 24.9062 936.82 24.7578L940.023 21.5312C940.195 21.3594 940.273 21.1719 940.273 20.9531C940.273 20.7422 940.188 20.5391 940.023 20.3828L936.82 17.125C936.68 16.9688 936.5 16.8906 936.273 16.8906C935.852 16.8906 935.516 17.2422 935.516 17.6719C935.516 17.8828 935.594 18.0547 935.727 18.2031L937.594 20.0312C937.227 19.9766 936.852 19.9453 936.477 19.9453C932.609 19.9453 929.516 23.0391 929.516 26.9141C929.516 30.7891 932.633 33.9062 936.5 33.9062C940.375 33.9062 943.484 30.7891 943.484 26.9141C943.484 26.4453 943.156 26.1094 942.688 26.1094C942.234 26.1094 941.93 26.4453 941.93 26.9141C941.93 29.9297 939.516 32.3516 936.5 32.3516C933.492 32.3516 931.07 29.9297 931.07 26.9141C931.07 23.875 933.469 21.4688 936.477 21.4688C936.984 21.4688 937.453 21.5078 937.867 21.5781L935.734 23.6875C935.594 23.8281 935.516 24 935.516 24.2109C935.516 24.6406 935.852 24.9766 936.273 24.9766Z"
                fill="#6d6e6c"
              />
            </g>
            <g>
              <path
                d="M265.5 33.8984C265.641 33.8984 265.852 33.8516 266.047 33.7422C270.547 31.2969 272.109 30.1641 272.109 27.3203V21.4219C272.109 20.4844 271.742 20.1484 270.961 19.8125C270.094 19.4453 267.18 18.4297 266.328 18.1406C266.07 18.0547 265.766 18 265.5 18C265.234 18 264.93 18.0703 264.672 18.1406C263.82 18.3828 260.906 19.4531 260.039 19.8125C259.258 20.1406 258.891 20.4844 258.891 21.4219V27.3203C258.891 30.1641 260.461 31.2812 264.945 33.7422C265.148 33.8516 265.359 33.8984 265.5 33.8984ZM265.922 19.5781C266.945 19.9766 269.172 20.7656 270.344 21.1875C270.562 21.2656 270.617 21.3828 270.617 21.6641V27.0234C270.617 29.3125 269.469 29.9375 265.945 32.0625C265.727 32.1875 265.617 32.2344 265.508 32.2344V19.4844C265.617 19.4844 265.734 19.5156 265.922 19.5781Z"
                fill="#6d6e6c"
              />
            </g>
          </>
        ) : null}
        
        {/* Content area - Adjusted foreignObject */}
        {imageSrc && (
          <image
            href={imageSrc}
            width={contentWidth} // Use calculated width
            height={contentHeight} // Use calculated height
            x={contentX} // Use calculated X
            y={contentY} // Use calculated Y
            preserveAspectRatio="xMidYMid slice"
            clipPath="url(#roundedBottom)"
          />
        )}
        
        {videoSrc && (
          <foreignObject
            x={contentX} // Use calculated X
            y={contentY} // Use calculated Y
            width={contentWidth} // Use calculated width
            height={contentHeight} // Use calculated height
            preserveAspectRatio="xMidYMid slice" // This might not be needed for foreignObject
            clipPath="url(#roundedBottom)"
          >
            <video
              className="size-full overflow-hidden object-cover" // size-full should make it fill foreignObject
              src={videoSrc}
              autoPlay
              loop
              muted
              playsInline
            />
          </foreignObject>
        )}
        
        {/* React component content - Adjusted foreignObject */}
        {content && (
          <foreignObject
            x={contentX} // Use calculated X
            y={contentY} // Use calculated Y
            width={contentWidth} // Use calculated width
            height={contentHeight} // Use calculated height
            clipPath="url(#roundedBottom)"
          >
            {/* Ensure the inner div also fills the foreignObject */}
            <div className="w-full h-full bg-transparent overflow-hidden"> 
              {content}
            </div>
          </foreignObject>
        )}
        
        {/* Fallback if no content provided - Adjusted rect */}
        {!imageSrc && !videoSrc && !content && (
          <rect 
            x={contentX} // Use calculated X
            y={contentY} // Use calculated Y
            width={contentWidth} // Use calculated width
            height={contentHeight} // Use calculated height 
            fill="#202020"
            clipPath="url(#roundedBottom)"
          />
        )}
      </g>
      <defs>
        <clipPath id="path0">
          {/* Clip path should match the overall SVG dimensions */}
          <rect width={width} height={height} fill="white" />
        </clipPath>
        <clipPath id="roundedBottom">
           {/* Clip path should match the content area including rounded corners */}
          <path
             d={`M${contentX} ${contentY} H${contentX + contentWidth} V${contentY + contentHeight - 11} C${contentX + contentWidth} ${contentY + contentHeight - 11 + 6.075} ${contentX + contentWidth - 5.925} ${contentY + contentHeight} ${contentX + contentWidth - 12} ${contentY + contentHeight} H${contentX + 12} C${contentX + 5.925} ${contentY + contentHeight} ${contentX} ${contentY + contentHeight - 5.925} ${contentX} ${contentY + contentHeight - 12} V${contentY} Z`}
             fill="white"
           />
        </clipPath>
      </defs>
    </svg>
  );
}
