import React from 'react';
import { motion } from 'framer-motion'; // motion is imported but not used, consider removing if not needed elsewhere

const Demo = () => {
  // Path to your video file (ensure this is correct relative to your public directory)
  const videoSrc = "/landingpage/video/market.mp4"; 

  return (
    // Container for the video, ensuring it fills the available space
    <div className="w-full h-full bg-black"> 
      <video 
        className="w-full h-full object-cover" // Changed object-contain to object-cover if you want it to fill
        src={videoSrc} 
        autoPlay 
        muted 
        loop 
        playsInline // Important for mobile playback
        controls={false} // Keep controls hidden for a clean look within the Safari frame
      >
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default Demo;