'use client'

import React, { useEffect } from 'react'
import { Button, ButtonProps } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CalendlyButtonProps extends ButtonProps {
  children: React.ReactNode
  className?: string
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  calendlyUrl?: string
}

export function CalendlyButton({
  children,
  className,
  variant = "default",
  calendlyUrl = "https://calendly.com/francisco-rekover/30min?primary_color=e3af13",
  ...props
}: CalendlyButtonProps) {

  // Load Calendly script
  useEffect(() => {
    const head = document.querySelector('head')

    // Add CSS
    const link = document.createElement('link')
    link.href = "https://assets.calendly.com/assets/external/widget.css"
    link.rel = "stylesheet"
    head?.appendChild(link)

    // Add JS
    const script = document.createElement('script')
    script.src = "https://assets.calendly.com/assets/external/widget.js"
    script.type = "text/javascript"
    script.async = true
    head?.appendChild(script)

    return () => {
      // Clean up
      if (head?.contains(link)) {
        head.removeChild(link)
      }
      if (head?.contains(script)) {
        head.removeChild(script)
      }
    }
  }, [])

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    // Make sure Calendly is loaded
    if (typeof window !== 'undefined' && window.Calendly) {
      window.Calendly.initPopupWidget({
        url: calendlyUrl
      })
    } else {
      console.error('Calendly not loaded')
      // Fallback - open the URL directly
      window.open(calendlyUrl, '_blank')
    }
    return false
  }

  return (
    <Button
      className={cn(className)}
      variant={variant}
      onClick={handleClick}
      {...props}
    >
      {children}
    </Button>
  )
}

// Add Calendly type to Window interface
declare global {
  interface Window {
    Calendly?: {
      initPopupWidget: (options: { url: string }) => void
    }
  }
}
