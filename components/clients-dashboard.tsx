"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import ClientAc<PERSON><PERSON>hart from "./charts/client-acquisition-chart"
import ClientTypesPieChart from "./charts/client-types-pie-chart"
import ClientValueBarChart from "./charts/client-value-bar-chart"
import ClientRetention<PERSON>hart from "./charts/client-retention-chart"
import TopClientsTable from "./top-clients-table"
import { DollarSign, Clock, FileText, TrendingUp, Briefcase, ClipboardList, Users } from 'lucide-react'

export default function ClientsDashboard() {
return (
<div className="space-y-6">
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </<PERSON><PERSON><PERSON>er>
      <CardContent>
        <div className="text-2xl font-bold">2,548</div>
        <p className="text-xs text-muted-foreground">+15.5% from last month</p>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Average Policies/Client</CardTitle>
        <Briefcase className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">2.8</div>
        <p className="text-xs text-muted-foreground">+0.2 from last month</p>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Average Claims/Client</CardTitle>
        <ClipboardList className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">0.9</div>
        <p className="text-xs text-muted-foreground">-0.1 from last month</p>
      </CardContent>
    </Card>
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Client Retention Rate</CardTitle>
        <TrendingUp className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">85%</div>
        <p className="text-xs text-muted-foreground">+2% from last month</p>
      </CardContent>
    </Card>
  </div>

  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>Client Acquisition Over Time</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <ClientAcquisitionChart />
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Client Types Distribution</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <ClientTypesPieChart />
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Client Value Segments</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <ClientValueBarChart />
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Client Retention Rate</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <ClientRetentionChart />
      </CardContent>
    </Card>
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>Top Clients</CardTitle>
      </CardHeader>
      <CardContent>
        <TopClientsTable />
      </CardContent>
    </Card>
  </div>
</div>
)
}

