"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Search, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import Link from 'next/link'

// Define the structure of a client
interface Client {
  id: string
  name: string
  email: string
  type: "Individual" | "Corporate"
  status: "Active" | "Inactive"
  policies: number
  totalValue: number
  lastContact: Date
}

// Sample data for clients
const clients: Client[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "Individual",
    status: "Active",
    policies: 2,
    totalValue: 5000,
    lastContact: new Date("2023-05-15")
  },
  {
    id: "2",
    name: "Jane Smith",
    email: "<EMAIL>",
    type: "Individual",
    status: "Active",
    policies: 1,
    totalValue: 3000,
    lastContact: new Date("2023-05-10")
  },
  {
    id: "3",
    name: "Acme Corp",
    email: "<EMAIL>",
    type: "Corporate",
    status: "Active",
    policies: 5,
    totalValue: 25000,
    lastContact: new Date("2023-05-20")
  },
  {
    id: "4",
    name: "Bob Johnson",
    email: "<EMAIL>",
    type: "Individual",
    status: "Inactive",
    policies: 1,
    totalValue: 2000,
    lastContact: new Date("2023-04-25")
  },
  {
    id: "5",
    name: "Tech Innovators Ltd",
    email: "<EMAIL>",
    type: "Corporate",
    status: "Active",
    policies: 3,
    totalValue: 15000,
    lastContact: new Date("2023-05-18")
  }
]

// Define table columns
const columns: ColumnDef<Client>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      const client = row.original
      return (
        <Link href={`/clients/${client.id}`} className="text-blue-500 hover:underline">
          {client.name}
        </Link>
      )
    },
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "type",
    header: "Type",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <Badge variant={status === "Active" ? "default" : "secondary"}>
          {status}
        </Badge>
      )
    },
  },
  {
    accessorKey: "policies",
    header: "Policies",
  },
  {
    accessorKey: "totalValue",
    header: "Total Value",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("totalValue"))
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount)
      return <div className="font-medium">{formatted}</div>
    },
  },
  {
    accessorKey: "lastContact",
    header: "Last Contact",
    cell: ({ row }) => {
      const date = row.getValue("lastContact") as Date
      return <div>{date.toLocaleDateString()}</div>
    },
  },
]

export default function ClientsBrowseScreen() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedType, setSelectedType] = useState<string | undefined>()
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>()
  const [filteredClients, setFilteredClients] = useState(clients)

  const handleSearch = () => {
    const filtered = clients.filter((client) => {
      const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            client.email.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesType = !selectedType || client.type === selectedType
      const matchesStatus = !selectedStatus || client.status === selectedStatus
      return matchesSearch && matchesType && matchesStatus
    })
    setFilteredClients(filtered)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Clients</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search clients..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Client Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Individual">Individual</SelectItem>
                <SelectItem value="Corporate">Corporate</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch} className="w-full sm:w-auto">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
          </div>
        </div>
        {filteredClients.length > 0 ? (
          <div className="mt-6">
            <DataTable columns={columns} data={filteredClients} />
          </div>
        ) : (
          <Alert className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No results</AlertTitle>
            <AlertDescription>
              No clients match your search criteria. Try adjusting your filters.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

