import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { InfoIcon, Car } from "lucide-react"

interface PriceStatsInfographicProps {
  stats: {
    nr_of_cars: number;
    min_price: number;
    max_price: number;
    median_price: number;
    mean_price: number;
  };
  safeZoneRange?: [number, number];
  estimatedPrice?: number;
}

export function PriceStatsInfographic({ stats, safeZoneRange, estimatedPrice }: PriceStatsInfographicProps) {
  const range = (stats?.max_price ?? 0) - (stats?.min_price ?? 0);
  
  // Clamp all position calculations to ensure they stay within 0-100% range
  const medianPosition = Math.min(100, Math.max(0, ((stats?.median_price ?? 0) - (stats?.min_price ?? 0)) / (range || 1) * 100));
  const meanPosition = Math.min(100, Math.max(0, ((stats?.mean_price ?? 0) - (stats?.min_price ?? 0)) / (range || 1) * 100));
  
  // Calculate percentage differences (for insurance context)
  const medianToMeanDiff = stats?.mean_price && stats?.median_price 
    ? (((stats.mean_price - stats.median_price) / stats.median_price) * 100).toFixed(1)
    : "0";
  
  const diffDirection = parseFloat(medianToMeanDiff) >= 0 ? "above" : "below";

  // Calculate safe zone positions with clamping
  const safeZoneStart = safeZoneRange 
    ? Math.min(100, Math.max(0, ((safeZoneRange[0] - (stats?.min_price ?? 0)) / (range || 1)) * 100))
    : 0;
  const safeZoneEnd = safeZoneRange
    ? Math.min(100, Math.max(0, ((safeZoneRange[1] - (stats?.min_price ?? 0)) / (range || 1)) * 100))
    : 0;
  const safeZoneWidth = safeZoneEnd - safeZoneStart;

  // Calculate triangle position with clamping
  const trianglePosition = estimatedPrice
    ? Math.min(100, Math.max(0, ((estimatedPrice - (stats?.min_price ?? 0)) / (range || 1)) * 100))
    : safeZoneRange
      ? Math.min(100, Math.max(0, ((safeZoneRange[0] + safeZoneRange[1]) / 2 - (stats?.min_price ?? 0)) / (range || 1) * 100))
      : 0;

  return (
    <>
      {/* Enhanced version - widened and colors adjusted */}
      <div className="mb-6 flex justify-end">
        <div className="bg-white rounded-lg shadow-md p-4 w-[600px] relative overflow-hidden">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-bold text-sm text-gray-700">Market Value Analysis</h3>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <InfoIcon className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent className="max-w-[300px]">
                  <p>This analysis is based on {stats?.nr_of_cars} vehicles. The distribution shows where prices fall in the market.</p>
                  {estimatedPrice && (
                    <p className="mt-1">The triangle shows your car's estimated value based on your selected filters.</p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          {/* Distribution visualization - adjusted colors and positioning */}
          <div className="relative h-12 mb-6">
            {/* Safe zone background */}
            {safeZoneRange && (
              <div 
                className="absolute bottom-0 h-2 bg-green-100 rounded-full"
                style={{ 
                  left: `${safeZoneStart}%`,
                  width: `${safeZoneWidth}%`
                }}
              />
            )}
            
            {/* Price distribution background - made slightly darker */}
            <div className="absolute bottom-0 w-full h-2 bg-gray-300 rounded-full"></div>
            
            {/* Progress bar - darker blue */}
            <div
              className="absolute bottom-0 h-2 bg-blue-200 rounded-full"
              style={{ width: `${medianPosition}%` }}
            />
            
            {/* Min and Max labels - keep these as text */}
            <div className="absolute bottom-5 text-xs font-medium transform -translate-x-1/2"
                 style={{ left: "0%" }}>
              <span className="text-gray-700">Min</span>
            </div>
            <div className="absolute bottom-5 text-xs font-medium transform -translate-x-1/2"
                 style={{ left: "100%" }}>
              <span className="text-gray-700">Max</span>
            </div>
            
            {/* Median line with tooltip */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className="absolute bottom-0 w-1.5 h-4 bg-blue-700 rounded cursor-help"
                    style={{ 
                      left: `${medianPosition}%`,
                      transform: 'translateX(-50%)'
                    }}
                  ></div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Median: {stats?.median_price?.toLocaleString()}€</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* Mean indicator with tooltip */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div 
                    className="absolute w-4 h-4 bg-green-600 rounded-full border-2 border-white cursor-help"
                    style={{ 
                      left: `${meanPosition}%`,
                      bottom: '-1px',
                      transform: 'translateX(-50%)'
                    }}
                  ></div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Mean: {typeof stats?.mean_price === 'number' ? Math.round(stats.mean_price).toLocaleString() : '0'}€</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* Valuation triangle with tooltip */}
            {(estimatedPrice || safeZoneRange) && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className="absolute w-0 h-0 border-l-8 border-r-8 border-b-8 border-transparent border-b-green-600"
                      style={{ 
                        left: `${trianglePosition}%`,
                        bottom: '-10px',
                        transform: 'translateX(-50%)'
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>My Car</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            
            {/* Car icon below the triangle */}
            {estimatedPrice && (
              <div
                className="absolute transform -translate-x-1/2"
                style={{ 
                  left: `${trianglePosition}%`,
                  bottom: '-25px'
                }}
              >
                <Car className="h-4 w-4 text-green-600" />
              </div>
            )}
          </div>
          
          {/* Price statistics - adjusted colors to match new scheme */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-xs text-gray-500 mb-1">Median Value</div>
              <div className="font-semibold text-blue-700 text-lg">
                {stats?.median_price?.toLocaleString()}€
              </div>
            </div>
            
            <div>
              <div className="text-xs text-gray-500 mb-1">Mean Value</div>
              <div className="font-semibold text-green-600 text-lg">
                {typeof stats?.mean_price === 'number' 
                  ? Math.round(stats.mean_price).toLocaleString() 
                  : '0'}€
              </div>
            </div>
            
            <div>
              <div className="text-xs text-gray-500 mb-1">Price Range</div>
              <div className="font-semibold text-gray-700">
                {stats?.min_price?.toLocaleString()} - {stats?.max_price?.toLocaleString()}€
              </div>
            </div>
            
            {estimatedPrice ? (
              <div>
                <div className="text-xs text-gray-500 mb-1">Your Car's Estimated Value</div>
                <div className="font-semibold text-green-600">
                  {Math.round(estimatedPrice).toLocaleString()}€
                </div>
              </div>
            ) : (
              <div>
                <div className="text-xs text-gray-500 mb-1">Market Dispersion</div>
                <div className="font-semibold">
                  <span className={parseFloat(medianToMeanDiff) >= 0 
                    ? "text-red-600" 
                    : "text-green-600"
                  }>
                    {Math.abs(parseFloat(medianToMeanDiff))}% {diffDirection} median
                  </span>
                </div>
              </div>
            )}
          </div>
          
          {/* Market insight - adjusted colors to match new scheme */}
          <div className="mt-4 text-xs border-t border-gray-200 pt-3">
            <span className="italic text-gray-600">
              Mean is </span>
            <span className={parseFloat(medianToMeanDiff) >= 0 
              ? "italic font-medium text-red-600" 
              : "italic font-medium text-green-600"
            }>
              {Math.abs(parseFloat(medianToMeanDiff))}% {diffDirection} median
            </span>
            <span className="italic text-gray-600">, indicating 
            {parseFloat(medianToMeanDiff) >= 0.5 
              ? " potential premium pricing in this market." 
              : parseFloat(medianToMeanDiff) <= -0.5 
                ? " potential undervaluation in this market."
                : " a balanced market valuation."}
            </span>
          </div>
        </div>
      </div>
    </>
  )
}

