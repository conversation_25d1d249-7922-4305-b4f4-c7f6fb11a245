"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export function CheckInsurerForm({ onSubmit }: { onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    licensePlate: '',
    checkDate: '',
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="licensePlate">License Plate</Label>
        <Input
          id="licensePlate"
          name="licensePlate"
          value={formData.licensePlate}
          onChange={handleChange}
          required
          placeholder="Enter license plate"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="checkDate">Check Date</Label>
        <Input
          id="checkDate"
          name="checkDate"
          type="date"
          value={formData.checkDate}
          onChange={handleChange}
          required
        />
      </div>
      <Button type="submit" className="w-full">
        Check Insurance
      </Button>
    </form>
  )
}

