import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, XCircle, Clock, AlertTriangle } from 'lucide-react'

interface WorkflowReportProps {
  success: boolean
  workflowTitle: string
  executionTime: string
  results: {
    summary: string
    details: string[]
    recommendations?: string[]
    warnings?: string[]
  }
}

export function WorkflowReport({ success, workflowTitle, executionTime, results }: WorkflowReportProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              {success ? (
                <CheckCircle2 className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span>{workflowTitle} - Execution Report</span>
            </CardTitle>
            <CardDescription className="flex items-center mt-1">
              <Clock className="h-4 w-4 mr-1" />
              Execution Time: {executionTime}
            </CardDescription>
          </div>
          <Badge variant={success ? "default" : "destructive"}>
            {success ? "Completed" : "Failed"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold mb-2">Summary</h3>
          <p className="text-sm">{results.summary}</p>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Details</h3>
          <ul className="list-disc list-inside space-y-1">
            {results.details.map((detail, index) => (
              <li key={index} className="text-sm">{detail}</li>
            ))}
          </ul>
        </div>

        {results.recommendations && results.recommendations.length > 0 && (
          <div>
            <h3 className="font-semibold mb-2">Recommendations</h3>
            <ul className="list-disc list-inside space-y-1">
              {results.recommendations.map((recommendation, index) => (
                <li key={index} className="text-sm">{recommendation}</li>
              ))}
            </ul>
          </div>
        )}

        {results.warnings && results.warnings.length > 0 && (
          <div>
            <h3 className="font-semibold flex items-center mb-2">
              <AlertTriangle className="h-4 w-4 mr-1 text-yellow-500" />
              Warnings
            </h3>
            <ul className="list-disc list-inside space-y-1">
              {results.warnings.map((warning, index) => (
                <li key={index} className="text-sm text-yellow-600">{warning}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

