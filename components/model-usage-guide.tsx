import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Lightbulb, AlertCircle, CheckCircle2, XCircle } from 'lucide-react'
import { Separator } from "@/components/ui/separator"

interface UsageExample {
  input: Record<string, string>
  expectedOutput: string
  explanation: string
}

interface ModelUsageGuideProps {
  requiredInputs: Array<{
    name: string
    description: string
    type: string
    required: boolean
  }>
  usageExamples: UsageExample[]
  bestPractices: string[]
  commonErrors: string[]
}

export function ModelUsageGuide({
  requiredInputs,
  usageExamples,
  bestPractices,
  commonErrors
}: ModelUsageGuideProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          How to Use This Model
        </CardTitle>
        <CardDescription>
          Follow these guidelines to get the most accurate results
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Required Inputs */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Required Inputs</h3>
          <div className="grid gap-3">
            {requiredInputs.map((input, index) => (
              <div key={index} className="flex items-start gap-3 bg-muted p-3 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{input.name}</span>
                    {input.required ? (
                      <Badge variant="destructive" className="text-xs">Required</Badge>
                    ) : (
                      <Badge variant="secondary" className="text-xs">Optional</Badge>
                    )}
                    <Badge variant="outline" className="text-xs">{input.type}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {input.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Usage Examples */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Usage Examples</h3>
          <div className="grid gap-4">
            {usageExamples.map((example, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="grid gap-3">
                  <div>
                    <h4 className="font-medium mb-2">Input:</h4>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-sm">
                        {JSON.stringify(example.input, null, 2)}
                      </pre>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Expected Output:</h4>
                    <div className="bg-muted p-3 rounded-md">
                      <pre className="text-sm whitespace-pre-wrap">
                        {example.expectedOutput}
                      </pre>
                    </div>
                  </div>
                  <Alert>
                    <AlertDescription>
                      {example.explanation}
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Best Practices */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
            Best Practices
          </h3>
          <ul className="space-y-2">
            {bestPractices.map((practice, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-green-500">•</span>
                <span className="text-sm">{practice}</span>
              </li>
            ))}
          </ul>
        </div>

        <Separator />

        {/* Common Errors */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <XCircle className="h-5 w-5 text-red-500" />
            Common Errors to Avoid
          </h3>
          <ul className="space-y-2">
            {commonErrors.map((error, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-red-500">•</span>
                <span className="text-sm">{error}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

