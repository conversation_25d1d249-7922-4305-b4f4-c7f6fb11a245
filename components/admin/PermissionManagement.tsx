'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"

// API endpoints
const BASE_URL = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users`

// Types
type User = {
  id: string
  name: string
  email: string
  tools: Record<string, boolean> // Tools with their enabled/disabled status
}

export default function PermissionManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)
  const [userTools, setUserTools] = useState<Record<string, boolean>>({})
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()

  // Fetch all users on component mount
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch(BASE_URL)
        if (!response.ok) throw new Error("Failed to fetch users")
        const usersData = await response.json()
        setUsers(usersData.map((user: any) => ({
          id: user.id,
          name: user.username,
          email: user.email,
          tools: user.tools || {},
        })))
      } catch (error) {
        console.error(error)
        toast({
          title: "Error",
          description: "Failed to fetch users. Please try again.",
          variant: "destructive",
        })
      }
    }
    fetchUsers()
  }, [toast])

  // Handle user selection
  const handleUserSelect = (userId: string) => {
    const user = users.find(u => u.id === userId)
    setSelectedUserId(userId)
    setUserTools(user?.tools || {})
  }

  // Toggle tool permission
  const togglePermission = (toolId: string) => {
    setUserTools(prev => ({ ...prev, [toolId]: !prev[toolId] }))
  }

  // Save updated tools for the selected user
  const handleSave = async () => {
    if (!selectedUserId) return

    setIsSaving(true)
    try {
      const response = await fetch(`${BASE_URL}/${selectedUserId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ tools: userTools }),
      })
      if (!response.ok) throw new Error("Failed to update user tools")

      toast({
        title: "Success",
        description: "User tools updated successfully.",
      })

      // Update the local user data after saving
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === selectedUserId
            ? { ...user, tools: userTools }
            : user
        )
      )
    } catch (error) {
      console.error(error)
      toast({
        title: "Error",
        description: "Failed to update user tools. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Permission Management</CardTitle>
        <CardDescription>Manage user permissions for tools</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <Select onValueChange={handleUserSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Select a user" />
            </SelectTrigger>
            <SelectContent>
              {users.map(user => (
                <SelectItem key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedUserId && (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tool</TableHead>
                  <TableHead>Enabled</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(userTools).map(([toolName, isEnabled]) => (
                  <TableRow key={toolName}>
                    <TableCell>{toolName}</TableCell>
                    <TableCell>
                      <Switch
                        checked={isEnabled}
                        onCheckedChange={() => togglePermission(toolName)}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <div className="mt-6">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
