'use client'

import { useState, useEffect } from 'react'
import axios from 'axios'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

type UserCredit = {
  id: string
  username: string
  email: string
  user_token: number
}

export default function CreditManagement() {
  const [users, setUsers] = useState<UserCredit[]>([])
  const [creditAmount, setCreditAmount] = useState<{ [key: string]: number }>({}) // Store individual credit amounts for each user

  // Fetch users from the API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users`)
        if (!response.ok) {
          throw new Error('Failed to fetch users')
        }
        const data = await response.json()
        setUsers(data)
      } catch (error) {
        console.error(error)
      }
    }
    fetchUsers()
  }, [])

  const updateCredits = async (id: string, amount: number) => {
    setUsers((prevUsers) => {
      const user = prevUsers.find((u) => u.id === id)
      if (!user) return prevUsers
  
      const newTokenValue = Math.max(0, user.user_token + amount) // Ensure tokens do not go negative
  
      // Make the API call to update the backend
      axios
        .put(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${id}`, {
          user_token: newTokenValue,
        })
        .catch((error) => {
          console.error('Failed to update user tokens', error)
          if (axios.isAxiosError(error)) {
            console.error('Error response:', error.response?.data)
          }
        })
  
      // Return the updated state
      return prevUsers.map((u) =>
        u.id === id ? { ...u, user_token: newTokenValue } : u
      )
    })
  }
  

  const handleInputChange = (id: string, value: string) => {
    setCreditAmount({ ...creditAmount, [id]: Number(value) || 0 })
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-4">Credit Management</h2>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Username</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>User Tokens</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell>{user.username}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.user_token}</TableCell>
              <TableCell>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    value={creditAmount[user.id] || ''}
                    onChange={(e) => handleInputChange(user.id, e.target.value)}
                    className="w-20"
                  />
                  <Button onClick={() => updateCredits(user.id, creditAmount[user.id] || 0)}>Add</Button>
                  <Button
                    variant="destructive"
                    onClick={() => updateCredits(user.id, -(creditAmount[user.id] || 0))}
                  >
                    Remove
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
