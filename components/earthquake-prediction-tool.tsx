"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Activity, MapPin, TrendingUp, Calculator, Clock, BarChart3, Shield, Settings, Target } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Bar<PERSON>hart, Bar } from 'recharts'
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"

interface EarthquakeData {
  pga: number
  probability: number
}

interface EarthquakeResponse {
  data: Array<{
    pga_value: number
    probability_of_exceedance: number
  }>
  metadata?: {
    target_latitude: number
    target_longitude: number
    window: number
    vs30?: number
  }
}

interface TimelineComparisonData {
  oneYear: EarthquakeData[]
  threeYear: EarthquakeData[]
  isLoading: boolean
  hasData: boolean
}

interface ReturnPeriodData {
  pga: number
  returnPeriod: number
  probability: number
}

interface PremiumCalculation {
  basePremium: number
  riskMultiplier: number
  finalPremium: number
  annualPremium: number
  deductible: number
}

interface AdvancedAnalytics {
  expectedAnnualLoss: number
  returnPeriods: ReturnPeriodData[]
  premiumCalculation: PremiumCalculation
}

// Phase 3: Insurance Features
interface DeductibleOptimization {
  deductible: number
  annualPremium: number
  outOfPocketExposure: number
  totalCost: number
  savings: number
}

interface RiskMitigation {
  measure: string
  cost: number
  pgaImprovement: number
  premiumReduction: number
  paybackPeriod: number
  description: string
}

interface ConfidenceInterval {
  lower: number
  upper: number
  confidence: number
}

interface LocationInfo {
  city?: string
  country?: string
  fullAddress?: string
  isLoading: boolean
  error?: string
}

interface AssetType {
  id: string
  name: string
  pgaTolerance: number
  description: string
}

const ASSET_TYPES: AssetType[] = [
  { id: 'residential_standard', name: 'Residential Building (Standard)', pgaTolerance: 20000, description: 'Typical residential construction' },
  { id: 'residential_reinforced', name: 'Residential Building (Reinforced)', pgaTolerance: 35000, description: 'Seismically reinforced residential' },
  { id: 'commercial_standard', name: 'Commercial Building (Standard)', pgaTolerance: 18000, description: 'Standard commercial construction' },
  { id: 'commercial_seismic', name: 'Commercial Building (Seismic Design)', pgaTolerance: 40000, description: 'Seismically designed commercial' },
  { id: 'industrial', name: 'Industrial Facility', pgaTolerance: 15000, description: 'Industrial/manufacturing facility' },
  { id: 'critical', name: 'Critical Infrastructure', pgaTolerance: 12000, description: 'Hospitals, emergency services' },
  { id: 'custom', name: 'Custom', pgaTolerance: 0, description: 'User-defined PGA tolerance' }
]

export function EarthquakePredictionTool() {
  const [formData, setFormData] = useState({
    latitude: '',
    longitude: '',
    window: '1',
    vs30: ''
  })
  const [interactiveData, setInteractiveData] = useState({
    assetValue: '500000',
    assetType: '',
    customPGA: '20000',
    riskMultiplier: '3',
    deductible: '10000'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [chartData, setChartData] = useState<EarthquakeData[]>([])
  const [error, setError] = useState<string | null>(null)
  const [hasResults, setHasResults] = useState(false)
  const [locationInfo, setLocationInfo] = useState<LocationInfo>({ isLoading: false })

  // Phase 2: Advanced Analytics State
  const [timelineComparison, setTimelineComparison] = useState<TimelineComparisonData>({
    oneYear: [],
    threeYear: [],
    isLoading: false,
    hasData: false
  })
  const [advancedAnalytics, setAdvancedAnalytics] = useState<AdvancedAnalytics | null>(null)
  const [activeTab, setActiveTab] = useState('basic')

  // Phase 3: Insurance Features State
  const [deductibleOptions, setDeductibleOptions] = useState<DeductibleOptimization[]>([])
  const [riskMitigations, setRiskMitigations] = useState<RiskMitigation[]>([])
  const [confidenceIntervals, setConfidenceIntervals] = useState<ConfidenceInterval[]>([])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError(null)
  }

  const handleWindowChange = (value: string) => {
    setFormData(prev => ({ ...prev, window: value }))
  }

  const handleAssetTypeChange = (value: string) => {
    setInteractiveData(prev => ({ ...prev, assetType: value }))

    // Auto-fill PGA tolerance for predefined asset types
    const selectedAsset = ASSET_TYPES.find(asset => asset.id === value)
    if (selectedAsset && selectedAsset.id !== 'custom') {
      setInteractiveData(prev => ({ ...prev, customPGA: selectedAsset.pgaTolerance.toString() }))
    } else if (selectedAsset?.id === 'custom') {
      setInteractiveData(prev => ({ ...prev, customPGA: '' }))
    }
  }

  const handleInteractiveChange = (field: string, value: string) => {
    setInteractiveData(prev => ({ ...prev, [field]: value }))

    // Recalculate advanced analytics when parameters change
    if ((field === 'riskMultiplier' || field === 'deductible' || field === 'assetValue') &&
        timelineComparison.hasData && timelineComparison.oneYear.length > 0) {

      const assetValue = parseFloat(field === 'assetValue' ? value : interactiveData.assetValue)
      const riskMultiplier = parseFloat(field === 'riskMultiplier' ? value : interactiveData.riskMultiplier)
      const deductible = parseFloat(field === 'deductible' ? value : interactiveData.deductible)

      const ealOneYear = calculateExpectedAnnualLoss(timelineComparison.oneYear, assetValue)
      const returnPeriods = calculateReturnPeriods(timelineComparison.oneYear)
      const premiumCalc = calculatePremium(ealOneYear, riskMultiplier, deductible)

      setAdvancedAnalytics({
        expectedAnnualLoss: ealOneYear,
        returnPeriods,
        premiumCalculation: premiumCalc
      })
    }
  }

  // Utility function for reverse geocoding
  const fetchLocationInfo = async (lat: number, lng: number): Promise<LocationInfo> => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Rekover-Earthquake-Tool/1.0'
          }
        }
      )

      if (!response.ok) {
        throw new Error('Geocoding service unavailable')
      }

      const data = await response.json()

      return {
        city: data.address?.city || data.address?.town || data.address?.village || data.address?.municipality,
        country: data.address?.country,
        fullAddress: data.display_name,
        isLoading: false
      }
    } catch (error) {
      return {
        isLoading: false,
        error: 'Unable to fetch location information'
      }
    }
  }

  // Utility function to interpolate probability at specific PGA
  const interpolateProbability = (targetPGA: number, data: EarthquakeData[]): number => {
    if (data.length === 0) return 0

    // Sort data by PGA
    const sortedData = [...data].sort((a, b) => a.pga - b.pga)

    // If target PGA is outside the range, return boundary values
    if (targetPGA <= sortedData[0].pga) return sortedData[0].probability
    if (targetPGA >= sortedData[sortedData.length - 1].pga) return sortedData[sortedData.length - 1].probability

    // Find the two points to interpolate between
    for (let i = 0; i < sortedData.length - 1; i++) {
      const current = sortedData[i]
      const next = sortedData[i + 1]

      if (targetPGA >= current.pga && targetPGA <= next.pga) {
        // Linear interpolation
        const ratio = (targetPGA - current.pga) / (next.pga - current.pga)
        return current.probability + ratio * (next.probability - current.probability)
      }
    }

    return 0
  }

  // Calculate risk assessment
  const calculateRiskAssessment = () => {
    if (!interactiveData.assetValue || !interactiveData.customPGA || chartData.length === 0) return null

    const assetValue = parseFloat(interactiveData.assetValue)
    const maxPGA = parseFloat(interactiveData.customPGA)
    const probability = interpolateProbability(maxPGA, chartData)
    const expectedLoss = (probability / 100) * assetValue

    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
    if (probability < 2) riskLevel = 'LOW'
    else if (probability < 10) riskLevel = 'MEDIUM'
    else riskLevel = 'HIGH'

    return {
      probability: probability.toFixed(1),
      expectedLoss: expectedLoss.toFixed(0),
      riskLevel,
      assetValue: assetValue.toFixed(0),
      maxPGA: maxPGA.toFixed(0),
      lossPercentage: ((expectedLoss / assetValue) * 100).toFixed(1)
    }
  }

  // Phase 2: Calculate Expected Annual Loss (EAL)
  const calculateExpectedAnnualLoss = (data: EarthquakeData[], assetValue: number): number => {
    if (data.length === 0) return 0

    // Sort data by PGA
    const sortedData = [...data].sort((a, b) => a.pga - b.pga)
    let eal = 0

    // Integrate under the curve using trapezoidal rule
    for (let i = 0; i < sortedData.length - 1; i++) {
      const current = sortedData[i]
      const next = sortedData[i + 1]

      // Calculate damage ratio based on PGA (simplified model)
      const currentDamageRatio = Math.min(current.pga / 100000, 1) // Assume 100% damage at 100,000 cm/s² (1000 m/s²)
      const nextDamageRatio = Math.min(next.pga / 100000, 1)

      // Calculate probability difference
      const probDiff = current.probability - next.probability

      // Average damage ratio for this interval
      const avgDamageRatio = (currentDamageRatio + nextDamageRatio) / 2

      // Add to EAL
      eal += (probDiff / 100) * avgDamageRatio * assetValue
    }

    return eal
  }

  // Phase 2: Calculate Return Periods
  const calculateReturnPeriods = (data: EarthquakeData[]): ReturnPeriodData[] => {
    if (data.length === 0) return []

    return data.map(point => ({
      pga: point.pga,
      probability: point.probability,
      returnPeriod: point.probability > 0 ? 100 / point.probability : Infinity
    })).filter(point => point.returnPeriod < 10000) // Filter out extremely high return periods
  }

  // Phase 2: Calculate Premium
  const calculatePremium = (eal: number, riskMultiplier: number, deductible: number): PremiumCalculation => {
    const basePremium = eal * riskMultiplier
    const adjustedPremium = Math.max(basePremium - (deductible * 0.1), basePremium * 0.5) // Deductible reduces premium

    return {
      basePremium: eal,
      riskMultiplier,
      finalPremium: adjustedPremium,
      annualPremium: adjustedPremium,
      deductible
    }
  }

  // Phase 2: Fetch Timeline Comparison Data
  const fetchTimelineComparison = async () => {
    if (!formData.latitude || !formData.longitude) return

    setTimelineComparison(prev => ({ ...prev, isLoading: true }))

    try {
      // Fetch both 1-year and 3-year data
      const [oneYearResponse, threeYearResponse] = await Promise.all([
        fetch(`/api/v1/earthquake-prediction?target_latitude=${formData.latitude}&target_longitude=${formData.longitude}&window=1${formData.vs30 ? `&Vs30=${formData.vs30}` : ''}`),
        fetch(`/api/v1/earthquake-prediction?target_latitude=${formData.latitude}&target_longitude=${formData.longitude}&window=3${formData.vs30 ? `&Vs30=${formData.vs30}` : ''}`)
      ])

      if (!oneYearResponse.ok || !threeYearResponse.ok) {
        throw new Error('Failed to fetch timeline comparison data')
      }

      const [oneYearData, threeYearData] = await Promise.all([
        oneYearResponse.json(),
        threeYearResponse.json()
      ])

      // Transform data
      const transformData = (data: any): EarthquakeData[] => {
        if (typeof data === 'object' && data !== null && !Array.isArray(data)) {
          return Object.entries(data).map(([pgaStr, probability]) => ({
            pga: parseFloat(pgaStr),
            probability: (probability as number) * 100
          })).sort((a, b) => a.pga - b.pga)
        }
        return []
      }

      const oneYear = transformData(oneYearData)
      const threeYear = transformData(threeYearData)

      setTimelineComparison({
        oneYear,
        threeYear,
        isLoading: false,
        hasData: oneYear.length > 0 && threeYear.length > 0
      })

      // Calculate advanced analytics
      if (interactiveData.assetValue) {
        const assetValue = parseFloat(interactiveData.assetValue)
        const riskMultiplier = parseFloat(interactiveData.riskMultiplier)
        const deductible = parseFloat(interactiveData.deductible)

        const ealOneYear = calculateExpectedAnnualLoss(oneYear, assetValue)
        const returnPeriods = calculateReturnPeriods(oneYear)
        const premiumCalc = calculatePremium(ealOneYear, riskMultiplier, deductible)

        setAdvancedAnalytics({
          expectedAnnualLoss: ealOneYear,
          returnPeriods,
          premiumCalculation: premiumCalc
        })

        // Phase 3: Calculate insurance features
        const deductibleOpts = calculateDeductibleOptions(ealOneYear, assetValue, riskMultiplier)
        const riskMits = calculateRiskMitigations(assetValue, parseFloat(interactiveData.customPGA), ealOneYear)
        const confIntervals = calculateConfidenceIntervals(oneYear)

        setDeductibleOptions(deductibleOpts)
        setRiskMitigations(riskMits)
        setConfidenceIntervals(confIntervals)
      }

    } catch (error) {
      console.error('Timeline comparison error:', error)
      setTimelineComparison(prev => ({ ...prev, isLoading: false }))
      toast({
        title: "Error",
        description: "Failed to fetch timeline comparison data",
        variant: "destructive",
      })
    }
  }

  // Phase 3: Deductible Optimizer
  const calculateDeductibleOptions = (eal: number, assetValue: number, riskMultiplier: number): DeductibleOptimization[] => {
    const deductibles = [0, 5000, 10000, 25000, 50000, 100000]
    const baseExposure = eal * 0.01 // 1% chance of needing to pay deductible

    return deductibles.map(deductible => {
      const premium = calculatePremium(eal, riskMultiplier, deductible)
      const outOfPocketExposure = baseExposure * deductible
      const totalCost = premium.annualPremium + outOfPocketExposure
      const savings = deductibles[0] === deductible ? 0 :
        (calculatePremium(eal, riskMultiplier, 0).annualPremium - premium.annualPremium)

      return {
        deductible,
        annualPremium: premium.annualPremium,
        outOfPocketExposure,
        totalCost,
        savings
      }
    })
  }

  // Phase 3: Risk Mitigation Suggestions
  const calculateRiskMitigations = (assetValue: number, currentPGA: number, eal: number): RiskMitigation[] => {
    const mitigations: RiskMitigation[] = [
      {
        measure: 'Seismic Retrofitting',
        cost: assetValue * 0.15, // 15% of asset value
        pgaImprovement: currentPGA * 0.3, // 30% improvement
        premiumReduction: eal * 0.25, // 25% reduction
        paybackPeriod: 0,
        description: 'Structural reinforcement to improve earthquake resistance'
      },
      {
        measure: 'Base Isolation System',
        cost: assetValue * 0.25, // 25% of asset value
        pgaImprovement: currentPGA * 0.5, // 50% improvement
        premiumReduction: eal * 0.4, // 40% reduction
        paybackPeriod: 0,
        description: 'Advanced isolation system to reduce seismic forces'
      },
      {
        measure: 'Structural Monitoring',
        cost: 50000, // Fixed cost
        pgaImprovement: currentPGA * 0.1, // 10% improvement
        premiumReduction: eal * 0.1, // 10% reduction
        paybackPeriod: 0,
        description: 'Real-time monitoring system for early damage detection'
      },
      {
        measure: 'Emergency Preparedness',
        cost: 10000, // Fixed cost
        pgaImprovement: 0, // No structural improvement
        premiumReduction: eal * 0.05, // 5% reduction
        paybackPeriod: 0,
        description: 'Comprehensive emergency response plan and training'
      }
    ]

    return mitigations.map(mitigation => ({
      ...mitigation,
      paybackPeriod: mitigation.premiumReduction > 0 ? mitigation.cost / mitigation.premiumReduction : Infinity
    }))
  }

  // Phase 3: Confidence Intervals
  const calculateConfidenceIntervals = (data: EarthquakeData[]): ConfidenceInterval[] => {
    return data.map(point => {
      // Simplified confidence interval calculation
      const variance = point.probability * 0.1 // 10% variance
      return {
        lower: Math.max(0, point.probability - variance),
        upper: Math.min(100, point.probability + variance),
        confidence: 95
      }
    })
  }

  const validateInputs = () => {
    const lat = parseFloat(formData.latitude)
    const lng = parseFloat(formData.longitude)

    if (!formData.latitude || !formData.longitude) {
      return "Latitude and longitude are required"
    }

    if (isNaN(lat) || lat < -90 || lat > 90) {
      return "Latitude must be between -90 and 90 degrees"
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      return "Longitude must be between -180 and 180 degrees"
    }

    if (formData.vs30 && (isNaN(parseFloat(formData.vs30)) || parseFloat(formData.vs30) <= 0)) {
      return "Vs30 must be a positive number"
    }



    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateInputs()
    if (validationError) {
      setError(validationError)
      return
    }

    setIsLoading(true)
    setError(null)
    setChartData([])
    setHasResults(false)

    try {
      const params = new URLSearchParams({
        target_latitude: formData.latitude,
        target_longitude: formData.longitude,
        window: formData.window
      })

      if (formData.vs30) {
        params.append('Vs30', formData.vs30)
      }

      const url = `/api/v1/earthquake-prediction?${params}`
      console.log('Making request to:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.error('API error response:', errorData)
        throw new Error(errorData.error || `API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('API response data:', data)

      // Transform the API response to chart data format
      let transformedData: EarthquakeData[] = []

      if (typeof data === 'object' && data !== null && !Array.isArray(data)) {
        // API returns an object with PGA values in cm/s² as keys and probabilities as values
        // Keep values in cm/s² for consistency
        transformedData = Object.entries(data).map(([pgaStr, probability]) => ({
          pga: parseFloat(pgaStr), // Keep in cm/s²
          probability: (probability as number) * 100 // Convert to percentage
        }))
      } else if (Array.isArray(data)) {
        // If data is directly an array
        transformedData = data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      } else if (data.data && Array.isArray(data.data)) {
        // If data is wrapped in a data property
        transformedData = data.data.map((item: any) => ({
          pga: item.pga_value || item.pga || item.x,
          probability: (item.probability_of_exceedance || item.probability || item.y) * 100
        }))
      }

      if (transformedData.length === 0) {
        throw new Error("No valid data received from API")
      }

      // Sort by PGA value for better chart visualization
      transformedData.sort((a, b) => a.pga - b.pga)

      setChartData(transformedData)
      setHasResults(true)

      // Fetch location information
      setLocationInfo({ isLoading: true })
      const locationData = await fetchLocationInfo(parseFloat(formData.latitude), parseFloat(formData.longitude))
      setLocationInfo(locationData)

      toast({
        title: "Success",
        description: `Generated earthquake prediction curve with ${transformedData.length} data points`,
      })

      // Phase 2: Fetch timeline comparison data for advanced analytics
      await fetchTimelineComparison()

    } catch (err) {
      console.error('API Error:', err)

      let errorMessage = "Failed to fetch earthquake prediction data"

      if (err instanceof Error) {
        if (err.message.includes('Failed to fetch')) {
          errorMessage = "Network error: Unable to connect to the earthquake prediction service. This could be due to CORS restrictions, network connectivity issues, or the API being temporarily unavailable."
        } else if (err.message.includes('CORS')) {
          errorMessage = "CORS error: The earthquake prediction API doesn't allow requests from this domain. This is a server-side configuration issue."
        } else if (err.message.includes('timeout')) {
          errorMessage = "Request timeout: The earthquake prediction service is taking too long to respond. Please try again."
        } else {
          errorMessage = err.message
        }
      }

      setError(errorMessage)

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setFormData({
      latitude: '',
      longitude: '',
      window: '1',
      vs30: ''
    })
    setInteractiveData({
      assetValue: '500000',
      assetType: '',
      customPGA: '20000',
      riskMultiplier: '3',
      deductible: '10000'
    })
    setChartData([])
    setError(null)
    setHasResults(false)
    setLocationInfo({ isLoading: false })
    setTimelineComparison({
      oneYear: [],
      threeYear: [],
      isLoading: false,
      hasData: false
    })
    setAdvancedAnalytics(null)
    setActiveTab('basic')
    setDeductibleOptions([])
    setRiskMitigations([])
    setConfidenceIntervals([])
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Earthquake Prediction Analysis
          </CardTitle>
          <CardDescription>
            Generate probabilistic earthquake curves and assess financial risk for insurance underwriting
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="latitude">Latitude *</Label>
                <Input
                  id="latitude"
                  name="latitude"
                  type="number"
                  step="any"
                  min="-90"
                  max="90"
                  value={formData.latitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -15.02"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -90 to 90 degrees</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="longitude">Longitude *</Label>
                <Input
                  id="longitude"
                  name="longitude"
                  type="number"
                  step="any"
                  min="-180"
                  max="180"
                  value={formData.longitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -72.15"
                  required
                />
                <p className="text-xs text-muted-foreground">Range: -180 to 180 degrees</p>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Prediction Window *</Label>
              <RadioGroup value={formData.window} onValueChange={handleWindowChange}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="1" id="window-1" />
                  <Label htmlFor="window-1">1 Year</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="3" id="window-3" />
                  <Label htmlFor="window-3">3 Years</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vs30">Vs30 (Optional)</Label>
              <Input
                id="vs30"
                name="vs30"
                type="number"
                step="any"
                min="0"
                value={formData.vs30}
                onChange={handleInputChange}
                placeholder="e.g., 760"
              />
              <p className="text-xs text-muted-foreground">
                Leave empty to use default USGS values
              </p>
            </div>



            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex gap-3">
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Prediction...
                  </>
                ) : (
                  "Generate Prediction"
                )}
              </Button>
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {hasResults && chartData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Seismic Risk Analysis Dashboard</CardTitle>
            <CardDescription>
              Comprehensive earthquake prediction and risk assessment with advanced analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-7">
                <TabsTrigger value="basic" className="flex items-center gap-1 text-xs">
                  <Activity className="h-3 w-3" />
                  Basic
                </TabsTrigger>
                <TabsTrigger value="timeline" className="flex items-center gap-1 text-xs">
                  <TrendingUp className="h-3 w-3" />
                  Timeline
                </TabsTrigger>
                <TabsTrigger value="returns" className="flex items-center gap-1 text-xs">
                  <Clock className="h-3 w-3" />
                  Returns
                </TabsTrigger>
                <TabsTrigger value="premium" className="flex items-center gap-1 text-xs">
                  <Calculator className="h-3 w-3" />
                  Premium
                </TabsTrigger>
                <TabsTrigger value="deductible" className="flex items-center gap-1 text-xs">
                  <Shield className="h-3 w-3" />
                  Deductible
                </TabsTrigger>
                <TabsTrigger value="mitigation" className="flex items-center gap-1 text-xs">
                  <Settings className="h-3 w-3" />
                  Mitigation
                </TabsTrigger>
                <TabsTrigger value="confidence" className="flex items-center gap-1 text-xs">
                  <Target className="h-3 w-3" />
                  Confidence
                </TabsTrigger>
              </TabsList>

              {/* Basic Analysis Tab */}
              <TabsContent value="basic" className="mt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Chart Section */}
              <div className="lg:col-span-2">
                <div className="h-96 w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="pga"
                        label={{ value: 'PGA (cm/s²)', position: 'insideBottom', offset: -5 }}
                        tickFormatter={(value) => Number(value).toFixed(0)}
                        type="number"
                        scale="linear"
                      />
                      <YAxis
                        label={{ value: 'Probability of Exceedance (%)', angle: -90, position: 'insideLeft' }}
                        tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                      />
                      <Tooltip
                        formatter={(value, name) => [
                          `${Number(value).toFixed(2)}${name === 'probability' ? '%' : ' cm/s²'}`,
                          name === 'probability' ? 'Probability of Exceedance' : 'PGA'
                        ]}
                        labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)} cm/s²`}
                      />
                      {/* Reference line for PGA threshold */}
                      {interactiveData.customPGA && parseFloat(interactiveData.customPGA) > 0 && (
                        <ReferenceLine
                          x={parseFloat(interactiveData.customPGA)}
                          stroke="#ef4444"
                          strokeWidth={2}
                          strokeDasharray="5 5"
                          label={{ value: `Max PGA: ${interactiveData.customPGA} cm/s²`, position: "top" }}
                        />
                      )}
                      <Line
                        type="monotone"
                        dataKey="probability"
                        stroke="#2563eb"
                        strokeWidth={2}
                        dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Interactive Controls Section */}
              <div className="lg:col-span-1">
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-3">Interactive Risk Assessment</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Adjust parameters to see real-time risk calculations
                    </p>
                  </div>

                  {/* Asset Value Slider */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="assetValueSlider" className="text-sm font-medium">Asset Value</Label>
                      <span className="text-sm font-medium">
                        €{interactiveData.assetValue ? parseInt(interactiveData.assetValue).toLocaleString() : '0'}
                      </span>
                    </div>
                    <Slider
                      id="assetValueSlider"
                      min={50000}
                      max={5000000}
                      step={50000}
                      value={[parseInt(interactiveData.assetValue) || 500000]}
                      onValueChange={(value) => handleInteractiveChange('assetValue', value[0].toString())}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>€50K</span>
                      <span>€5M</span>
                    </div>
                  </div>

                  {/* Asset Type Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="assetTypeSelect" className="text-sm font-medium">Asset Type</Label>
                    <Select value={interactiveData.assetType} onValueChange={handleAssetTypeChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {ASSET_TYPES.map((asset) => (
                          <SelectItem key={asset.id} value={asset.id}>
                            <div className="flex flex-col">
                              <span className="text-sm">{asset.name}</span>
                              <span className="text-xs text-muted-foreground">{asset.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* PGA Tolerance Slider */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="pgaSlider" className="text-sm font-medium">Max PGA Tolerance</Label>
                      <span className="text-sm font-medium">
                        {interactiveData.customPGA ? parseFloat(interactiveData.customPGA).toFixed(0) : '0'} cm/s²
                      </span>
                    </div>
                    <Slider
                      id="pgaSlider"
                      min={0}
                      max={200000}
                      step={1000}
                      value={[parseFloat(interactiveData.customPGA) || 20000]}
                      onValueChange={(value) => handleInteractiveChange('customPGA', value[0].toFixed(1))}
                      className="w-full"
                      disabled={!!(interactiveData.assetType && interactiveData.assetType !== 'custom')}
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0 cm/s²</span>
                      <span>200K cm/s²</span>
                    </div>
                    {interactiveData.assetType && interactiveData.assetType !== 'custom' && (
                      <p className="text-xs text-muted-foreground">
                        Auto-set based on asset type. Select "Custom" to adjust.
                      </p>
                    )}
                  </div>

                  {/* Risk Summary */}
                  {interactiveData.assetValue && interactiveData.customPGA && (() => {
                    const riskData = calculateRiskAssessment()
                    return riskData ? (
                      <div className="space-y-3 p-3 bg-muted/50 rounded-lg">
                        <h5 className="font-medium text-sm">Risk Summary</h5>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Damage Probability:</span>
                            <span className="font-medium">{riskData.probability}%</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Expected Loss:</span>
                            <span className="font-medium">€{parseInt(riskData.expectedLoss).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Risk Level:</span>
                            <span className={`font-medium ${
                              riskData.riskLevel === 'LOW' ? 'text-green-600' :
                              riskData.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {riskData.riskLevel}
                            </span>
                          </div>
                        </div>
                      </div>
                    ) : null
                  })()}
                </div>
              </div>
            </div>

                <div className="mt-6 pt-4 border-t text-sm text-muted-foreground">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4" />
                    <span>
                      <strong>Location:</strong>{' '}
                      {locationInfo.isLoading ? (
                        <span className="inline-flex items-center gap-1">
                          <Loader2 className="h-3 w-3 animate-spin" />
                          Loading location...
                        </span>
                      ) : locationInfo.city && locationInfo.country ? (
                        `${locationInfo.city}, ${locationInfo.country} (${formData.latitude}°, ${formData.longitude}°)`
                      ) : locationInfo.error ? (
                        `${formData.latitude}°, ${formData.longitude}° (Location unavailable)`
                      ) : (
                        `${formData.latitude}°, ${formData.longitude}°`
                      )}
                    </span>
                  </div>
                  <p>
                    <strong>Analysis Window:</strong> {formData.window} year{formData.window === '1' ? '' : 's'}
                    {formData.vs30 && <span> | <strong>Vs30:</strong> {formData.vs30}</span>}
                    {interactiveData.customPGA && <span> | <strong>PGA Threshold:</strong> {interactiveData.customPGA} cm/s²</span>}
                  </p>
                </div>
              </TabsContent>

              {/* Timeline Comparison Tab */}
              <TabsContent value="timeline" className="mt-6">
                <div className="space-y-6">
                  {timelineComparison.isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin" />
                      <span className="ml-2">Loading timeline comparison...</span>
                    </div>
                  ) : timelineComparison.hasData ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* 1-Year Chart */}
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            1-Year Risk Window
                          </h4>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart data={timelineComparison.oneYear} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="pga"
                                  label={{ value: 'PGA (cm/s²)', position: 'insideBottom', offset: -5 }}
                                  tickFormatter={(value) => Number(value).toFixed(0)}
                                />
                                <YAxis
                                  label={{ value: 'Probability (%)', angle: -90, position: 'insideLeft' }}
                                  tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                                />
                                <Tooltip
                                  formatter={(value) => [`${Number(value).toFixed(2)}%`, 'Probability']}
                                  labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)} cm/s²`}
                                />
                                <Line
                                  type="monotone"
                                  dataKey="probability"
                                  stroke="#2563eb"
                                  strokeWidth={2}
                                  dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>

                        {/* 3-Year Chart */}
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            3-Year Risk Window
                          </h4>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart data={timelineComparison.threeYear} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="pga"
                                  label={{ value: 'PGA (cm/s²)', position: 'insideBottom', offset: -5 }}
                                  tickFormatter={(value) => Number(value).toFixed(0)}
                                />
                                <YAxis
                                  label={{ value: 'Probability (%)', angle: -90, position: 'insideLeft' }}
                                  tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                                />
                                <Tooltip
                                  formatter={(value) => [`${Number(value).toFixed(2)}%`, 'Probability']}
                                  labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)} cm/s²`}
                                />
                                <Line
                                  type="monotone"
                                  dataKey="probability"
                                  stroke="#dc2626"
                                  strokeWidth={2}
                                  dot={{ fill: '#dc2626', strokeWidth: 2, r: 3 }}
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>

                      {/* Comparative Metrics */}
                      {advancedAnalytics && (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="text-2xl font-bold text-blue-600">
                              €{Math.round(advancedAnalytics.expectedAnnualLoss).toLocaleString()}
                            </div>
                            <div className="text-sm text-blue-700">Expected Annual Loss (EAL)</div>
                          </div>
                          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                            <div className="text-2xl font-bold text-green-600">
                              {timelineComparison.oneYear.length > 0 && interactiveData.customPGA ?
                                interpolateProbability(parseFloat(interactiveData.customPGA), timelineComparison.oneYear).toFixed(1) : '0'}%
                            </div>
                            <div className="text-sm text-green-700">1-Year Risk</div>
                          </div>
                          <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                            <div className="text-2xl font-bold text-red-600">
                              {timelineComparison.threeYear.length > 0 && interactiveData.customPGA ?
                                interpolateProbability(parseFloat(interactiveData.customPGA), timelineComparison.threeYear).toFixed(1) : '0'}%
                            </div>
                            <div className="text-sm text-red-700">3-Year Risk</div>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Timeline comparison data will be available after generating predictions</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Return Periods Tab */}
              <TabsContent value="returns" className="mt-6">
                <div className="space-y-6">
                  {advancedAnalytics && advancedAnalytics.returnPeriods.length > 0 ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Return Period Chart */}
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Return Period Analysis
                          </h4>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart data={advancedAnalytics.returnPeriods} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="pga"
                                  label={{ value: 'PGA (cm/s²)', position: 'insideBottom', offset: -5 }}
                                  tickFormatter={(value) => Number(value).toFixed(0)}
                                />
                                <YAxis
                                  label={{ value: 'Return Period (years)', angle: -90, position: 'insideLeft' }}
                                  scale="log"
                                  domain={[1, 1000]}
                                  tickFormatter={(value) => `${Number(value).toFixed(0)}`}
                                />
                                <Tooltip
                                  formatter={(value) => [`${Number(value).toFixed(1)} years`, 'Return Period']}
                                  labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)} cm/s²`}
                                />
                                <Line
                                  type="monotone"
                                  dataKey="returnPeriod"
                                  stroke="#7c3aed"
                                  strokeWidth={2}
                                  dot={{ fill: '#7c3aed', strokeWidth: 2, r: 3 }}
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>

                        {/* Return Period Benchmarks */}
                        <div>
                          <h4 className="font-semibold mb-3">Standard Return Periods</h4>
                          <div className="space-y-3">
                            {[50, 100, 500, 1000].map(period => {
                              const pgaForPeriod = advancedAnalytics.returnPeriods.find(rp =>
                                Math.abs(rp.returnPeriod - period) < period * 0.1
                              )
                              return (
                                <div key={period} className="flex justify-between items-center p-3 bg-muted rounded-lg">
                                  <span className="font-medium">{period}-Year Event</span>
                                  <span className="text-sm">
                                    {pgaForPeriod ? `${pgaForPeriod.pga.toFixed(0)} cm/s²` : 'N/A'}
                                  </span>
                                </div>
                              )
                            })}
                          </div>

                          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <h5 className="font-medium text-blue-800 mb-2">Understanding Return Periods</h5>
                            <p className="text-sm text-blue-700">
                              A 100-year return period means there's a 1% chance of this event occurring in any given year.
                              These are standard benchmarks used in insurance and engineering design.
                            </p>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Return period analysis will be available after generating predictions</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Premium Calculator Tab */}
              <TabsContent value="premium" className="mt-6">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Premium Controls */}
                    <div className="space-y-6">
                      <h4 className="font-semibold flex items-center gap-2">
                        <Calculator className="h-4 w-4" />
                        Premium Calculation Parameters
                      </h4>

                      {/* Risk Multiplier Slider */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="riskMultiplierSlider" className="text-sm font-medium">Risk Multiplier</Label>
                          <span className="text-sm font-medium">{interactiveData.riskMultiplier}x</span>
                        </div>
                        <Slider
                          id="riskMultiplierSlider"
                          min={1}
                          max={10}
                          step={0.5}
                          value={[parseFloat(interactiveData.riskMultiplier) || 3]}
                          onValueChange={(value) => handleInteractiveChange('riskMultiplier', value[0].toString())}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>1x (Conservative)</span>
                          <span>10x (High Risk)</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Industry standard: 2-5x for earthquake insurance
                        </p>
                      </div>

                      {/* Deductible Slider */}
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Label htmlFor="deductibleSlider" className="text-sm font-medium">Deductible</Label>
                          <span className="text-sm font-medium">
                            €{parseInt(interactiveData.deductible).toLocaleString()}
                          </span>
                        </div>
                        <Slider
                          id="deductibleSlider"
                          min={0}
                          max={100000}
                          step={5000}
                          value={[parseInt(interactiveData.deductible) || 10000]}
                          onValueChange={(value) => handleInteractiveChange('deductible', value[0].toString())}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>€0</span>
                          <span>€100K</span>
                        </div>
                      </div>
                    </div>

                    {/* Premium Results */}
                    <div className="space-y-4">
                      <h4 className="font-semibold">Premium Calculation Results</h4>
                      {advancedAnalytics ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 gap-3">
                            <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                              <span className="text-sm">Expected Annual Loss (EAL)</span>
                              <span className="font-medium">€{Math.round(advancedAnalytics.expectedAnnualLoss).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                              <span className="text-sm">Risk Multiplier</span>
                              <span className="font-medium">{advancedAnalytics.premiumCalculation.riskMultiplier}x</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                              <span className="text-sm">Deductible</span>
                              <span className="font-medium">€{Math.round(advancedAnalytics.premiumCalculation.deductible).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-primary/10 rounded-lg border border-primary/20">
                              <span className="font-medium">Annual Premium</span>
                              <span className="font-bold text-primary text-lg">
                                €{Math.round(advancedAnalytics.premiumCalculation.annualPremium).toLocaleString()}
                              </span>
                            </div>
                          </div>

                          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <h5 className="font-medium text-blue-800 mb-2">Premium Breakdown</h5>
                            <div className="space-y-1 text-sm text-blue-700">
                              <p><strong>Base Premium:</strong> €{Math.round(advancedAnalytics.premiumCalculation.basePremium).toLocaleString()} (Expected Annual Loss)</p>
                              <p><strong>Risk Adjustment:</strong> {advancedAnalytics.premiumCalculation.riskMultiplier}x multiplier</p>
                              <p><strong>Deductible Impact:</strong> Reduces premium by ~10% of deductible amount</p>
                              <p><strong>Final Premium:</strong> €{Math.round(advancedAnalytics.premiumCalculation.finalPremium).toLocaleString()}/year</p>
                            </div>
                          </div>

                          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                            <h5 className="font-medium text-yellow-800 mb-2">Premium Recommendations</h5>
                            <div className="space-y-1 text-sm text-yellow-700">
                              <p>• Standard earthquake insurance multiplier: 2-5x</p>
                              <p>• Higher deductibles reduce premiums but increase out-of-pocket exposure</p>
                              <p>• Consider risk mitigation measures to reduce base EAL</p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Premium calculations will be available after generating predictions</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Deductible Optimizer Tab */}
              <TabsContent value="deductible" className="mt-6">
                <div className="space-y-6">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Deductible Optimization
                  </h4>
                  {deductibleOptions.length > 0 ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Deductible Options Chart */}
                        <div>
                          <h5 className="font-medium mb-3">Cost Comparison</h5>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart data={deductibleOptions} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="deductible"
                                  tickFormatter={(value) => `€${(value / 1000).toFixed(0)}K`}
                                />
                                <YAxis
                                  tickFormatter={(value) => `€${(value / 1000).toFixed(0)}K`}
                                />
                                <Tooltip
                                  formatter={(value, name) => [
                                    `€${Math.round(value as number).toLocaleString()}`,
                                    name === 'totalCost' ? 'Total Annual Cost' :
                                    name === 'annualPremium' ? 'Annual Premium' : 'Out-of-Pocket Exposure'
                                  ]}
                                  labelFormatter={(label) => `Deductible: €${Number(label).toLocaleString()}`}
                                />
                                <Bar dataKey="annualPremium" stackId="cost" fill="#3b82f6" name="annualPremium" />
                                <Bar dataKey="outOfPocketExposure" stackId="cost" fill="#ef4444" name="outOfPocketExposure" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>
                        </div>

                        {/* Deductible Recommendations */}
                        <div>
                          <h5 className="font-medium mb-3">Optimization Results</h5>
                          <div className="space-y-3">
                            {deductibleOptions.map((option, index) => (
                              <div key={option.deductible} className={`p-3 rounded-lg border ${
                                option.totalCost === Math.min(...deductibleOptions.map(o => o.totalCost))
                                  ? 'bg-green-50 border-green-200'
                                  : 'bg-muted'
                              }`}>
                                <div className="flex justify-between items-center mb-2">
                                  <span className="font-medium">
                                    €{option.deductible.toLocaleString()} Deductible
                                    {option.totalCost === Math.min(...deductibleOptions.map(o => o.totalCost)) &&
                                      <span className="ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded">OPTIMAL</span>
                                    }
                                  </span>
                                  <span className="text-sm font-medium">
                                    €{Math.round(option.totalCost).toLocaleString()}/year
                                  </span>
                                </div>
                                <div className="text-xs space-y-1">
                                  <div className="flex justify-between">
                                    <span>Premium:</span>
                                    <span>€{Math.round(option.annualPremium).toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Expected Out-of-Pocket:</span>
                                    <span>€{Math.round(option.outOfPocketExposure).toLocaleString()}</span>
                                  </div>
                                  {option.savings > 0 && (
                                    <div className="flex justify-between text-green-600">
                                      <span>Annual Savings:</span>
                                      <span>€{Math.round(option.savings).toLocaleString()}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>

                          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <h6 className="font-medium text-blue-800 mb-2">Optimization Strategy</h6>
                            <p className="text-sm text-blue-700">
                              The optimal deductible minimizes your total annual cost (premium + expected out-of-pocket expenses).
                              Higher deductibles reduce premiums but increase your financial exposure in case of a claim.
                            </p>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Deductible optimization will be available after generating predictions</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Risk Mitigation Tab */}
              <TabsContent value="mitigation" className="mt-6">
                <div className="space-y-6">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Risk Mitigation Strategies
                  </h4>
                  {riskMitigations.length > 0 ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Mitigation Options */}
                        <div className="space-y-4">
                          <h5 className="font-medium">Available Measures</h5>
                          {riskMitigations.map((mitigation, index) => (
                            <div key={index} className="p-4 border rounded-lg">
                              <div className="flex justify-between items-start mb-2">
                                <h6 className="font-medium">{mitigation.measure}</h6>
                                <span className={`text-xs px-2 py-1 rounded ${
                                  mitigation.paybackPeriod < 5 ? 'bg-green-100 text-green-800' :
                                  mitigation.paybackPeriod < 10 ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {mitigation.paybackPeriod === Infinity ? 'No payback' :
                                   `${mitigation.paybackPeriod.toFixed(1)} years`}
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground mb-3">{mitigation.description}</p>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div>
                                  <span className="font-medium">Cost:</span>
                                  <div>€{Math.round(mitigation.cost).toLocaleString()}</div>
                                </div>
                                <div>
                                  <span className="font-medium">Annual Savings:</span>
                                  <div>€{Math.round(mitigation.premiumReduction).toLocaleString()}</div>
                                </div>
                                <div>
                                  <span className="font-medium">PGA Improvement:</span>
                                  <div>{Math.round(mitigation.pgaImprovement).toLocaleString()} cm/s²</div>
                                </div>
                                <div>
                                  <span className="font-medium">Risk Reduction:</span>
                                  <div>{((mitigation.premiumReduction / (advancedAnalytics?.expectedAnnualLoss || 1)) * 100).toFixed(1)}%</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        {/* Cost-Benefit Analysis */}
                        <div>
                          <h5 className="font-medium mb-3">Cost-Benefit Analysis</h5>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <BarChart data={riskMitigations} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="measure"
                                  angle={-45}
                                  textAnchor="end"
                                  height={80}
                                  fontSize={10}
                                />
                                <YAxis
                                  tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                                />
                                <Tooltip
                                  formatter={(value, name) => [
                                    `€${Math.round(value as number).toLocaleString()}`,
                                    name === 'cost' ? 'Implementation Cost' : 'Annual Premium Reduction'
                                  ]}
                                />
                                <Bar dataKey="cost" fill="#ef4444" name="cost" />
                                <Bar dataKey="premiumReduction" fill="#22c55e" name="premiumReduction" />
                              </BarChart>
                            </ResponsiveContainer>
                          </div>

                          <div className="mt-4 space-y-3">
                            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                              <h6 className="font-medium text-green-800 mb-2">Recommended Actions</h6>
                              <div className="space-y-1 text-sm text-green-700">
                                {riskMitigations
                                  .filter(m => m.paybackPeriod < 10 && m.paybackPeriod !== Infinity)
                                  .sort((a, b) => a.paybackPeriod - b.paybackPeriod)
                                  .slice(0, 2)
                                  .map((m, i) => (
                                    <p key={i}>• {m.measure} (Payback: {m.paybackPeriod.toFixed(1)} years)</p>
                                  ))
                                }
                              </div>
                            </div>

                            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <h6 className="font-medium text-blue-800 mb-2">Implementation Strategy</h6>
                              <p className="text-sm text-blue-700">
                                Prioritize measures with shorter payback periods. Consider combining multiple
                                strategies for maximum risk reduction. Consult with structural engineers for
                                detailed implementation plans.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Risk mitigation analysis will be available after generating predictions</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Confidence Intervals Tab */}
              <TabsContent value="confidence" className="mt-6">
                <div className="space-y-6">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Uncertainty Analysis
                  </h4>
                  {confidenceIntervals.length > 0 && chartData.length > 0 ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Confidence Interval Chart */}
                        <div>
                          <h5 className="font-medium mb-3">95% Confidence Intervals</h5>
                          <div className="h-80 w-full">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart data={chartData.map((point, i) => ({
                                ...point,
                                lower: confidenceIntervals[i]?.lower || point.probability,
                                upper: confidenceIntervals[i]?.upper || point.probability
                              }))} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                  dataKey="pga"
                                  label={{ value: 'PGA (cm/s²)', position: 'insideBottom', offset: -5 }}
                                  tickFormatter={(value) => Number(value).toFixed(0)}
                                />
                                <YAxis
                                  label={{ value: 'Probability (%)', angle: -90, position: 'insideLeft' }}
                                  tickFormatter={(value) => `${Number(value).toFixed(1)}%`}
                                />
                                <Tooltip
                                  formatter={(value, name) => [
                                    `${Number(value).toFixed(2)}%`,
                                    name === 'probability' ? 'Best Estimate' :
                                    name === 'lower' ? 'Lower Bound (95%)' : 'Upper Bound (95%)'
                                  ]}
                                  labelFormatter={(label) => `PGA: ${Number(label).toFixed(0)} cm/s²`}
                                />
                                <Line
                                  type="monotone"
                                  dataKey="lower"
                                  stroke="#94a3b8"
                                  strokeWidth={1}
                                  strokeDasharray="5 5"
                                  dot={false}
                                  name="lower"
                                />
                                <Line
                                  type="monotone"
                                  dataKey="upper"
                                  stroke="#94a3b8"
                                  strokeWidth={1}
                                  strokeDasharray="5 5"
                                  dot={false}
                                  name="upper"
                                />
                                <Line
                                  type="monotone"
                                  dataKey="probability"
                                  stroke="#2563eb"
                                  strokeWidth={2}
                                  dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                                  name="probability"
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>

                        {/* Uncertainty Metrics */}
                        <div>
                          <h5 className="font-medium mb-3">Uncertainty Metrics</h5>
                          <div className="space-y-4">
                            <div className="p-3 bg-muted rounded-lg">
                              <h6 className="font-medium mb-2">Model Confidence</h6>
                              <div className="text-sm space-y-1">
                                <div className="flex justify-between">
                                  <span>Confidence Level:</span>
                                  <span className="font-medium">95%</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Average Uncertainty:</span>
                                  <span className="font-medium">
                                    ±{(confidenceIntervals.reduce((sum, ci, i) =>
                                      sum + (ci.upper - ci.lower), 0) / confidenceIntervals.length).toFixed(1)}%
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Data Points:</span>
                                  <span className="font-medium">{chartData.length}</span>
                                </div>
                              </div>
                            </div>

                            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                              <h6 className="font-medium text-blue-800 mb-2">Interpretation</h6>
                              <div className="space-y-2 text-sm text-blue-700">
                                <p>
                                  <strong>Confidence Intervals:</strong> There is a 95% probability that the true
                                  exceedance probability lies within the shaded bands.
                                </p>
                                <p>
                                  <strong>Model Uncertainty:</strong> Accounts for limitations in seismic data,
                                  geological modeling, and statistical estimation.
                                </p>
                                <p>
                                  <strong>Risk Management:</strong> Consider the upper bound for conservative
                                  risk assessment and insurance pricing.
                                </p>
                              </div>
                            </div>

                            <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                              <h6 className="font-medium text-yellow-800 mb-2">Recommendations</h6>
                              <div className="space-y-1 text-sm text-yellow-700">
                                <p>• Use upper confidence bounds for conservative estimates</p>
                                <p>• Consider sensitivity analysis for critical decisions</p>
                                <p>• Update analysis with new seismic data when available</p>
                                <p>• Account for model uncertainty in risk pricing</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Uncertainty analysis will be available after generating predictions</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}



      {/* Risk Assessment Card */}
      {hasResults && interactiveData.assetValue && interactiveData.customPGA && (() => {
        const riskData = calculateRiskAssessment()
        return riskData ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Seismic Risk Assessment
              </CardTitle>
              <CardDescription>
                Financial risk analysis based on asset value and PGA tolerance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Risk Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">{riskData.probability}%</div>
                    <div className="text-sm text-muted-foreground">Damage Probability</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold text-primary">€{parseInt(riskData.expectedLoss).toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Expected Loss</div>
                  </div>
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className={`text-2xl font-bold ${
                      riskData.riskLevel === 'LOW' ? 'text-green-600' :
                      riskData.riskLevel === 'MEDIUM' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {riskData.riskLevel}
                    </div>
                    <div className="text-sm text-muted-foreground">Risk Level</div>
                  </div>
                </div>

                {/* Detailed Assessment */}
                <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-semibold text-lg">SEISMIC RISK ASSESSMENT</h4>

                  <div className="space-y-2">
                    <h5 className="font-medium">Asset Details:</h5>
                    <ul className="space-y-1 text-sm">
                      <li>
                        <strong>Location:</strong>{' '}
                        {locationInfo.city && locationInfo.country
                          ? `${locationInfo.city}, ${locationInfo.country} (${formData.latitude}°, ${formData.longitude}°)`
                          : `${formData.latitude}°, ${formData.longitude}°`
                        }
                      </li>
                      <li><strong>Asset Value:</strong> €{parseInt(riskData.assetValue).toLocaleString()}</li>
                      <li><strong>PGA Tolerance:</strong> {riskData.maxPGA} cm/s²</li>
                      <li><strong>Time Window:</strong> {formData.window} year{formData.window === '1' ? '' : 's'}</li>
                    </ul>
                  </div>

                  <div className="space-y-2">
                    <h5 className="font-medium">Risk Analysis:</h5>
                    <ul className="space-y-1 text-sm">
                      <li><strong>Probability of Damage:</strong> {riskData.probability}%</li>
                      <li><strong>Expected Loss:</strong> €{parseInt(riskData.expectedLoss).toLocaleString()} ({riskData.lossPercentage}% of asset value)</li>
                      <li><strong>Risk Level:</strong> {riskData.riskLevel}</li>
                    </ul>
                  </div>

                  <div className="mt-4 p-3 bg-background rounded border-l-4 border-l-primary">
                    <p className="text-sm">
                      <strong>Assessment Summary:</strong> There is a {riskData.probability}% chance that seismic activity exceeding {riskData.maxPGA} cm/s² could damage your €{parseInt(riskData.assetValue).toLocaleString()} asset within the next {formData.window} year{formData.window === '1' ? '' : 's'}, potentially resulting in losses up to €{parseInt(riskData.expectedLoss).toLocaleString()}.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : null
      })()}
    </div>
  )
}
