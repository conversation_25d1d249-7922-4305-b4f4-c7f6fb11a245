"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, Tooltip as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import { DollarSign, Clock, FileText, TrendingUp } from "lucide-react";


const API_URL = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claims`;

interface Claim {
  claim_id: string;
  claim_amount: number;
  claim_date: string;
  claim_status: string;
  claim_type: string;
  resolution_timeline_estimate: number;
  claim_location: string;
}

interface ChartData {
  date?: string;
  count?: number;
  name?: string;
  value?: number;
  month?: string;
  amount?: number;
  time?: number;
}

export default function ClaimsDashboard() {
  const [claims, setClaims] = useState<Claim[]>([]);
  const [claimsOverTime, setClaimsOverTime] = useState<ChartData[]>([]);
  const [statusDistribution, setStatusDistribution] = useState<ChartData[]>([]);
  const [typeDistribution, setTypeDistribution] = useState<ChartData[]>([]);
  const [monthlyAvgAmount, setMonthlyAvgAmount] = useState<ChartData[]>([]);
  const [processingTime, setProcessingTime] = useState<ChartData[]>([]);
  const [locationDistribution, setLocationDistribution] = useState<ChartData[]>([]);
  const [totalClaims, setTotalClaims] = useState<number>(0);
  const [averageClaimAmount, setAverageClaimAmount] = useState<number>(0);
  const [averageProcessingTime, setAverageProcessingTime] = useState<number>(0);

  useEffect(() => {
    fetch(API_URL)
      .then((res) => res.json())
      .then((data: Claim[]) => {
        setClaims(data);
        processChartData(data);
      });
  }, []);

  const processChartData = (claims: Claim[]) => {
    const claimsByDate: Record<string, number> = {};
    const statusCount: Record<string, number> = {};
    const typeCount: Record<string, number> = {};
    const avgAmountByMonth: Record<string, number> = {};
    const processingTimeCount: Record<number, number> = {};
    const locationCount: Record<string, number> = {};
    let totalAmount = 0;
    let totalTime = 0;

    claims.forEach((claim) => {
      totalAmount += claim.claim_amount;
      totalTime += claim.resolution_timeline_estimate;
      const date = new Date(claim.claim_date).toISOString().split("T")[0];
      claimsByDate[date] = (claimsByDate[date] || 0) + 1;
      statusCount[claim.claim_status] = (statusCount[claim.claim_status] || 0) + 1;
      typeCount[claim.claim_type] = (typeCount[claim.claim_type] || 0) + 1;
      const month = new Date(claim.claim_date).toISOString().slice(0, 7);
      avgAmountByMonth[month] = (avgAmountByMonth[month] || 0) + claim.claim_amount;
      processingTimeCount[claim.resolution_timeline_estimate] =
        (processingTimeCount[claim.resolution_timeline_estimate] || 0) + 1;
      locationCount[claim.claim_location] = (locationCount[claim.claim_location] || 0) + 1;
    });

    setTotalClaims(claims.length);
    setAverageClaimAmount(claims.length ? parseFloat((totalAmount / claims.length).toFixed(2)) : 0);
    setAverageProcessingTime(claims.length ? parseFloat((totalTime / claims.length).toFixed(2)) : 0);
    setClaimsOverTime(Object.entries(claimsByDate).map(([date, count]) => ({ date, count })));
    setStatusDistribution(Object.entries(statusCount).map(([name, value]) => ({ name, value })));
    setTypeDistribution(Object.entries(typeCount).map(([name, count]) => ({ name, count })));
    setMonthlyAvgAmount(Object.entries(avgAmountByMonth).map(([month, amount]) => ({ month, amount })));
    setProcessingTime(Object.entries(processingTimeCount).map(([time, count]) => ({ time: Number(time), count })));
    setLocationDistribution(Object.entries(locationCount).map(([name, value]) => ({ name, value })));
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader><CardTitle>Total Claims</CardTitle></CardHeader>
          <CardContent className="text-2xl font-bold">{totalClaims}</CardContent>
        </Card>
        <Card>
          <CardHeader><CardTitle>Average Claim Amount</CardTitle></CardHeader>
          <CardContent className="text-2xl font-bold">${averageClaimAmount}</CardContent>
        </Card>
        <Card>
          <CardHeader><CardTitle>Average Processing Time</CardTitle></CardHeader>
          <CardContent className="text-2xl font-bold">{averageProcessingTime} days</CardContent>
        </Card>
      </div>
      {/* Claims Over Time (Line Chart) */}
      <Card>
        <CardHeader>
          <CardTitle>Claims Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={claimsOverTime}>
              <XAxis dataKey="date" />
              <YAxis />
              <ChartTooltip />
              <Line type="monotone" dataKey="count" stroke="#4CAF50" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Claim Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusDistribution.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={index % 2 ? "#FF6384" : "#36A2EB"}
                    />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Claim Types</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={typeDistribution}>
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip />
                <Bar dataKey="count" fill="#FFC107" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Monthly Average Claim Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyAvgAmount}>
                <XAxis dataKey="month" />
                <YAxis />
                <ChartTooltip />
                <Line type="monotone" dataKey="amount" stroke="#FF9800" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Claim Processing Time</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={processingTime}>
                <XAxis dataKey="time" />
                <YAxis />
                <ChartTooltip />
                <Bar dataKey="count" fill="#03A9F4" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Claims by Location</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={locationDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8BC34A"
                  dataKey="value"
                >
                  {locationDistribution.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={index % 2 ? "#FF9800" : "#009688"}
                    />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}