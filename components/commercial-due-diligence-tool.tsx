"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { FileUploader } from "@/components/file-uploader"
import { Loader2, FileText, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface DueDiligenceReport {
  companyName: string
  summary: string
  financialHealthScore: number
  riskScore: number
  keyFindings: string[]
  recommendations: string[]
  financialMetrics: {
    revenue: number
    profitMargin: number
    debtToEquityRatio: number
    currentRatio: number
    returnOnEquity: number
  }
  industryComparison: {
    metric: string
    companyValue: number
    industryAverage: number
  }[]
  riskFactors: {
    factor: string
    severity: 'Low' | 'Medium' | 'High'
  }[]
}

export function CommercialDueDiligenceTool() {
  const [vatNumber, setVatNumber] = useState('')
  const [additionalFiles, setAdditionalFiles] = useState<File[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [report, setReport] = useState<DueDiligenceReport | null>(null)
  const [progress, setProgress] = useState(0)

  const handleFileUpload = (files: File[]) => {
    setAdditionalFiles(files)
  }

  const handleProcess = async () => {
    if (!vatNumber) {
      toast({
        title: "VAT Number Required",
        description: "Please enter a VAT number to proceed with the due diligence.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setReport(null)
    setProgress(0)

    try {
      // Simulate processing steps
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      // Mock report
      const mockReport: DueDiligenceReport = {
        companyName: "TechInnovate Solutions Ltd",
        summary: "TechInnovate Solutions Ltd demonstrates strong financial health with moderate risk factors. The company shows consistent revenue growth and profitability, but faces challenges in an increasingly competitive market landscape. Overall, the company presents a favorable investment opportunity with manageable risks.",
        financialHealthScore: 78,
        riskScore: 35,
        keyFindings: [
          "Consistent revenue growth of 15% year-over-year for the past 3 years",
          "Strong profit margins above industry average",
          "Healthy cash reserves, but increasing accounts receivable",
          "Moderate debt levels with a well-structured repayment plan",
          "Potential supply chain vulnerabilities identified"
        ],
        recommendations: [
          "Consider diversifying supplier base to mitigate supply chain risks",
          "Implement stricter credit control measures to manage growing accounts receivable",
          "Explore opportunities for strategic acquisitions to consolidate market position",
          "Increase investment in R&D to maintain competitive edge in rapidly evolving tech sector",
          "Develop a comprehensive ESG strategy to align with growing investor expectations"
        ],
        financialMetrics: {
          revenue: ********,
          profitMargin: 0.18,
          debtToEquityRatio: 0.4,
          currentRatio: 2.1,
          returnOnEquity: 0.22
        },
        industryComparison: [
          { metric: "Profit Margin", companyValue: 18, industryAverage: 15 },
          { metric: "Revenue Growth", companyValue: 15, industryAverage: 12 },
          { metric: "Debt to Equity", companyValue: 0.4, industryAverage: 0.5 },
          { metric: "Return on Equity", companyValue: 22, industryAverage: 18 }
        ],
        riskFactors: [
          { factor: "Market Competition", severity: "High" },
          { factor: "Technology Obsolescence", severity: "Medium" },
          { factor: "Regulatory Compliance", severity: "Low" },
          { factor: "Supply Chain Disruption", severity: "Medium" },
          { factor: "Cybersecurity Threats", severity: "High" }
        ]
      }

      setReport(mockReport)
      toast({
        title: "Due Diligence Complete",
        description: "The commercial due diligence report has been generated successfully.",
      })
    } catch (error) {
      toast({
        title: "Processing Failed",
        description: "There was an error generating the due diligence report. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Commercial Due Diligence Tool</CardTitle>
        <CardDescription>Enter a company's VAT number and upload additional documents for a comprehensive due diligence analysis</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="vat-number">VAT Number</Label>
            <Input
              id="vat-number"
              placeholder="Enter VAT number"
              value={vatNumber}
              onChange={(e) => setVatNumber(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label>Additional Documents (Optional)</Label>
            <FileUploader
              id="additional-docs"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
              onFilesSelected={handleFileUpload}
              multiple
            />
          </div>
          <Button onClick={handleProcess} disabled={isProcessing || !vatNumber}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generate Due Diligence Report
              </>
            )}
          </Button>

          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground">Analyzing company data... {progress}%</p>
            </div>
          )}

          {report && (
            <div className="mt-6 space-y-4">
              <Tabs defaultValue="summary" className="w-full">
                <TabsList>
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                  <TabsTrigger value="financial">Financial Analysis</TabsTrigger>
                  <TabsTrigger value="risks">Risk Assessment</TabsTrigger>
                  <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                </TabsList>
                <TabsContent value="summary">
                  <Card>
                    <CardHeader>
                      <CardTitle>{report.companyName}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm mb-4">{report.summary}</p>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Financial Health Score</h3>
                          <div className="flex items-center">
                            <Progress value={report.financialHealthScore} className="w-full mr-2" />
                            <span className="font-bold">{report.financialHealthScore}/100</span>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Risk Score</h3>
                          <div className="flex items-center">
                            <Progress value={report.riskScore} className="w-full mr-2" />
                            <span className="font-bold">{report.riskScore}/100</span>
                          </div>
                        </div>
                      </div>
                      <h3 className="text-lg font-semibold mt-4 mb-2">Key Findings</h3>
                      <ul className="list-disc list-inside space-y-1">
                        {report.keyFindings.map((finding, index) => (
                          <li key={index} className="text-sm">{finding}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="financial">
                  <Card>
                    <CardHeader>
                      <CardTitle>Financial Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <h3 className="text-lg font-semibold mb-2">Key Financial Metrics</h3>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableCell className="font-medium">Revenue</TableCell>
                            <TableCell>${report.financialMetrics.revenue.toLocaleString()}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Profit Margin</TableCell>
                            <TableCell>{(report.financialMetrics.profitMargin * 100).toFixed(2)}%</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Debt to Equity Ratio</TableCell>
                            <TableCell>{report.financialMetrics.debtToEquityRatio.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Current Ratio</TableCell>
                            <TableCell>{report.financialMetrics.currentRatio.toFixed(2)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Return on Equity</TableCell>
                            <TableCell>{(report.financialMetrics.returnOnEquity * 100).toFixed(2)}%</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                      <h3 className="text-lg font-semibold mt-4 mb-2">Industry Comparison</h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Metric</TableHead>
                            <TableHead>Company</TableHead>
                            <TableHead>Industry Average</TableHead>
                            <TableHead>Performance</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {report.industryComparison.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">{item.metric}</TableCell>
                              <TableCell>{item.companyValue}%</TableCell>
                              <TableCell>{item.industryAverage}%</TableCell>
                              <TableCell>
                                {item.companyValue > item.industryAverage ? (
                                  <Badge variant="default" className="flex items-center w-fit">
                                    <TrendingUp className="mr-1 h-4 w-4" />
                                    Above Average
                                  </Badge>
                                ) : (
                                  <Badge variant="secondary" className="flex items-center w-fit">
                                    <TrendingDown className="mr-1 h-4 w-4" />
                                    Below Average
                                  </Badge>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="risks">
                  <Card>
                    <CardHeader>
                      <CardTitle>Risk Assessment</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Risk Factor</TableHead>
                            <TableHead>Severity</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {report.riskFactors.map((risk, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">{risk.factor}</TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    risk.severity === 'High'
                                      ? 'destructive'
                                      : risk.severity === 'Medium'
                                      ? 'default'
                                      : 'secondary'
                                  }
                                >
                                  {risk.severity}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="recommendations">
                  <Card>
                    <CardHeader>
                      <CardTitle>Recommendations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {report.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start">
                            <AlertTriangle className="mr-2 h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                            <span>{recommendation}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

