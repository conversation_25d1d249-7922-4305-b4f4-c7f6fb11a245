"use client"

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface CLVInfo {
  customerAge: string;
  customerIncome: string;
  productCategory: string;
  purchaseFrequency: string;
  averagePurchaseValue: string;
  customerTenure: string;
}

interface CLVFormProps {
  onSubmit: (clvInfo: CLVInfo) => void;
}

export function CLVForm({ onSubmit }: CLVFormProps) {
  const [clvInfo, setClvInfo] = useState<CLVInfo>({
    customerAge: '',
    customerIncome: '',
    productCategory: '',
    purchaseFrequency: '',
    averagePurchaseValue: '',
    customerTenure: '',
  });

  const handleChange = (field: keyof CLVInfo, value: string) => {
    setClvInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(clvInfo);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="customerAge">Customer Age</Label>
          <Input
            id="customerAge"
            type="number"
            value={clvInfo.customerAge}
            onChange={(e) => handleChange('customerAge', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="customerIncome">Annual Income</Label>
          <Input
            id="customerIncome"
            type="number"
            value={clvInfo.customerIncome}
            onChange={(e) => handleChange('customerIncome', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="productCategory">Product Category</Label>
          <Select 
            value={clvInfo.productCategory} 
            onValueChange={(value) => handleChange('productCategory', value)}
          >
            <SelectTrigger id="productCategory">
              <SelectValue placeholder="Select product category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="auto">Auto Insurance</SelectItem>
              <SelectItem value="home">Home Insurance</SelectItem>
              <SelectItem value="life">Life Insurance</SelectItem>
              <SelectItem value="health">Health Insurance</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="purchaseFrequency">Purchase Frequency (per year)</Label>
          <Input
            id="purchaseFrequency"
            type="number"
            value={clvInfo.purchaseFrequency}
            onChange={(e) => handleChange('purchaseFrequency', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="averagePurchaseValue">Average Purchase Value</Label>
          <Input
            id="averagePurchaseValue"
            type="number"
            value={clvInfo.averagePurchaseValue}
            onChange={(e) => handleChange('averagePurchaseValue', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="customerTenure">Customer Tenure (years)</Label>
          <Input
            id="customerTenure"
            type="number"
            value={clvInfo.customerTenure}
            onChange={(e) => handleChange('customerTenure', e.target.value)}
            required
          />
        </div>
      </div>
      <Button type="submit">Calculate CLV</Button>
    </form>
  );
}

