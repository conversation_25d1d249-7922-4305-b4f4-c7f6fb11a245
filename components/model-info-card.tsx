import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Brain, Cpu, FileOutput, ChevronDown } from 'lucide-react'
import { Separator } from "@/components/ui/separator"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { Button } from "@/components/ui/button"
import { useState } from "react"

interface ModelInfoCardProps {
  title: string
  description: string
  purpose: string[]
  methodology: string[]
  outputs: string[]
  accuracy?: number
  type: "classification" | "regression" | "clustering" | "nlp" | "tool" | "lookup"
}

export function ModelInfoCard({
  title,
  description,
  purpose,
  methodology,
  outputs,
  accuracy,
  type
}: ModelInfoCardProps) {
  const [isOpen, setIsOpen] = useState(true)

  return (
    <Card className="border-0 shadow-none">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CardHeader className="px-0">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">{title}</CardTitle>
                <div className="flex items-center gap-2">
                  <div className="flex gap-2">
                    <Badge variant="outline" className="capitalize">
                      {type}
                    </Badge>
                    {accuracy && (
                      <Badge variant="secondary">
                        {accuracy}% Accuracy
                      </Badge>
                    )}
                  </div>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="w-9 p-0">
                      <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isOpen ? "" : "-rotate-90"}`} />
                      <span className="sr-only">Toggle model info</span>
                    </Button>
                  </CollapsibleTrigger>
                </div>
              </div>
              <CardDescription className="mt-2">{description}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CollapsibleContent>
          <CardContent className="grid gap-6 px-0">
            <div>
              <h3 className="text-lg font-semibold flex items-center mb-3">
                <Brain className="mr-2 h-5 w-5" />
                Purpose
              </h3>
              <ul className="list-disc list-inside space-y-1">
                {purpose.map((item, index) => (
                  <li key={index} className="text-muted-foreground">
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold flex items-center mb-3">
                <Cpu className="mr-2 h-5 w-5" />
                How it Works
              </h3>
              <ul className="list-disc list-inside space-y-1">
                {methodology.map((item, index) => (
                  <li key={index} className="text-muted-foreground">
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold flex items-center mb-3">
                <FileOutput className="mr-2 h-5 w-5" />
                Model Outputs
              </h3>
              <ul className="list-disc list-inside space-y-1">
                {outputs.map((item, index) => (
                  <li key={index} className="text-muted-foreground">
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

