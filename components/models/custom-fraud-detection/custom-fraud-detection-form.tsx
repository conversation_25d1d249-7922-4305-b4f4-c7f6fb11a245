"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, X, Loader2 } from 'lucide-react'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const suggestedRedFlags = [
  "Check if the customer might be submitting a temporary disability to work due to the tomato harvest that happens every year in June",
  "Verify if the claim amount is suspiciously close to the policy limit",
  "Look for multiple claims from the same address within a short timeframe",
  "Check if the claimant has a history of filing claims just before policy expiration",
  "Identify if the reported injury is inconsistent with the described accident",
  "Verify if the claimant's social media activity contradicts the claimed injury",
  "Check for unusually detailed knowledge of insurance terms and processes",
  "Look for claims filed immediately after policy inception or coverage increase",
  "Identify if the claimant is overly pushy about quick settlement",
  "Verify if there's a lack of witnesses in situations where witnesses would be expected"
]

interface CustomFraudDetectionFormProps {
  onSubmit: (formData: { context: string, redFlags: string[] }) => void
  analyzing: boolean
}

export function CustomFraudDetectionForm({ onSubmit, analyzing }: CustomFraudDetectionFormProps) {
  const [context, setContext] = useState("")
  const [redFlags, setRedFlags] = useState<string[]>([])
  const [newRedFlag, setNewRedFlag] = useState("")

  const handleAddRedFlag = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault() // Prevent form submission
    if (newRedFlag.trim()) {
      setRedFlags([...redFlags, newRedFlag.trim()])
      setNewRedFlag("")
    }
  }

  const handleRemoveRedFlag = (index: number) => {
    setRedFlags(redFlags.filter((_, i) => i !== index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ context, redFlags })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="context">Context</Label>
        <Textarea
          id="context"
          placeholder="Enter the context to analyze..."
          value={context}
          onChange={(e) => setContext(e.target.value)}
          rows={5}
        />
      </div>
      <div>
        <Label htmlFor="newRedFlag">Add Red Flag</Label>
        <div className="flex space-x-2">
          <Input
            id="newRedFlag"
            placeholder="Enter a new red flag..."
            value={newRedFlag}
            onChange={(e) => setNewRedFlag(e.target.value)}
          />
          <Button type="button" onClick={handleAddRedFlag}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Current Red Flags</h3>
        <ScrollArea className="h-40 w-full rounded-md border p-4">
          {redFlags.map((flag, index) => (
            <div key={index} className="flex items-center justify-between mb-2">
              <span className="text-sm">{flag}</span>
              <Button variant="ghost" size="sm" onClick={() => handleRemoveRedFlag(index)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </ScrollArea>
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Suggested Red Flags</h3>
        <ScrollArea className="h-40 w-full rounded-md border p-4">
          {suggestedRedFlags.map((flag, index) => (
            <div key={index} className="flex items-center justify-between mb-2">
              <span className="text-sm">{flag}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  setRedFlags([...redFlags, flag]);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>
          ))}
        </ScrollArea>
      </div>
      <Button type="submit" className="w-full" disabled={analyzing}>
        {analyzing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Analyzing...
          </>
        ) : (
          'Analyze'
        )}
      </Button>
    </form>
  )
}

