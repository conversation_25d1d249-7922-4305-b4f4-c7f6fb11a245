import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle } from 'lucide-react'

interface RedFlagResult {
  flag: string
  score: number
  confirmed: boolean
  comment: string
}

interface CustomFraudDetectionResultsProps {
  results: RedFlagResult[]
}

export function CustomFraudDetectionResults({ results }: CustomFraudDetectionResultsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Fraud Detection Results</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {results.map((result, index) => (
            <div key={index} className="border-b pb-4 last:border-b-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold">{result.flag}</h3>
                <Badge variant={result.confirmed ? "destructive" : "secondary"}>
                  {result.confirmed ? "Confirmed" : "Rejected"}
                </Badge>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <div className="font-medium">Score: {result.score.toFixed(2)}</div>
                {result.confirmed ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
              </div>
              <p className="text-sm text-muted-foreground">{result.comment}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

