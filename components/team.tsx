"use client";

import { motion } from "framer-motion";

interface TeamMember {
  name: string;
  role: string;
  image: string;
  color: string;
}

const teamMembers: TeamMember[] = [
  {
    name: "<PERSON>",
    role: "Founder",
    image: "/landingpage/kiko.png",
    color: "bg-pink-200",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "CTO",
    image: "/landingpage/filipe1.png",
    color: "bg-blue-100",
  },
  {
    name: "<PERSON>",
    role: "Founding Engineer",
    image: "/landingpage/ze1.png",
    color: "bg-slate-200",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "Founding Engineer",
    image: "/landingpage/gui.png",
    color: "bg-yellow-200",
  },
];

export function Team() {
  return (
    <div className="w-full">
      <div className="max-w-5xl mx-auto px-4">            
        <div className="relative flex justify-center items-center h-[600px]">
          {teamMembers.map((member, index) => {
            // Centered positions with adjusted offsets
            const cardWidth = 180; // px
            const horizontalSpacing = 240; // px, center-to-center spacing
            
            const positions = [
              { left: `calc(50% - ${1.5 * horizontalSpacing + cardWidth / 2}px)`, top: '0' },     // Kiko
              { left: `calc(50% - ${0.5 * horizontalSpacing + cardWidth / 2}px)`, top: '80px' }, // Filipe
              { left: `calc(50% + ${0.5 * horizontalSpacing - cardWidth / 2}px)`, top: '0' },    // Jose 
              { left: `calc(50% + ${1.5 * horizontalSpacing - cardWidth / 2}px)`, top: '80px' }  // Guilherme
            ];
            
            // Assign correct position based on member name, maintaining visual order from image
            let position;
            if (member.name === "Francisco") position = positions[0];
            else if (member.name === "Filipe") position = positions[1];
            else if (member.name === "Jose") position = positions[2]; 
            else if (member.name === "Guilherme") position = positions[3]; 
            
            return (
              <motion.div
                key={member.name}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.15 }}
                className={`absolute ${member.color} rounded-full overflow-hidden`}
                style={{
                  width: `${cardWidth}px`, 
                  height: '400px', 
                  left: position?.left,
                  top: position?.top,
                }}
              >
                <div className="px-4 pt-6 pb-4 text-center z-10 relative">
                  <h3 className="font-bold text-base uppercase tracking-wide">{member.name}</h3> 
                  <p className="text-gray-700 text-sm">{member.role}</p> 
                </div>
                
                <div className="absolute bottom-0 left-0 right-0 top-20 overflow-hidden"> 
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-[120%] object-cover object-center grayscale"
                    style={{
                      maxWidth: '150%',
                      objectPosition: 'center top'
                    }}
                  />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
