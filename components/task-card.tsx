"use client"

import { useState } from 'react'
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Calendar, MoreVertical, AlertCircle, Clock, Paperclip, MessageSquare, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ir<PERSON> } from 'lucide-react'
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FileUploader } from "@/components/file-uploader"

interface Task {
  id: string
  title: string
  description: string
  category: string
  priority: string
  status: string
  progress: number
  dueDate: string
  assignedBy: string
  labels: string[]
  attachments: number
  comments: number
  inputs?: {
    name: string
    type: "text" | "number" | "file" | "textarea"
    required?: boolean
    placeholder?: string
  }[]
}

interface TaskCardProps {
  task: Task
  onUpdate: (taskId: string, updates: Partial<Task>) => void
  onDelete: (taskId: string) => void
}

type TaskExecutionState = "idle" | "running" | "completed" | "failed"

export function TaskCard({ task, onUpdate, onDelete }: TaskCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isExecutionOpen, setIsExecutionOpen] = useState(false)
  const [executionState, setExecutionState] = useState<TaskExecutionState>("idle")
  const [executionProgress, setExecutionProgress] = useState(0)
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [executionResult, setExecutionResult] = useState<any>(null)

  // Default inputs if none provided
  const taskInputs = task.inputs || [
    { name: "notes", type: "textarea", placeholder: "Enter any notes or comments", required: false },
    { name: "attachments", type: "file", required: false }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-500"
      case "medium": return "bg-yellow-500"
      case "low": return "bg-green-500"
      default: return "bg-gray-500"
    }
  }

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "outline" => {
    switch (status) {
      case "completed": return "default"
      case "in_progress": return "secondary"
      default: return "outline"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const handleStatusChange = (newStatus: string) => {
    const updates: Partial<Task> = {
      status: newStatus,
      progress: newStatus === "completed" ? 100 : task.progress
    }
    onUpdate(task.id, updates)
  }

  const handleProgressUpdate = (increment: boolean) => {
    const newProgress = increment
      ? Math.min(task.progress + 10, 100)
      : Math.max(task.progress - 10, 0)
    
    const updates: Partial<Task> = {
      progress: newProgress,
      status: newProgress === 100 ? "completed" : newProgress === 0 ? "not_started" : "in_progress"
    }
    onUpdate(task.id, updates)
  }

  const handleStartTask = () => {
    setIsExecutionOpen(true)
    setExecutionState("idle")
    setExecutionProgress(0)
    setFormData({})
    setUploadedFiles([])
    setExecutionResult(null)
  }

  const handleInputChange = (name: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleExecuteTask = async () => {
    setExecutionState("running")
    setExecutionProgress(0)

    // Simulate task execution
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 500))
      setExecutionProgress(i)
    }

    // Simulate success/failure (90% success rate)
    const success = Math.random() < 0.9

    if (success) {
      setExecutionState("completed")
      setExecutionResult({
        status: "success",
        message: "Task executed successfully",
        data: {
          ...formData,
          files: uploadedFiles.map(f => f.name)
        },
        executionTime: `${(Math.random() * 2 + 1).toFixed(1)} seconds`
      })

      // Update task status
      handleStatusChange("in_progress")
    } else {
      setExecutionState("failed")
      setExecutionResult({
        status: "error",
        message: "Task execution failed. Please try again.",
        error: "Internal processing error"
      })
    }
  }

  const handleCloseExecution = () => {
    setIsExecutionOpen(false)
    setExecutionState("idle")
    setExecutionProgress(0)
    setFormData({})
    setUploadedFiles([])
    setExecutionResult(null)
  }

  return (
    <>
      <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div className={`h-1 ${getPriorityColor(task.priority)}`} />
        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold mb-1">{task.title}</h3>
              <p className="text-sm text-muted-foreground">{task.description}</p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={getStatusBadgeVariant(task.status)}>
                {task.status.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleStatusChange("not_started")}>
                    Mark as Not Started
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange("in_progress")}>
                    Mark as In Progress
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange("completed")}>
                    Mark as Completed
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-red-600">
                    <Trash className="mr-2 h-4 w-4" />
                    Delete Task
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                  Due {formatDate(task.dueDate)}
                </span>
                <span className="flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4 text-muted-foreground" />
                  {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
                </span>
              </div>
              <span className="flex items-center">
                <User className="mr-2 h-4 w-4 text-muted-foreground" />
                {task.assignedBy}
              </span>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Progress</span>
                <span>{task.progress}%</span>
              </div>
              <Progress value={task.progress} className="h-2" />
              <div className="flex justify-between space-x-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleProgressUpdate(false)}
                  disabled={task.progress === 0}
                >
                  -10%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleProgressUpdate(true)}
                  disabled={task.progress === 100}
                >
                  +10%
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {task.labels.map((label, index) => (
                <Badge key={index} variant="secondary">
                  {label}
                </Badge>
              ))}
            </div>

            <Separator />

            <div className="flex justify-between items-center">
              <div className="flex space-x-4 text-sm text-muted-foreground">
                <span className="flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  {task.category}
                </span>
                <span className="flex items-center">
                  <Paperclip className="mr-2 h-4 w-4" />
                  {task.attachments} attachments
                </span>
                <span className="flex items-center">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  {task.comments} comments
                </span>
              </div>
              <Button
                variant="default"
                size="sm"
                onClick={handleStartTask}
                className="flex items-center"
              >
                <PlayCircle className="mr-2 h-4 w-4" />
                Start Task
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the task
              and remove it from your list.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete(task.id)
                setIsDeleteDialogOpen(false)
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={isExecutionOpen} onOpenChange={handleCloseExecution}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Execute Task: {task.title}</DialogTitle>
            <DialogDescription>
              {executionState === "idle" 
                ? "Provide the required information to start this task"
                : executionState === "running"
                ? "Task is being executed..."
                : executionState === "completed"
                ? "Task completed successfully"
                : "Task execution failed"}
            </DialogDescription>
          </DialogHeader>

          {executionState === "idle" && (
            <div className="space-y-4">
              {taskInputs.map((input, index) => (
                <div key={index} className="space-y-2">
                  <Label htmlFor={input.name}>
                    {input.name.charAt(0).toUpperCase() + input.name.slice(1)}
                    {input.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  
                  {input.type === "textarea" ? (
                    <Textarea
                      id={input.name}
                      placeholder={input.placeholder}
                      value={formData[input.name] || ""}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                      required={input.required}
                    />
                  ) : input.type === "file" ? (
                    <FileUploader
                      id={input.name}
                      accept="*/*"
                      multiple
                      onFilesSelected={setUploadedFiles}
                    />
                  ) : input.type === "number" ? (
                    <Input
                      id={input.name}
                      type="number"
                      placeholder={input.placeholder}
                      value={formData[input.name] || ""}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                      required={input.required}
                    />
                  ) : (
                    <Input
                      id={input.name}
                      type="text"
                      placeholder={input.placeholder}
                      value={formData[input.name] || ""}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                      required={input.required}
                    />
                  )}
                </div>
              ))}
            </div>
          )}

          {executionState === "running" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Execution Progress</span>
                  <span>{executionProgress}%</span>
                </div>
                <Progress value={executionProgress} />
              </div>
              <p className="text-sm text-muted-foreground">
                Processing task...
              </p>
            </div>
          )}

          {executionState === "completed" && executionResult && (
            <div className="space-y-4">
              <Alert>
                <AlertDescription>
                  Task completed in {executionResult.executionTime}
                </AlertDescription>
              </Alert>
              <div className="space-y-2">
                <h4 className="font-semibold">Submitted Information:</h4>
                {Object.entries(executionResult.data).map(([key, value]: [string, any]) => (
                  key !== 'files' && (
                    <div key={key} className="bg-muted p-2 rounded">
                      <span className="font-medium">{key}:</span> {value}
                    </div>
                  )
                ))}
                {executionResult.data.files && executionResult.data.files.length > 0 && (
                  <div className="bg-muted p-2 rounded">
                    <span className="font-medium">Attached Files:</span>
                    <ul className="list-disc list-inside mt-1">
                      {executionResult.data.files.map((file: string, index: number) => (
                        <li key={index} className="text-sm">{file}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {executionState === "failed" && executionResult && (
            <Alert variant="destructive">
              <AlertDescription>
                {executionResult.message}
                <br />
                <span className="text-sm opacity-70">Error: {executionResult.error}</span>
              </AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            {executionState === "idle" && (
              <Button onClick={handleExecuteTask}>
                Execute Task
              </Button>
            )}
            {(executionState === "completed" || executionState === "failed") && (
              <Button onClick={handleCloseExecution}>
                Close
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

