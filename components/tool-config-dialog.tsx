"use client"

import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ConfigOption {
  type: 'input' | 'select';
  label: string;
  options?: string[];
  inputType?: string;
}

interface ToolConfigDialogProps {
  tool: {
    name: string;
    configOptions: ConfigOption[];
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ToolConfigDialog({ tool, open, onOpenChange }: ToolConfigDialogProps) {
  const [config, setConfig] = useState<Record<string, string>>({})

  const handleConfigChange = (label: string, value: string) => {
    setConfig(prevConfig => ({ ...prevConfig, [label]: value }))
  }

  const handleSaveConfig = () => {
    console.log(`Saving configuration for ${tool.name}:`, config)
    // Here you would typically save the configuration to your backend or local storage
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Configure {tool.name}</DialogTitle>
          <DialogDescription>
            Set up the necessary configurations for this tool.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {tool.configOptions.map((option, index) => (
            <div key={index} className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor={option.label} className="text-right">
                {option.label}
              </Label>
              {option.type === 'input' ? (
                <Input
                  id={option.label}
                  type={option.inputType || 'text'}
                  className="col-span-3"
                  value={config[option.label] || ''}
                  onChange={(e) => handleConfigChange(option.label, e.target.value)}
                />
              ) : (
                <Select
                  value={config[option.label] || ''}
                  onValueChange={(value) => handleConfigChange(option.label, value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder={`Select ${option.label}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {option.options?.map((opt, i) => (
                      <SelectItem key={i} value={opt}>
                        {opt}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleSaveConfig}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

