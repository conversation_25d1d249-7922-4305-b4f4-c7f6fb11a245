"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Star, Users, CheckCircle, AlertCircle, ShoppingCart, ExternalLink } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"

interface MarketplaceFeatureCardProps {
  feature: {
    id: string
    name: string
    description: string
    category: string
    subcategory: string
    price: number
    billingCycle: string
    features: string[]
    metrics: {
      users: number
      rating: number
      reviews: number
    }
    status: "available" | "purchased"
  }
  view?: "grid" | "list"
}

export function MarketplaceFeatureCard({ feature, view = "grid" }: MarketplaceFeatureCardProps) {
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const handlePurchase = async () => {
    setIsProcessing(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast({
      title: "Purchase successful",
      description: `${feature.name} has been activated for your account.`,
    })
    
    setIsProcessing(false)
    setIsPurchaseDialogOpen(false)
  }

  if (view === "list") {
    return (
      <Card>
        <CardContent className="flex items-start gap-4 p-6">
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold">{feature.name}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">{feature.category}</Badge>
                <Badge variant="secondary">{feature.subcategory}</Badge>
              </div>
            </div>
            
            <div className="mt-4 flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{feature.metrics.users.toLocaleString()} users</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">{feature.metrics.rating} ({feature.metrics.reviews} reviews)</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            <div className="text-right">
              <div className="text-2xl font-bold">${feature.price}</div>
              <div className="text-sm text-muted-foreground">per {feature.billingCycle}</div>
            </div>
            {feature.status === "available" ? (
              <Button onClick={() => setIsPurchaseDialogOpen(true)}>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Purchase
              </Button>
            ) : (
              <Button variant="secondary" disabled>
                <CheckCircle className="mr-2 h-4 w-4" />
                Purchased
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>{feature.name}</CardTitle>
              <CardDescription>{feature.description}</CardDescription>
            </div>
            <div className="flex gap-2">
              <Badge variant="outline">{feature.category}</Badge>
              <Badge variant="secondary">{feature.subcategory}</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{feature.metrics.users.toLocaleString()} users</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">{feature.metrics.rating} ({feature.metrics.reviews} reviews)</span>
              </div>
            </div>
            <Progress value={feature.metrics.rating * 20} className="h-2" />
          </div>

          <Separator />

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Key Features</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {feature.features.map((item, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  {item}
                </li>
              ))}
            </ul>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">${feature.price}</div>
              <div className="text-sm text-muted-foreground">per {feature.billingCycle}</div>
            </div>
            {feature.status === "available" ? (
              <Button onClick={() => setIsPurchaseDialogOpen(true)}>
                <ShoppingCart className="mr-2 h-4 w-4" />
                Purchase
              </Button>
            ) : (
              <Button variant="secondary" disabled>
                <CheckCircle className="mr-2 h-4 w-4" />
                Purchased
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={isPurchaseDialogOpen} onOpenChange={setIsPurchaseDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Purchase {feature.name}</DialogTitle>
            <DialogDescription>
              Review the details of your purchase below
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Purchase Summary</h4>
              <div className="rounded-lg border p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Feature</span>
                  <span className="font-medium">{feature.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Billing Cycle</span>
                  <span className="font-medium capitalize">{feature.billingCycle}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span>${feature.price}</span>
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                By proceeding with the purchase, you agree to our terms of service and the feature-specific terms.
              </AlertDescription>
            </Alert>

            <div className="space-y-2 text-sm text-muted-foreground">
              <p className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Instant activation after purchase
              </p>
              <p className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Cancel anytime
              </p>
              <p className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                24/7 support included
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPurchaseDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handlePurchase} disabled={isProcessing}>
              {isProcessing ? (
                <>Processing...</>
              ) : (
                <>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Complete Purchase
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

