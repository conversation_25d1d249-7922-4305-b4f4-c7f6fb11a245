"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Progress } from "@/components/ui/progress"
import { DollarSign, Calendar, BarChart2, ChevronLeft, ChevronRight, Users, Search, X, RefreshCw, CheckCircle, XCircle } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"

interface FlaggedBy {
  name: string
  avatar: string
}

interface FraudType {
  type: string
  modelScore: number
  description: string
  estimatedSavings: number
  flaggedBy: FlaggedBy[]
}

interface ClaimStatus {
  status: 'Open' | 'Closed'
  confirmedFraud: boolean
  estimatedSavings: number
  pendingSavings: number
  actualSavings: number
  observation: string
}

interface FraudClaimCardProps {
  claim: {
    claimNumber: string
    openDate: string
    urgencyLevel: string
    fraudTypes: FraudType[]
    product: string
    status: ClaimStatus
  }
}

export function FraudClaimCard({ claim }: FraudClaimCardProps) {
  const [currentFraudIndex, setCurrentFraudIndex] = useState(0)
  const [claimStatus, setClaimStatus] = useState<ClaimStatus>(claim.status)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [pendingSavings, setPendingSavings] = useState(claim.status.pendingSavings.toString())
  const [actualSavings, setActualSavings] = useState(claim.status.actualSavings.toString())
  const [confirmedFraud, setConfirmedFraud] = useState(claim.status.confirmedFraud)
  const [observation, setObservation] = useState(claim.status.observation)


  const getInitials = (name: string) => {
    const names = name.split(' ');
    return names.length > 1
      ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
      : name.slice(0, 2).toUpperCase();
  };

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case "High": return "bg-red-500";
      case "Medium": return "bg-yellow-500";
      default: return "bg-green-500";
    }
  };

  const nextFraud = () => {
    setCurrentFraudIndex((prevIndex) => 
      prevIndex === claim.fraudTypes.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevFraud = () => {
    setCurrentFraudIndex((prevIndex) => 
      prevIndex === 0 ? claim.fraudTypes.length - 1 : prevIndex - 1
    )
  }

  const currentFraud = claim.fraudTypes[currentFraudIndex] || {
    type: 'N/A',
    modelScore: 0,
    description: 'No fraud type information available.',
    estimatedSavings: 0,
    flaggedBy: []
  }

  const flaggedBy = currentFraud.flaggedBy || []

  const handleInspect = () => {
    console.log(`Inspecting claim: ${claim.claimNumber}`)
  }

  const handleStatusChange = () => {
    if (claimStatus.status === 'Open') {
      setIsModalOpen(true) // Open modal for closure details
    } else {
      setClaimStatus(prevStatus => ({
        ...prevStatus,
        status: 'Open',
        pendingSavings: 0, // Reset values when reopening
        actualSavings: 0,
        observation: ''
      }))
    }
  }

  const handleCloseConfirmation = () => {
    setClaimStatus(prevStatus => ({
      ...prevStatus,
      status: 'Closed',
      confirmedFraud,
      pendingSavings: parseFloat(pendingSavings),
      actualSavings: parseFloat(actualSavings),
      observation
    }))
    setIsModalOpen(false)
  }

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
      <div className={`h-2 ${getUrgencyColor(claim.urgencyLevel)}`} />
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold mb-1">{claim.claimNumber}</h3>
            <p className="text-sm text-gray-500">{claim.product}</p>
          </div>
          <Badge variant={claimStatus.status === "Open" ? "default" : "secondary"}>
            {claimStatus.status}
          </Badge>
        </div>

        <div className="flex items-center mb-4">
          <Calendar className="w-4 h-4 mr-2 text-gray-400" />
          <span className="text-sm">{claim.openDate}</span>
        </div>

        <div className="bg-muted p-4 rounded-lg mb-4">
          <div className="flex justify-between items-center mb-2">
            <Button variant="ghost" size="icon" onClick={prevFraud} disabled={claim.fraudTypes.length <= 1}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h4 className="text-sm font-semibold">{currentFraud.type}</h4>
            <Button variant="ghost" size="icon" onClick={nextFraud} disabled={claim.fraudTypes.length <= 1}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <BarChart2 className="w-4 h-4 mr-2 text-gray-400" />
              <span className="text-sm">Score: {currentFraud.modelScore.toFixed(2)}</span>
            </div>
            <div className="flex items-center">
              <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
              <span className="text-sm">
                ${typeof currentFraud.estimatedSavings === 'number' 
                  ? currentFraud.estimatedSavings.toLocaleString() 
                  : 'N/A'}
              </span>
            </div>
          </div>
          <Progress value={currentFraud.modelScore * 100} className="h-2 mb-2" />
          <p className="text-sm mb-2">{currentFraud.description}</p>
          <div className="flex justify-between items-center">
            <div className="flex -space-x-2">
              <TooltipProvider>
                {flaggedBy.map((flagger, index) => (
                  <Tooltip key={index}>
                    <TooltipTrigger asChild>
                      <Avatar className="w-6 h-6 border-2 border-white">
                        <AvatarImage src={flagger.avatar} alt={flagger.name} />
                        <AvatarFallback>{getInitials(flagger.name)}</AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{flagger.name}</p>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </TooltipProvider>
            </div>
            <div className="text-xs text-gray-500 flex items-center">
              <Users className="w-4 h-4 mr-1" />
              {flaggedBy.length} {flaggedBy.length === 1 ? 'person' : 'people'} flagged
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button onClick={handleStatusChange} size="sm" variant="outline" className="flex items-center">
            {claimStatus.status === 'Open' ? (
              <>
                <X className="w-4 h-4 mr-2" />
                Close
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Reopen
              </>
            )}
          </Button>
          <Button onClick={handleInspect} size="sm" className="flex items-center">
            <Search className="w-4 h-4 mr-2" />
            Inspect
          </Button>
        </div>

        {claimStatus.status === 'Closed' && (
          <div className="mt-4 flex items-center justify-center">
            {claimStatus.confirmedFraud ? (
              <CheckCircle className="h-6 w-6 text-green-600" />
            ) : (
              <XCircle className="h-6 w-6 text-red-600" />
            )}
          </div>
        )}

      </CardContent>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Claim Closure</DialogTitle>
            <DialogDescription>
              Please provide details about the claim closure.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4"> {/* Input fields for closure details */}
            <div className="flex items-center space-x-2">
              <Switch
                id="confirmed-fraud"
                checked={confirmedFraud}
                onCheckedChange={setConfirmedFraud}
              />
              <Label htmlFor="confirmed-fraud">Confirmed Fraud</Label>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="pending-savings" className="text-right">
                Pending Savings
              </Label>
              <Input
                id="pending-savings"
                type="number"
                value={pendingSavings}
                onChange={(e) => setPendingSavings(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="actual-savings" className="text-right">
                Actual Savings
              </Label>
              <Input
                id="actual-savings"
                type="number"
                value={actualSavings}
                onChange={(e) => setActualSavings(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="observation" className="text-right">
                Observation
              </Label>
              <Input
                id="observation"
                value={observation}
                onChange={(e) => setObservation(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleCloseConfirmation}>Confirm Closure</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

