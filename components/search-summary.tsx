import { format } from 'date-fns'

interface SearchSummaryProps {
  filters: {
    brand: string;
    model?: string;
    year?: string;
    fuelType?: string;
    kmRange?: [number, number];
    color?: string;
    state?: string;
  };
  date: Date;
}

export function SearchSummary({ filters, date }: SearchSummaryProps) {
  const activeFilters = Object.entries(filters).filter(([_, value]) => {
    if (Array.isArray(value)) {
      return value[0] !== 0 || value[1] !== 200000; // Check if kmRange is not default
    }
    return value && value !== "";
  });

  return (
    <div className="text-sm text-slate-600 flex flex-wrap gap-2 items-center">
      <span className="font-medium text-slate-700">Search:</span>
      {activeFilters.map(([key, value], index) => {
        if (!value) return null;
        
        let displayValue = value;
        if (key === 'kmRange' && Array.isArray(value)) {
          displayValue = `${value[0].toLocaleString()}-${value[1].toLocaleString()} km`;
        }

        return (
          <div 
            key={key}
            className="bg-slate-100 px-2 py-1 rounded-md flex items-center gap-1"
          >
            <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
            <span>{displayValue}</span>
          </div>
        );
      })}
      <span className="text-slate-400 ml-auto">
        {date.toLocaleString()}
      </span>
    </div>
  );
}

