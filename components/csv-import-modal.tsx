"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { toast } from "@/components/ui/use-toast"
import { Upload } from 'lucide-react'

interface CSVImportModalProps {
  onImport: (data: any[]) => void
  modelName: string
}

export function CSVImportModal({ onImport, modelName }: CSVImportModalProps) {
  const [file, setFile] = useState<File | null>(null)
  const [rowCount, setRowCount] = useState<number | null>(null)
  const [progress, setProgress] = useState<number>(0)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFile = e.target.files[0]
      setFile(selectedFile)
      
      // Count the number of rows in the CSV file
      const text = await selectedFile.text()
      const rows = text.split('\n').filter(row => row.trim() !== '')
      setRowCount(rows.length - 1) // Subtract 1 to exclude the header row
    }
  }

  const handleImport = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a CSV file to import.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const text = await file.text()
      const rows = text.split('\n').filter(row => row.trim() !== '')
      const headers = rows[0].split(',')
      const data = rows.slice(1).map(row => {
        const values = row.split(',')
        return headers.reduce((obj, header, index) => {
          obj[header.trim()] = values[index]?.trim()
          return obj
        }, {} as Record<string, string>)
      })

      // Simulate processing delay and update progress
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      onImport(data)
      toast({
        title: "Import successful",
        description: `${data.length} rows imported for ${modelName} predictions.`,
      })
    } catch (error) {
      console.error("Error importing CSV:", error)
      toast({
        title: "Import failed",
        description: "There was an error importing the CSV file. Please check the file format and try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Upload className="mr-2 h-4 w-4" />
          Import CSV
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Import CSV for Bulk Predictions</DialogTitle>
          <DialogDescription>
            Upload a CSV file with input data for bulk predictions using the {modelName} model.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Input
            id="csv-file"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            disabled={isProcessing}
          />
          {rowCount !== null && (
            <p className="text-sm text-muted-foreground">
              {rowCount} rows will be processed
            </p>
          )}
          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground">
                Processing... {progress}%
              </p>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button onClick={handleImport} disabled={!file || isProcessing}>
            {isProcessing ? "Processing..." : "Import and Run Predictions"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

