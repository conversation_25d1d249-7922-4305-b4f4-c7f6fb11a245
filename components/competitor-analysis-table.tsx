import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const competitors = [
  { name: "Our Company", marketShare: "35%", growth: "5.2%", customerSatisfaction: "4.5/5" },
  { name: "Competitor A", marketShare: "25%", growth: "4.8%", customerSatisfaction: "4.2/5" },
  { name: "Competitor B", marketShare: "20%", growth: "3.9%", customerSatisfaction: "4.0/5" },
  { name: "Competitor C", marketShare: "15%", growth: "4.1%", customerSatisfaction: "3.8/5" },
  { name: "Others", marketShare: "5%", growth: "2.5%", customerSatisfaction: "3.5/5" },
]

export function CompetitorAnalysisTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Company</TableHead>
          <TableHead>Market Share</TableHead>
          <TableHead>Growth</TableHead>
          <TableHead>Customer Satisfaction</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {competitors.map((competitor) => (
          <TableRow key={competitor.name}>
            <TableCell className="font-medium">{competitor.name}</TableCell>
            <TableCell>{competitor.marketShare}</TableCell>
            <TableCell>{competitor.growth}</TableCell>
            <TableCell>{competitor.customerSatisfaction}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

