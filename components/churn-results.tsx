import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function ChurnResults({ results }: { results: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Churn Prediction Results</CardTitle>
        <CardDescription>Based on the provided customer information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Churn Probability</p>
            <p className="text-2xl font-bold">{(results.churnProbability * 100).toFixed(2)}%</p>
          </div>
          <div>
            <p className="text-sm font-medium">Risk Level</p>
            <p className="text-2xl font-bold">{results.riskLevel}</p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium">Top Contributing Factors</p>
          <ul className="list-disc pl-5 mt-2">
            {results.topFactors.map((factor: string, index: number) => (
              <li key={index} className="text-sm">{factor}</li>
            ))}
          </ul>
        </div>
        <div>
          <p className="text-sm font-medium">Recommended Actions</p>
          <ul className="list-disc pl-5 mt-2">
            {results.recommendedActions.map((action: string, index: number) => (
              <li key={index} className="text-sm">{action}</li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

