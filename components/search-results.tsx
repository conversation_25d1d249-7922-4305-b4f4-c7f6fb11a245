import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { PriceStatsInfographic } from "./price-stats-infographic";
import { VehicleCard } from "./vehicle-card";
import { AiNotes } from "./ai-notes";
import { PriceVsMileagePlot } from "./charts/price-vs-mileage-plot";
import { PriceVsYearPlot } from "./charts/price-vs-year-plot";

interface SearchResultsProps {
  results: {
    totalVehicles: number;
    priceStats: {
      min: number;
      avg: number;
      max: number;
    };
    vehicles: Array<{
      id: number;
      brand: string;
      model: string;
      year: number;
      fuelType: string;
      kms: number;
      price: number;
      image: string;
    }>;
    aiNotes: string;
  };
}

export function SearchResults({ results }: SearchResultsProps) {
  function mapToPriceStatsInfographicData(
    priceStats: { min: number; avg: number; max: number },
    totalVehicles: number
  ): {
    nr_of_cars: number;
    min_price: number;
    max_price: number;
    median_price: number;
    mean_price: number;
  } {
    return {
      nr_of_cars: totalVehicles,
      min_price: priceStats.min,
      max_price: priceStats.max,
      median_price: priceStats.avg,
      mean_price: priceStats.avg,
    };
  }

  function mapToVehicleCardData(vehicle: any): any {
    return {
      Source_Url: "",
      Brand: vehicle.brand,
      Cilinder: "",
      Potency: "",
      Milage: `${vehicle.kms} km`,
      Fuel: vehicle.fuelType,
      Gearbox: "",
      Year: vehicle.year,
      Location: "",
      Seller: "",
      Price: vehicle.price,
      Currency: "USD",
      Image: vehicle.image,
    };
  }

  function mapToMilagePriceData(
    vehicles: typeof results.vehicles
  ): { Milage_Value: number; Price: number }[] {
    return vehicles.map((vehicle) => ({
      Milage_Value: vehicle.kms,
      Price: vehicle.price,
    }));
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
          <CardDescription>
            {results.totalVehicles} vehicles found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PriceStatsInfographic
            stats={mapToPriceStatsInfographicData(
              results.priceStats,
              results.totalVehicles
            )}
          />
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {results.vehicles.map((vehicle) => (
          <VehicleCard
            key={vehicle.id}
            vehicle={mapToVehicleCardData(vehicle)}
          />
        ))}
      </div>

      <AiNotes notes={results.aiNotes} />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Price vs Mileage</CardTitle>
          </CardHeader>
          <CardContent>
            <PriceVsMileagePlot data={mapToMilagePriceData(results.vehicles)} />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Price vs Year</CardTitle>
          </CardHeader>
          <CardContent>
            <PriceVsYearPlot
              data={results.vehicles.map((vehicle) => ({
                Year: vehicle.year,
                Price: vehicle.price,
              }))}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
