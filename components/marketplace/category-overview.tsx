import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LucideIcon, ChevronRight, Package, CheckCircle } from 'lucide-react'
import Link from "next/link"

interface CategoryOverviewProps {
  title: string
  description: string
  icon: LucideIcon
  items: Array<{
    id: string
    name: string
    status: "available" | "purchased"
    metrics: {
      users: number
      rating: number
    }
  }>
  metrics: {
    totalItems: number
    purchasedItems: number
    averagePrice: number
  }
}

export function CategoryOverview({
  title,
  description,
  icon: Icon,
  items,
  metrics,
}: CategoryOverviewProps) {
  const purchaseProgress = (metrics.purchasedItems / metrics.totalItems) * 100

  // Get the most popular item
  const mostPopularItem = [...items].sort((a, b) => b.metrics.users - a.metrics.users)[0]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <div className="rounded-lg bg-primary/10 p-2">
            <Icon className="h-6 w-6 text-primary" />
          </div>
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Package className="h-4 w-4" />
              Total Items
            </div>
            <p className="text-2xl font-bold">{metrics.totalItems}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4" />
              Purchased
            </div>
            <p className="text-2xl font-bold">{metrics.purchasedItems}</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Purchase Progress</span>
            <span className="font-medium">{Math.round(purchaseProgress)}%</span>
          </div>
          <Progress value={purchaseProgress} className="h-2" />
        </div>

        {mostPopularItem && (
          <div className="rounded-lg border p-3 space-y-2">
            <div className="text-sm text-muted-foreground">Most Popular</div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">{mostPopularItem.name}</div>
                <div className="text-sm text-muted-foreground">
                  {mostPopularItem.metrics.users.toLocaleString()} users
                </div>
              </div>
              <Badge variant={mostPopularItem.status === "purchased" ? "secondary" : "default"}>
                {mostPopularItem.status === "purchased" ? "Purchased" : "Available"}
              </Badge>
            </div>
          </div>
        )}

        <Button variant="outline" className="w-full" asChild>
          <Link href={`#${title.toLowerCase()}`}>
            View All {title}
            <ChevronRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}

