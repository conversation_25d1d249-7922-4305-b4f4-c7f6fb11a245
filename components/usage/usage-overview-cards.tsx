"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { TrendingDown, TrendingUp, FileText, <PERSON>ert<PERSON>riangle, Clock, PiggyBank } from 'lucide-react'

interface UsageOverviewCardsProps {
  timeRange: string
}

export function UsageOverviewCards({ timeRange }: UsageOverviewCardsProps) {
  // Mock data - in real app would fetch based on timeRange
  const stats = {
    claimsProcessed: {
      value: 128,
      change: 8.5,
      trend: "up"
    },
    fraudAlerts: {
      value: 14,
      change: 27.3,
      trend: "up"
    },
    reviewTime: {
      value: "3m 12s",
      rawValue: 192, // seconds
      change: -12.4,
      trend: "down"
    },
    savingsDetected: {
      value: 4300,
      change: 15.7,
      trend: "up"
    }
  }

  // Common styling for consistent appearance
  const cardStyle = "border-none shadow-sm";
  const iconStyle = "h-5 w-5";
  const titleStyle = "text-sm font-medium";
  const valueStyle = "text-3xl font-bold mt-2";
  const trendStyle = "text-xs flex items-center mt-2";
  const descStyle = "text-xs text-gray-500 mt-1";

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Claims Processed Card */}
      <Card className={`${cardStyle} bg-gradient-to-br from-blue-50 to-blue-100`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className={`${titleStyle} text-blue-800`}>My Claims Processed</CardTitle>
          <div className="p-2 rounded-full bg-blue-100">
            <FileText className={`${iconStyle} text-blue-600`} />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`${valueStyle} text-blue-800`}>{stats.claimsProcessed.value}</div>
          <p className={`${trendStyle} ${stats.claimsProcessed.trend === 'up' ? 'text-emerald-600' : 'text-rose-600'}`}>
            {stats.claimsProcessed.trend === 'up' ? (
              <TrendingUp className="mr-1 h-4 w-4" />
            ) : (
              <TrendingDown className="mr-1 h-4 w-4" />
            )}
            {stats.claimsProcessed.change}% vs last week
          </p>
          <p className={descStyle}>Total claims this user reviewed</p>
        </CardContent>
      </Card>

      {/* Fraud Alerts Card */}
      <Card className={`${cardStyle} bg-gradient-to-br from-amber-50 to-amber-100`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className={`${titleStyle} text-amber-800`}>Fraud Alerts Flagged</CardTitle>
          <div className="p-2 rounded-full bg-amber-100">
            <AlertTriangle className={`${iconStyle} text-amber-600`} />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`${valueStyle} text-amber-800`}>{stats.fraudAlerts.value}</div>
          <p className={`${trendStyle} ${stats.fraudAlerts.trend === 'up' ? 'text-emerald-600' : 'text-rose-600'}`}>
            {stats.fraudAlerts.trend === 'up' ? (
              <TrendingUp className="mr-1 h-4 w-4" />
            ) : (
              <TrendingDown className="mr-1 h-4 w-4" />
            )}
            {stats.fraudAlerts.change}% vs last week
          </p>
          <p className={descStyle}>Number of suspected fraud alerts</p>
        </CardContent>
      </Card>

      {/* Review Time Card */}
      <Card className={`${cardStyle} bg-gradient-to-br from-purple-50 to-purple-100`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className={`${titleStyle} text-purple-800`}>Average Review Time</CardTitle>
          <div className="p-2 rounded-full bg-purple-100">
            <Clock className={`${iconStyle} text-purple-600`} />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`${valueStyle} text-purple-800`}>{stats.reviewTime.value}</div>
          <p className={`${trendStyle} ${stats.reviewTime.trend === 'down' ? 'text-emerald-600' : 'text-rose-600'}`}>
            {stats.reviewTime.trend === 'up' ? (
              <TrendingUp className="mr-1 h-4 w-4" />
            ) : (
              <TrendingDown className="mr-1 h-4 w-4" />
            )}
            {Math.abs(stats.reviewTime.change)}% vs last week
          </p>
          <p className={descStyle}>How fast they process per claim</p>
        </CardContent>
      </Card>

      {/* Savings Card */}
      <Card className={`${cardStyle} bg-gradient-to-br from-emerald-50 to-emerald-100`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className={`${titleStyle} text-emerald-800`}>Savings Detected</CardTitle>
          <div className="p-2 rounded-full bg-emerald-100">
            <PiggyBank className={`${iconStyle} text-emerald-600`} />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`${valueStyle} text-emerald-800`}>${stats.savingsDetected.value.toLocaleString()}</div>
          <p className={`${trendStyle} ${stats.savingsDetected.trend === 'up' ? 'text-emerald-600' : 'text-rose-600'}`}>
            {stats.savingsDetected.trend === 'up' ? (
              <TrendingUp className="mr-1 h-4 w-4" />
            ) : (
              <TrendingDown className="mr-1 h-4 w-4" />
            )}
            {stats.savingsDetected.change}% vs last week
          </p>
          <p className={descStyle}>$ value saved by catching fraud or inefficiencies</p>
        </CardContent>
      </Card>
    </div>
  )
}

