"use client"

import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Lightbulb, TrendingDown, TrendingUp, DollarSign } from 'lucide-react'

interface RecommendationsCardProps {
  timeRange: string
}

export function RecommendationsCard({ timeRange }: RecommendationsCardProps) {
  // Mock data - in real app would generate based on usage patterns and timeRange
  const recommendations = [
    {
      type: "cost",
      title: "Optimize Model Usage",
      description: "Consider batching CLV calculations to reduce API calls and costs",
      impact: "High",
      potentialSavings: 120
    },
    {
      type: "usage",
      title: "Underutilized Features",
      description: "Network Analysis tool usage is below average. Consider training sessions to increase adoption.",
      impact: "Medium",
      potentialSavings: null
    },
    {
      type: "cost",
      title: "Workflow Optimization",
      description: "Automated document analysis costs can be reduced by implementing caching",
      impact: "Medium",
      potentialSavings: 85
    },
    {
      type: "usage",
      title: "Feature Adoption",
      description: "OSINT Search tool shows increasing usage. Consider upgrading plan for better rates.",
      impact: "Low",
      potentialSavings: null
    }
  ]

  return (
    <ScrollArea className="h-[400px] pr-4">
      <div className="space-y-4">
        {recommendations.map((recommendation, index) => (
          <div
            key={index}
            className="flex flex-col space-y-2 rounded-lg border p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                <span className="font-semibold">{recommendation.title}</span>
              </div>
              <Badge variant={recommendation.impact === "High" ? "destructive" : "secondary"}>
                {recommendation.impact} Impact
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {recommendation.description}
            </p>
            {recommendation.potentialSavings && (
              <div className="flex items-center text-sm text-green-500">
                <DollarSign className="h-4 w-4 mr-1" />
                Potential monthly savings: ${recommendation.potentialSavings}
              </div>
            )}
          </div>
        ))}
      </div>
    </ScrollArea>
  )
}

