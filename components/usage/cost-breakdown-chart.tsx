"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

interface CostBreakdownChartProps {
  timeRange: string
}

export function CostBreakdownChart({ timeRange }: CostBreakdownChartProps) {
  // Mock data - in real app would fetch based on timeRange
  const data = [
    { category: "Analytics", cost: 850 },
    { category: "Models", cost: 1200 },
    { category: "Workflows", cost: 650 },
    { category: "Tools", cost: 450 },
    { category: "Other", cost: 200 },
  ]

  return (
    <ChartContainer
      config={{
        cost: {
          label: "Cost ($)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="category" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="cost" fill="var(--color-cost)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

