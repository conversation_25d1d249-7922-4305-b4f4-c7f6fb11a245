"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { TrendingDown, TrendingUp } from 'lucide-react'

interface FeatureUsageTableProps {
  category: string
  timeRange: string
}

export function FeatureUsageTable({ category, timeRange }: FeatureUsageTableProps) {
  // Mock data - in real app would fetch based on category and timeRange
  const features = {
    Analytics: [
      { name: "Claims Dashboard", usage: 1250, cost: 250, trend: "up", change: 15 },
      { name: "Client Analytics", usage: 980, cost: 196, trend: "up", change: 8 },
      { name: "Policy Analysis", usage: 750, cost: 150, trend: "down", change: 3 },
    ],
    Models: [
      { name: "Claims Segmentation", usage: 850, cost: 425, trend: "up", change: 12 },
      { name: "Total Loss Prediction", usage: 620, cost: 310, trend: "up", change: 5 },
      { name: "CLV Calculation", usage: 450, cost: 225, trend: "down", change: 2 },
    ],
    Workflows: [
      { name: "Claims Processing", usage: 950, cost: 190, trend: "up", change: 10 },
      { name: "Document Analysis", usage: 780, cost: 156, trend: "down", change: 4 },
      { name: "Risk Assessment", usage: 560, cost: 112, trend: "up", change: 7 },
    ],
    Tools: [
      { name: "Market Evaluation", usage: 480, cost: 96, trend: "up", change: 9 },
      { name: "Network Analysis", usage: 320, cost: 64, trend: "down", change: 6 },
      { name: "OSINT Search", usage: 290, cost: 58, trend: "up", change: 11 },
    ],
    Other: [
      { name: "Fraud Detection", usage: 680, cost: 136, trend: "up", change: 14 },
      { name: "Leakage Analysis", usage: 450, cost: 90, trend: "down", change: 5 },
      { name: "Custom Reports", usage: 320, cost: 64, trend: "up", change: 8 },
    ],
  }

  const categoryFeatures = features[category as keyof typeof features] || []

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Feature</TableHead>
          <TableHead className="text-right">Usage Count</TableHead>
          <TableHead className="text-right">Cost ($)</TableHead>
          <TableHead className="text-right">Trend</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {categoryFeatures.map((feature) => (
          <TableRow key={feature.name}>
            <TableCell className="font-medium">{feature.name}</TableCell>
            <TableCell className="text-right">{feature.usage.toLocaleString()}</TableCell>
            <TableCell className="text-right">${feature.cost.toLocaleString()}</TableCell>
            <TableCell className="text-right">
              <Badge 
                variant={feature.trend === "up" ? "default" : "secondary"}
                className="flex items-center justify-center gap-1 ml-auto"
              >
                {feature.trend === "up" ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                {feature.change}%
              </Badge>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

