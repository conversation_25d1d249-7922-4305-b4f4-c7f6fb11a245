"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

interface FeatureUsageChartProps {
  timeRange: string
}

export function FeatureUsageChart({ timeRange }: FeatureUsageChartProps) {
  // Mock data - in real app would fetch based on timeRange
  const data = [
    { name: "Analytics", value: 35 },
    { name: "Models", value: 25 },
    { name: "Workflows", value: 20 },
    { name: "Tools", value: 15 },
    { name: "Other", value: 5 },
  ]

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

  return (
    <ChartContainer
      config={{
        value: {
          label: "Usage (%)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <ChartTooltip content={<ChartTooltipContent />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

