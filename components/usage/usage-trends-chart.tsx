"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

interface UsageTrendsChartProps {
  timeRange: string
}

export function UsageTrendsChart({ timeRange }: UsageTrendsChartProps) {
  // Mock data - in real app would fetch based on timeRange
  const data = [
    { date: "2023-01", analytics: 120, models: 150, workflows: 80, tools: 90, other: 30 },
    { date: "2023-02", analytics: 150, models: 160, workflows: 90, tools: 100, other: 35 },
    { date: "2023-03", analytics: 180, models: 170, workflows: 100, tools: 110, other: 40 },
    { date: "2023-04", analytics: 220, models: 190, workflows: 120, tools: 130, other: 45 },
    { date: "2023-05", analytics: 190, models: 200, workflows: 110, tools: 120, other: 40 },
    { date: "2023-06", analytics: 230, models: 220, workflows: 130, tools: 140, other: 50 },
  ]

  return (
    <ChartContainer
      config={{
        analytics: {
          label: "Analytics",
          color: "hsl(var(--chart-1))",
        },
        models: {
          label: "Models",
          color: "hsl(var(--chart-2))",
        },
        workflows: {
          label: "Workflows",
          color: "hsl(var(--chart-3))",
        },
        tools: {
          label: "Tools",
          color: "hsl(var(--chart-4))",
        },
        other: {
          label: "Other",
          color: "hsl(var(--chart-5))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="date" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="analytics" stroke="var(--color-analytics)" strokeWidth={2} />
          <Line type="monotone" dataKey="models" stroke="var(--color-models)" strokeWidth={2} />
          <Line type="monotone" dataKey="workflows" stroke="var(--color-workflows)" strokeWidth={2} />
          <Line type="monotone" dataKey="tools" stroke="var(--color-tools)" strokeWidth={2} />
          <Line type="monotone" dataKey="other" stroke="var(--color-other)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

