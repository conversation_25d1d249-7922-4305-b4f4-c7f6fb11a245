"use client"

import { useState } from 'react'
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search, Plus } from 'lucide-react'
import { Input } from "@/components/ui/input"

interface ChatSession {
  id: string
  title: string
  preview: string
  timestamp: string
  isActive?: boolean
}

interface ChatHistorySidebarProps {
  sessions: ChatSession[]
  onSessionSelect: (sessionId: string) => void
  activeSessionId?: string
  onNewChat: () => void
  isCreatingNewChat: boolean
}

export function ChatHistorySidebar({ 
  sessions, 
  onSessionSelect, 
  activeSessionId,
  onNewChat,
  isCreatingNewChat 
}: ChatHistorySidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const filteredSessions = sessions.filter(session => 
    session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    session.preview.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <aside className="w-80 border-r border-border/40 bg-muted/50 flex flex-col h-full">
      <div className="p-4 border-b border-border/40">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 bg-background/50 focus-visible:ring-primary/20"
          />
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-2">
            {filteredSessions.map((session) => (
              <Button
                key={session.id}
                variant={session.id === activeSessionId ? "secondary" : "ghost"}
                className={`w-full justify-start text-left h-auto py-3 px-4 mb-1 transition-colors ${
                  session.id === activeSessionId 
                    ? 'bg-primary/10 hover:bg-primary/15' 
                    : 'hover:bg-muted/80'
                }`}
                onClick={() => onSessionSelect(session.id)}
              >
                <div className="min-w-0 w-full">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="truncate font-medium text-sm flex-1">{session.title}</p>
                    <span className="text-xs text-muted-foreground shrink-0">{session.timestamp}</span>
                  </div>
                  <p className="truncate text-xs text-muted-foreground">{session.preview}</p>
                </div>
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>

      <div className="p-4 border-t border-border/40 bg-muted/50">
        <Button 
          className="w-full font-medium" 
          onClick={onNewChat}
          disabled={isCreatingNewChat}
          variant="outline"
          size="lg"
        >
          <Plus className="h-5 w-5 mr-2" />
          {isCreatingNewChat ? 'Creating...' : 'New Chat'}
        </Button>
      </div>
    </aside>
  )
} 