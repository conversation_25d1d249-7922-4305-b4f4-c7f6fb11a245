import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface TotalLossResults {
  totalLossScore: number;
  estimatedRepairCost: number;
  estimatedMarketValue: number;
  explanation: string;
}

interface TotalLossResultsProps {
  results: TotalLossResults;
}

export function TotalLossResults({ results }: TotalLossResultsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Total Loss Prediction Results</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Total Loss Probability</span>
              <span className="text-sm font-medium">{results.totalLossScore.toFixed(2)}</span>
            </div>
            <Progress value={results.totalLossScore * 100} className="h-2" />
          </div>
          <div>
            <span className="text-sm font-medium">Estimated Repair Cost</span>
            <p className="text-2xl font-bold">${results.estimatedRepairCost.toLocaleString()}</p>
          </div>
          <div>
            <span className="text-sm font-medium">Estimated Market Value</span>
            <p className="text-2xl font-bold">${results.estimatedMarketValue.toLocaleString()}</p>
          </div>
          <div>
            <span className="text-sm font-medium">Explanation</span>
            <p className="mt-1 text-sm">{results.explanation}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

