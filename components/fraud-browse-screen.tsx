"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { FraudClaimCard } from "./fraud-claim-card";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const getInitials = (name: string) => {
  const names = name.split(" ");
  return names.length > 1
    ? `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase()
    : name.slice(0, 2).toUpperCase();
};

const mockFlaggedBy = [
  { name: "<PERSON>", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Bob Williams", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Charlie Garcia", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "David Rodriguez", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Emily Davis", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Frank Wilson", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Grace Martinez", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Henry Anderson", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Isabella Thomas", avatar: "/placeholder.svg?height=32&width=32" },
  { name: "Jack Jackson", avatar: "/placeholder.svg?height=32&width=32" },
];

const generateRandomNumber = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const generateMockFraudClaims = (count: number) => {
  const claims = [];
  for (let i = 0; i < count; i++) {
    const id = (i + 1).toString(); // Generate unique ID
    const claimNumber = `FR-2023-${(i + 1).toString().padStart(3, "0")}`;
    const openDate = `2023-06-${generateRandomNumber(1, 30)
      .toString()
      .padStart(2, "0")}`;
    const estimatedSavings = generateRandomNumber(5000, 25000);
    const urgencyLevel = ["High", "Medium", "Low"][generateRandomNumber(0, 2)];
    const product = [
      "Auto Insurance",
      "Home Insurance",
      "Health Insurance",
      "Life Insurance",
    ][generateRandomNumber(0, 3)];
    const fraudType = [
      "Multiple Claims",
      "Identity Fraud",
      "Inflated Damages",
      "Staged Accident",
    ][generateRandomNumber(0, 3)];
    const modelScore = Math.random();
    const description = `Suspicious activity related to ${fraudType.toLowerCase()} detected.`;
    const flaggedBy = mockFlaggedBy.slice(0, generateRandomNumber(1, 3));
    const status: "Open" | "Closed" = ["Open", "Closed"][
      generateRandomNumber(0, 1)
    ] as "Open" | "Closed";

    claims.push({
      id, // Add the unique ID to the claim object
      claimNumber,
      openDate,
      estimatedSavings,
      urgencyLevel,
      fraudTypes: [
        {
          type: fraudType,
          modelScore,
          description,
          estimatedSavings,
          flaggedBy,
        },
      ],
      product,
      status: {
        status,
        confirmedFraud: status === "Closed" ? Math.random() < 0.5 : false,
        estimatedSavings: status === "Closed" ? estimatedSavings : 0,
        pendingSavings: status === "Open" ? estimatedSavings : 0,
        actualSavings:
          status === "Closed"
            ? Math.random() < 0.5
              ? estimatedSavings
              : 0
            : 0,
        observation:
          status === "Closed" ? "Claim closed with observation." : "",
      },
    });
  }
  return claims;
};

const additionalFraudClaims = generateMockFraudClaims(20);

// Initial data structure for fraud claims
const initialFraudClaims = [...additionalFraudClaims];

const fraudTypes = [
  "All Types",
  "Multiple Claims",
  "Identity Fraud",
  "Inflated Damages",
  "Staged Accident",
];
const products = [
  "All Products",
  "Auto Insurance",
  "Home Insurance",
  "Life Insurance",
  "Health Insurance",
];
const statuses = ["All Statuses", "Open", "Closed"];

export default function FraudBrowseScreen() {
  const [fraudClaims, setFraudClaims] = useState(initialFraudClaims);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFraudType, setSelectedFraudType] = useState("All Types");
  const [selectedProduct, setSelectedProduct] = useState("All Products");
  const [selectedStatus, setSelectedStatus] = useState("All Statuses");
  const [estimatedSavingsRange, setEstimatedSavingsRange] = useState<
    [number, number]
  >([0, 30000]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const itemsPerPage = 9;

  const filteredClaims = fraudClaims.filter((claim) => {
    const matchesSearch = claim.claimNumber
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesFraudType =
      selectedFraudType === "All Types" ||
      claim.fraudTypes.some((ft) => ft.type === selectedFraudType);
    const matchesProduct =
      selectedProduct === "All Products" || claim.product === selectedProduct;
    const matchesStatus =
      selectedStatus === "All Statuses" ||
      claim.status.status === selectedStatus;
    const matchesSavings =
      claim.estimatedSavings >= estimatedSavingsRange[0] &&
      claim.estimatedSavings <= estimatedSavingsRange[1];

    return (
      matchesSearch &&
      matchesFraudType &&
      matchesProduct &&
      matchesStatus &&
      matchesSavings
    );
  });

  const totalPages = Math.ceil(filteredClaims.length / itemsPerPage);
  const paginatedClaims = filteredClaims.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSearch = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      // In a real app, you would fetch data here
      setIsLoading(false);
    } catch (err) {
      setError("An error occurred while fetching the data. Please try again.");
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Refine your fraud claims search</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fraudType">Fraud Type</Label>
              <Select
                value={selectedFraudType}
                onValueChange={setSelectedFraudType}
              >
                <SelectTrigger id="fraudType">
                  <SelectValue placeholder="Select Fraud Type" />
                </SelectTrigger>
                <SelectContent>
                  {fraudTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="product">Product</Label>
              <Select
                value={selectedProduct}
                onValueChange={setSelectedProduct}
              >
                <SelectTrigger id="product">
                  <SelectValue placeholder="Select Product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product} value={product}>
                      {product}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="search">Search by Claim Number</Label>
              <div className="flex space-x-2">
                <Input
                  id="search"
                  type="text"
                  placeholder="Enter claim number"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Button onClick={handleSearch} disabled={isLoading}>
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    "Search"
                  )}
                </Button>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Estimated Savings Range</Label>
            <Slider
              min={0}
              max={30000}
              step={1000}
              value={estimatedSavingsRange}
              onValueChange={(value) =>
                setEstimatedSavingsRange(value as [number, number])
              }
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>${estimatedSavingsRange[0].toLocaleString()}</span>
              <span>${estimatedSavingsRange[1].toLocaleString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-96">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {paginatedClaims.map((claim) => (
              <FraudClaimCard key={claim.id} claim={claim} /> // Use unique id as key
            ))}
          </div>

          {filteredClaims.length === 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No results found</AlertTitle>
              <AlertDescription>
                Try adjusting your filters or search query.
              </AlertDescription>
            </Alert>
          )}

          {filteredClaims.length > itemsPerPage && (
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  {currentPage === 1 ? (
                    <span className="disabled">Previous</span> // Render as a non-clickable element
                  ) : (
                    <PaginationPrevious
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(prev - 1, 1))
                      }
                    />
                  )}
                </PaginationItem>
                {[...Array(totalPages)].map((_, i) => (
                  <PaginationItem key={i}>
                    {currentPage === i + 1 ? (
                      <span className="active">{i + 1}</span> // Render active page as a non-clickable element
                    ) : (
                      <PaginationLink
                        onClick={() => setCurrentPage(i + 1)}
                        isActive={currentPage === i + 1}
                      >
                        {i + 1}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}
                <PaginationItem>
                  {currentPage === totalPages ? (
                    <span className="disabled">Next</span>
                  ) : (
                    <PaginationNext
                      onClick={() =>
                        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                      }
                    />
                  )}
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      )}
    </div>
  );
}
