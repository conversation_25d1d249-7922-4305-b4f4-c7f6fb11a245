"use client";

import { useState, useEffect, useCallback } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageCarouselProps {
  images: string[];
  alt: string;
  compact?: boolean;
}

export const ImageCarousel = ({ images, alt, compact = false }: ImageCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isHovering, setIsHovering] = useState(false);

  // Fallback image when no images are available or there's an error
  const fallbackImage = "https://www.shutterstock.com/image-vector/vector-illustration-car-sold-icon-600nw-2131221743.jpg";
  
  // Check if we have valid images to display
  const hasValidImages = Array.isArray(images) && images.length > 0 && images[0] !== "NO_IMAGES";
  
  // Use the images array if valid, otherwise use the fallback
  const displayImages = hasValidImages ? images : [fallbackImage];

  const goToPrevious = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    if (isTransitioning || displayImages.length <= 1) return;
    
    setPreviousIndex(currentIndex);
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? displayImages.length - 1 : prevIndex - 1
    );
    
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  }, [currentIndex, displayImages.length, isTransitioning]);

  const goToNext = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    if (isTransitioning || displayImages.length <= 1) return;
    
    setPreviousIndex(currentIndex);
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => 
      prevIndex === displayImages.length - 1 ? 0 : prevIndex + 1
    );
    
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  }, [currentIndex, displayImages.length, isTransitioning]);

  const goToSlide = useCallback((index: number, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
      e.preventDefault();
    }
    
    if (isTransitioning || index === currentIndex) return;
    
    setPreviousIndex(currentIndex);
    setIsTransitioning(true);
    setCurrentIndex(index);
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  }, [currentIndex, isTransitioning]);

  // Auto-advance carousel every 5 seconds if there's more than one image and not hovering
  useEffect(() => {
    if (displayImages.length <= 1 || isHovering) return;
    
    const interval = setInterval(() => {
      if (!isTransitioning) {
        goToNext();
      }
    }, 5000);
    
    return () => clearInterval(interval);
  }, [currentIndex, displayImages.length, goToNext, isHovering, isTransitioning]);

  // Handle loading errors by showing fallback image
  const handleImageError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  // Pre-load images
  useEffect(() => {
    if (displayImages.length <= 1) return;
    
    const nextIndex = currentIndex === displayImages.length - 1 ? 0 : currentIndex + 1;
    const prevIndex = currentIndex === 0 ? displayImages.length - 1 : currentIndex - 1;
    
    const preloadImages = [nextIndex, prevIndex].map(index => {
      const img = new Image();
      img.src = displayImages[index];
      return img;
    });
    
    return () => {
      preloadImages.forEach(img => {
        img.onload = null;
        img.onerror = null;
      });
    };
  }, [currentIndex, displayImages]);

  return (
    <div 
      className={cn(
        "relative w-full overflow-hidden bg-gray-100 group max-w-full",
        compact ? "h-40" : "h-60"
      )}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Main image container */}
      <div className="relative w-full h-full">
        {/* Loading state */}
        {isLoading && !hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse">
            <span className="sr-only">Loading...</span>
          </div>
        )}

        {/* Current image with fade in/out transition */}
        {displayImages.map((img, index) => (
          <img
            key={index}
            src={hasError ? fallbackImage : img}
            alt={`${alt} - image ${index + 1}`}
            className={cn(
              "absolute inset-0 object-cover w-full h-full transition-opacity duration-500 ease-in-out",
              index === currentIndex ? "opacity-100 z-10" : "opacity-0 z-0"
            )}
            onError={handleImageError}
            onLoad={handleImageLoad}
            style={{
              transitionProperty: "opacity, transform",
              transitionDuration: "500ms",
              transitionTimingFunction: "ease",
              maxWidth: "100%"
            }}
          />
        ))}
      </div>
      
      {/* Navigation arrows - only show if there are multiple images */}
      {displayImages.length > 1 && !hasError && (
        <>
          <button 
            className={cn(
              "absolute left-1 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20",
              compact ? "h-6 w-6" : "h-8 w-8"
            )}
            onClick={goToPrevious}
            aria-label="Previous image"
            disabled={isTransitioning}
          >
            <ChevronLeft className={compact ? "h-4 w-4" : "h-5 w-5"} />
          </button>
          
          <button 
            className={cn(
              "absolute right-1 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-20",
              compact ? "h-6 w-6" : "h-8 w-8"
            )}
            onClick={goToNext}
            aria-label="Next image"
            disabled={isTransitioning}
          >
            <ChevronRight className={compact ? "h-4 w-4" : "h-5 w-5"} />
          </button>
        </>
      )}
      
      {/* Simplified indicators for compact mode */}
      {displayImages.length > 1 && !hasError && (
        <div className={cn(
          "absolute bottom-1 right-3 flex justify-end gap-1 px-2 z-20", 
          compact ? "bottom-1" : "bottom-3"
        )}>
          {compact ? (
            // Simplified dots for compact mode with aligned counter
            <div className="flex items-center gap-1">
              {displayImages.slice(0, Math.min(3, displayImages.length)).map((_, index) => (
                <div 
                  key={index}
                  className={cn(
                    "h-1 w-1 rounded-full",
                    currentIndex === index ? "bg-white" : "bg-white/40"
                  )}
                />
              ))}
              {displayImages.length > 3 && (
                <span className="text-xs text-white bg-black/50 rounded-full px-1.5 ml-0.5">
                  +{displayImages.length - 3}
                </span>
              )}
            </div>
          ) : (
            // Original indicators for normal mode with aligned counter
            <div className="flex items-center gap-1">
              {displayImages.slice(0, Math.min(5, displayImages.length)).map((img, index) => (
                <button
                  key={index}
                  onClick={(e) => goToSlide(index, e)}
                  className={cn(
                    "h-1.5 rounded-full transition-all duration-300",
                    currentIndex === index 
                      ? "bg-white w-6" 
                      : "bg-white/50 hover:bg-white/80 w-1.5"
                  )}
                  aria-label={`Go to image ${index + 1}`}
                />
              ))}
              {displayImages.length > 5 && (
                <span className="text-xs text-white bg-black/50 rounded-full px-1.5 ml-0.5">
                  +{displayImages.length - 5}
                </span>
              )}
            </div>
          )}
        </div>
      )}
      
      {/* Image counter - simplified for compact mode */}
      {displayImages.length > 1 && !hasError && !compact && (
        <div className="absolute top-3 right-3 bg-black/60 text-white text-xs px-2 py-1 rounded-full z-20">
          {currentIndex + 1} / {displayImages.length}
        </div>
      )}
    </div>
  );
};