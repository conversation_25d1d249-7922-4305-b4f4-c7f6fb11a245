"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Code2 } from 'lucide-react'

interface CustomModel {
  id: string
  name: string
  type: 'classification' | 'regression'
  pythonVersion: string
  createdAt: string
  version: string
  trainingMetrics: {
    accuracy: number
    f1Score: number
    precision: number
    recall: number
  }
  realPerformance: {
    accuracy: number
  }
}

interface CustomModelInfoDialogProps {
  model: CustomModel
}

export function CustomModelInfoDialog({ model }: CustomModelInfoDialogProps) {
  const pythonExample = `
import os
from rekover.auth import configure_auth
from rekover.models import CustomModel

# Set up authentication
configure_auth(api_key="your_api_key_here")

# Initialize the custom model
model = CustomModel("${model.name}")

# Make predictions
result = model.predict({
    "feature1": value1,
    "feature2": value2,
    # ... other features as defined in your input schema
})

print(result)
  `.trim()

  const restExample = `
curl -X POST https://api.rekover.ai/v1/custom-models/${model.id}/predict \\
-H "Authorization: Bearer your_api_key_here" \\
-H "Content-Type: application/json" \\
-d '{
  "feature1": value1,
  "feature2": value2,
  // ... other features as defined in your input schema
}'
  `.trim()

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Code2 className="mr-2 h-4 w-4" />
          API & Usage
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{model.name} - API & Usage Information</DialogTitle>
          <DialogDescription>
            Learn how to use this custom model via our API and Python package
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="python" className="w-full mt-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="python">Python Package</TabsTrigger>
            <TabsTrigger value="rest">REST API</TabsTrigger>
          </TabsList>
          <TabsContent value="python">
            <div className="space-y-4">
              <p>Install the Rekover package: <code>pip install rekover</code></p>
              <p>Use the following code to make predictions with your custom model:</p>
              <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                <code>{pythonExample}</code>
              </pre>
            </div>
          </TabsContent>
          <TabsContent value="rest">
            <div className="space-y-4">
              <p>Make a POST request to the following endpoint:</p>
              <p><code>https://api.rekover.ai/v1/custom-models/{model.id}/predict</code></p>
              <p>Example cURL request:</p>
              <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                <code>{restExample}</code>
              </pre>
            </div>
          </TabsContent>
        </Tabs>
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Model Details:</h4>
          <ul className="list-disc list-inside space-y-1">
            <li>Type: {model.type}</li>
            <li>Python Version: {model.pythonVersion}</li>
            <li>Version: {model.version}</li>
            <li>Created At: {model.createdAt}</li>
            <li>Training Accuracy: {(model.trainingMetrics.accuracy * 100).toFixed(2)}%</li>
            <li>Real Performance Accuracy: {(model.realPerformance.accuracy * 100).toFixed(2)}%</li>
          </ul>
        </div>
      </DialogContent>
    </Dialog>
  )
}

