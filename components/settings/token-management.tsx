"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { AlertCircle, Copy, Key, Plus, Shield, Trash2, User } from 'lucide-react'
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"

interface Token {
  id: string
  name: string
  token: string
  assignedTo: {
    type: "user" | "role"
    name: string
  }
  createdAt: string
  lastUsed: string | null
  status: "active" | "revoked"
}

// Mock data for available users and roles
const availableUsers = [
  { id: "1", name: "John Doe" },
  { id: "2", name: "Jane Smith" },
  { id: "3", name: "Bob Johnson" },
]

const availableRoles = [
  { id: "1", name: "Admin" },
  { id: "2", name: "Manager" },
  { id: "3", name: "Analyst" },
]

// Mock data for existing tokens
const initialTokens: Token[] = [
  {
    id: "1",
    name: "Production API Token",
    token: "rek_prod_12345678",
    assignedTo: { type: "role", name: "Admin" },
    createdAt: "2023-12-01",
    lastUsed: "2023-12-10",
    status: "active",
  },
  {
    id: "2",
    name: "Testing Token",
    token: "rek_test_87654321",
    assignedTo: { type: "user", name: "John Doe" },
    createdAt: "2023-12-05",
    lastUsed: null,
    status: "active",
  },
  {
    id: "3",
    name: "Deprecated Token",
    token: "rek_old_11223344",
    assignedTo: { type: "role", name: "Analyst" },
    createdAt: "2023-11-01",
    lastUsed: "2023-11-15",
    status: "revoked",
  },
]

export function TokenManagement() {
  const [tokens, setTokens] = useState<Token[]>(initialTokens)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isRevokeDialogOpen, setIsRevokeDialogOpen] = useState(false)
  const [tokenToRevoke, setTokenToRevoke] = useState<string | null>(null)
  const [newTokenData, setNewTokenData] = useState<{
    token: string
    name: string
  } | null>(null)
  
  const handleCreateToken = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const name = formData.get("name") as string
    const assignmentType = formData.get("assignmentType") as "user" | "role"
    const assignedTo = formData.get("assignedTo") as string
    
    // Generate a mock token
    const token = `rek_${Math.random().toString(36).substring(2, 15)}`
    
    const newToken: Token = {
      id: Math.random().toString(),
      name,
      token,
      assignedTo: {
        type: assignmentType,
        name: assignmentType === "user"
          ? availableUsers.find(u => u.id === assignedTo)?.name || ""
          : availableRoles.find(r => r.id === assignedTo)?.name || "",
      },
      createdAt: new Date().toISOString().split("T")[0],
      lastUsed: null,
      status: "active",
    }
    
    setTokens(prev => [...prev, newToken])
    setNewTokenData({ token, name })
  }

  const handleRevokeToken = async (tokenId: string) => {
    setTokens(prev => prev.map(token => 
      token.id === tokenId
        ? { ...token, status: "revoked" }
        : token
    ))
    
    toast({
      title: "Token revoked",
      description: "The API token has been revoked successfully.",
    })
    
    setIsRevokeDialogOpen(false)
    setTokenToRevoke(null)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The token has been copied to your clipboard.",
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>API Tokens</CardTitle>
            <CardDescription>
              Manage API tokens for secure access to the platform
            </CardDescription>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Token
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New API Token</DialogTitle>
                <DialogDescription>
                  Generate a new API token and assign it to a user or role.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateToken}>
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Token Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter a name for this token"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Assignment Type</Label>
                    <Select name="assignmentType" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">User</SelectItem>
                        <SelectItem value="role">Role</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Assign To</Label>
                    <Select name="assignedTo" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select assignment" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableUsers.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            <span className="flex items-center">
                              <User className="mr-2 h-4 w-4" />
                              {user.name}
                            </span>
                          </SelectItem>
                        ))}
                        {availableRoles.map((role) => (
                          <SelectItem key={role.id} value={role.id}>
                            <span className="flex items-center">
                              <Shield className="mr-2 h-4 w-4" />
                              {role.name}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">
                    <Key className="mr-2 h-4 w-4" />
                    Generate Token
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* Show new token alert */}
        {newTokenData && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>New Token Generated</AlertTitle>
            <AlertDescription>
              <div className="mt-2 space-y-2">
                <p><strong>Tool Name:</strong> {newTokenData.name}</p>
                <p><strong>Token:</strong> {newTokenData.token}</p>
                <p className="text-sm text-muted-foreground">
                  Make sure to copy your token now. You won't be able to see it again!
                </p>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => copyToClipboard(newTokenData.token)}
                >
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Token
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Used</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tokens.map((token) => (
                <TableRow key={token.id}>
                  <TableCell>
                    <div className="font-medium">{token.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {token.token.slice(0, 12)}...
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {token.assignedTo.type === "user" ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Shield className="h-4 w-4" />
                      )}
                      {token.assignedTo.name}
                    </div>
                  </TableCell>
                  <TableCell>{token.createdAt}</TableCell>
                  <TableCell>
                    {token.lastUsed || "Never"}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={token.status === "active" ? "default" : "secondary"}
                    >
                      {token.status.charAt(0).toUpperCase() + token.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {token.status === "active" && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setTokenToRevoke(token.id)
                          setIsRevokeDialogOpen(true)
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Revoke token</span>
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Revoke Token Dialog */}
        <AlertDialog open={isRevokeDialogOpen} onOpenChange={setIsRevokeDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. Once revoked, this token will no longer
                have access to the API.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => tokenToRevoke && handleRevokeToken(tokenToRevoke)}
                className="bg-red-600 hover:bg-red-700"
              >
                Revoke Token
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}

