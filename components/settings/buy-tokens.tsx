'use client';
import React, { useState } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON>ontent, Card<PERSON>ooter, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Zap, Rocket, Gem } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import { useSession } from 'next-auth/react'

// Load Stripe.js asynchronously
if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  console.error('Missing Stripe publishable key');
}
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

interface TokenPackage {
  id: number;
  name: string;
  tokens: number;
  price: number;
  icon: React.ReactNode;
}

const tokenPackages: TokenPackage[] = [
  {
    id: 1,
    name: 'Basic',
    tokens: 100,
    price: 10,
    icon: <Zap className="h-8 w-8 text-blue-500" />,
  },
  {
    id: 2,
    name: 'Standard',
    tokens: 200,
    price: 20,
    icon: <Rocket className="h-8 w-8 text-purple-500" />,
  },
  {
    id: 3,
    name: 'Premium',
    tokens: 300,
    price: 30,
    icon: <Gem className="h-8 w-8 text-yellow-500" />,
  },
  {
    id: 4,
    name: 'Mini',
    tokens: 10,
    price: 1,
    icon: <Zap className="h-8 w-8 text-blue-500" />,
  },
];

export default function BuyTokens() {
  const [loading, setLoading] = useState<number | null>(null);
  // const router = useRouter();
  const { data: session } = useSession();

  const userID = session?.user?.id;

  const handleCheckout = async (price: number, tokens: number) => {
    console.log(`Initiating checkout for price: $${price}`);
    setLoading(price);

    try {
      console.log('Creating Checkout Session');
      // Call your backend to create the Checkout Session
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/stripe-pay/checkout_sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ price, userID, tokens }),
      });

      const session = await response.json();
      console.log('Checkout Session created', session);

      if (session.error) {
        throw new Error(session.error);
      }

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      const { error } = await stripe!.redirectToCheckout({
        sessionId: session.id,
      });

      if (error) {
        throw new Error(error.message);
      }
    } catch (err) {
      console.error('Error during checkout', err);
    } finally {
      console.log('Checkout process completed');
      setLoading(null);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <Card className="p-6">
        <div className="flex flex-col items-center justify-between space-y-4">
          <div className="text-center">
            <CardTitle className="text-2xl font-bold">API Tokens</CardTitle>
            <CardDescription className="text-gray-600">
              Purchase API tokens for secure access to the platform
            </CardDescription>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
            {tokenPackages.map((pkg) => (
              <Card key={pkg.id} className="flex flex-col items-center text-center">
                <CardHeader className="flex flex-col items-center space-y-2">
                  {pkg.icon}
                  <h2 className="text-xl font-bold">{pkg.tokens} Tokens</h2>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Price: ${pkg.price}</p>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={() => handleCheckout(pkg.price, pkg.tokens)}
                    variant="default"
                    className="w-full"
                    disabled={loading !== null && loading !== pkg.price}
                  >
                    {loading === pkg.price ? 'Processing...' : 'Buy Now'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
}