"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { 
  ChevronRight, TrendingUp, AlertTriangle, BarChart4, 
  GaugeCircle, Calendar, CheckCircle2, Info, ClipboardCheck
} from "lucide-react"
import ReactMarkdown from "react-markdown"
import { useState, useEffect, useMemo } from "react"

interface AiNotesProps {
  notes: string
  structured?: boolean
}

interface ChecklistItem {
  id: string;
  label: string;
  description: string;
}

export function AiNotes({ notes, structured = false }: AiNotesProps) {
  const [activeSection, setActiveSection] = useState("overview")
  
  // Extract and structure the content
  const structuredContent = useMemo(() => {
    if (!notes) return null;
    
    // Simple parsing for key sections using regex patterns
    const sections = {
      overview: extractSection(notes, "Market Overview", "Price Drivers"),
      priceDrivers: extractSection(notes, "Price Drivers", "Risk Assessment"),
      risks: extractSection(notes, "Risk Assessment", "Investment Potential"),
      investment: extractSection(notes, "Investment Potential", "Smart Buying"),
    };
    
    return sections;
  }, [notes]);
  
  // Helper function to extract a section from the notes
  function extractSection(text: string, startMarker: string, endMarker: string): string {
    if (!text) return "";
    
    const startIndex = text.indexOf(startMarker);
    if (startIndex === -1) return "";
    
    let endIndex = text.length;
    if (endMarker && text.indexOf(endMarker) > startIndex) {
      endIndex = text.indexOf(endMarker);
    }
    
    return text.substring(startIndex, endIndex).trim();
  }
  
  // Extract numerical data from text for visualization
  const highlightData = useMemo(() => {
    if (!notes) return {};
    
    // Extract price range using regex
    const priceRangeMatch = notes.match(/range from €([0-9,]+) to €([0-9,]+)/);
    const avgMileageMatch = notes.match(/average mileage of ([0-9,]+) km/);
    
    return {
      minPrice: priceRangeMatch ? priceRangeMatch[1] : null,
      maxPrice: priceRangeMatch ? priceRangeMatch[2] : null,
      avgMileage: avgMileageMatch ? avgMileageMatch[1] : null
    };
  }, [notes]);
  
  // Render a styled section with icon and title
  const renderSection = (title: string, content: string, icon: React.ReactNode) => {
    // Remove any duplicated section titles from the content
    let cleanedContent = content;
    if (title && content.includes(title)) {
      const titleIndex = content.indexOf(title);
      cleanedContent = content.substring(titleIndex + title.length).trim();
    }
    
    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2 font-medium text-lg">
          {icon}
          <h3>{title}</h3>
        </div>
        <div className="pl-6 text-sm leading-relaxed space-y-2">
          {renderFormattedContent(cleanedContent)}
        </div>
      </div>
    );
  };
  
  // Format content with bold keywords and statistics
  const renderFormattedContent = (content: string) => {
    if (!content) return null;
    
    // Clean up the content - remove markdown artifacts
    const cleanContent = content
      // Remove heading markers
      .replace(/#{1,6}\s+/g, '')
      // Remove duplicated section titles
      .replace(/Market Overview\s+Market Overview/g, 'Market Overview')
      // Clean up any bullet points that use markdown syntax
      .replace(/^\s*[-*]\s+/gm, '• ')
      // Remove standalone section numbers (e.g., "2." on a line by itself)
      .replace(/^\s*\d+\.\s*$/gm, '')
      // Remove section numbers at the end of a section
      .replace(/\n\s*\d+\.\s*$/, '')
      // Remove "###" markdown formatting
      .replace(/###\s+\d+\./g, '')
      // Remove any standalone "###" characters
      .replace(/\s*#{1,6}\s*$/gm, '')
      // Remove any line that's just a number followed by a period
      .replace(/^\s*\d+\.\s*\n/gm, '');
    
    // Split by line breaks or bullet points for better paragraphing
    const paragraphs = cleanContent.split(/\n(?=[-•*]|\w)/).filter(p => {
      const trimmed = p.trim();
      // Filter out any standalone section numbers
      return trimmed && !/^\d+\.$/.test(trimmed);
    });
    
    return paragraphs.map((paragraph, idx) => {
      // Skip any paragraph that's just a number (likely a section number)
      if (/^\s*\d+\s*$/.test(paragraph)) return null;
      
      // Remove markdown bold/italic formatting
      let formattedText = paragraph
        // Replace markdown bold with actual HTML bold
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // Replace markdown italics
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // Highlight price values
        .replace(/(€[0-9,]+)/g, '<span class="font-medium text-primary">$1</span>')
        // Highlight mileage values
        .replace(/([0-9,]+)\s?km/g, '<span class="font-medium text-primary">$1 km</span>');
      
      // Handle lines that start with emphasis indicators for key points
      if (formattedText.startsWith('**') && formattedText.includes('**:')) {
        formattedText = formattedText.replace(/\*\*(.*?)\*\*:/, '<strong>$1:</strong>');
        const rest = formattedText.split('<strong>').slice(1);
        if (rest.length > 0) {
          const [boldPart, ...restPart] = rest[0].split('</strong>');
          return (
            <div key={idx} className="flex gap-2 items-start">
              <Badge variant="outline" className="h-5 mt-0.5 shrink-0 whitespace-nowrap">
                {boldPart.replace(':', '')}
              </Badge>
              <p dangerouslySetInnerHTML={{ __html: restPart.join('') }} />
            </div>
          );
        }
      }
      
      // Check if this is a key point (starts with keyword and colon)
      else if (/^[A-Za-z\s]+:/.test(formattedText)) {
        const [key, ...rest] = formattedText.split(':');
        return (
          <div key={idx} className="flex gap-2 items-start">
            <Badge variant="outline" className="h-5 mt-0.5 shrink-0 whitespace-nowrap">
              {key.trim()}
            </Badge>
            <p dangerouslySetInnerHTML={{ __html: rest.join(':').trim() }} />
          </div>
        );
      }
      
      // Handle bullet point lines
      else if (formattedText.startsWith('• ') || formattedText.startsWith('- ')) {
        return (
          <div key={idx} className="flex gap-2 items-start">
            <span className="text-primary">•</span>
            <p dangerouslySetInnerHTML={{ 
              __html: formattedText.replace(/^[•-]\s+/, '') 
            }} />
          </div>
        );
      }
      
      // Skip any line that's just a section number
      else if (/^\s*\d+\.\s*$/.test(formattedText)) {
        return null;
      }
      
      return <p key={idx} dangerouslySetInnerHTML={{ __html: formattedText }} />;
    }).filter(Boolean); // Filter out any null elements
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-xl">
            <BarChart4 className="h-5 w-5" />
            AI Market Analysis
          </CardTitle>
        </CardHeader>
        {structured && structuredContent ? (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {highlightData.minPrice && highlightData.maxPrice && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="text-sm text-muted-foreground mb-1">Price Range</div>
                  <div className="font-medium">
                    €{highlightData.minPrice} - €{highlightData.maxPrice}
                  </div>
                </div>
              )}
              
              {highlightData.avgMileage && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="text-sm text-muted-foreground mb-1">Average Mileage</div>
                  <div className="font-medium">{highlightData.avgMileage} km</div>
                </div>
              )}
              
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground mb-1">Analysis Date</div>
                <div className="font-medium">
                  {new Date().toLocaleDateString()}
                </div>
              </div>
            </div>
            
            {/* Tabbed Analysis Sections */}
            <Tabs 
              defaultValue="overview" 
              value={activeSection} 
              onValueChange={setActiveSection}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4 h-auto">
                <TabsTrigger value="overview" className="text-xs py-2">Overview</TabsTrigger>
                <TabsTrigger value="priceDrivers" className="text-xs py-2">Price Drivers</TabsTrigger>
                <TabsTrigger value="risks" className="text-xs py-2">Risk Assessment</TabsTrigger>
                <TabsTrigger value="investment" className="text-xs py-2">Investment Outlook</TabsTrigger>
              </TabsList>
              
              <div className="mt-4 border rounded-md p-3">
                <TabsContent value="overview" className="mt-0">
                  {renderSection("Market Overview", structuredContent.overview, <TrendingUp className="h-5 w-5 text-blue-500" />)}
                </TabsContent>
                
                <TabsContent value="priceDrivers" className="mt-0">
                  {renderSection("Key Price Factors", structuredContent.priceDrivers, <GaugeCircle className="h-5 w-5 text-green-500" />)}
                </TabsContent>
                
                <TabsContent value="risks" className="mt-0">
                  {renderSection("Risk Factors", structuredContent.risks, <AlertTriangle className="h-5 w-5 text-amber-500" />)}
                </TabsContent>
                
                <TabsContent value="investment" className="mt-0">
                  {renderSection("Investment Timeline", structuredContent.investment, <Calendar className="h-5 w-5 text-purple-500" />)}
                </TabsContent>
              </div>
            </Tabs>
            
            <Alert className="bg-muted/30">
              <div className="flex items-center gap-1.5">
                <Info className="h-4 w-4" />
                <AlertDescription className="text-xs text-muted-foreground">
                  This analysis is based on market data and algorithmic evaluation. Individual vehicle conditions may vary.
                </AlertDescription>
              </div>
            </Alert>
          </div>
        ) : (
          // Fallback to simple well-formatted content when not structured
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <div className="space-y-4">
              {renderFormattedContent(notes)}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
