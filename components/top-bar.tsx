"use client"

import { useState, useEffect } from 'react'
import { Co<PERSON>, Search, Settings, User } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import Image from 'next/image'
import { ThemeToggle } from '@/components/theme-toggle'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import axios from 'axios'


export function TopBar() {
  const [searchQuery, setSearchQuery] = useState('')
  const { data: session, update: updateSession } = useSession()
  const [userImage, setUserImage] = useState(session?.user?.image || "/avatars/01.png")
  const [tokens, setTokens] = useState(0)
  const user = session?.user

  useEffect(() => {
    const fetchUserImage = async () => {
      if (!user) return;
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${user.id}`);
        if (!response.ok) throw new Error('Failed to fetch user image');
        const data = await response.json();
        setUserImage(data.profile_pic || "/avatars/01.png");
      } catch (error) {
        console.error('Error fetching user image:', error);
      }
    };
  
    // Fetch user image on component mount
    fetchUserImage();
  
    // Handle profile picture updates
    const handleProfilePicUpdated = () => {
      fetchUserImage();
    };
  
    window.addEventListener('profile-pic-updated', handleProfilePicUpdated);
    return () => {
      window.removeEventListener('profile-pic-updated', handleProfilePicUpdated);
    };
  }, [user]);
  
  const fetchUserData = async (id: string) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${id}`)
      setTokens(response.data.user_token)
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchUserData(user.id)
    }
  }, [user])

  return (
    <div className="flex items-center justify-between px-6 py-2 bg-white dark:bg-black dark:border-gray-700 border-b border-gray-200">
      <div className="flex items-center">
        <h2 className="text-lg font-semibold mr-4">Rekover</h2>
        <div className="relative w-64">
          <Input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        </div>
      </div>
        

      <div className="flex items-center space-x-4">
      <div className="flex items-end">
          <Coins className="text-2xl" />
          <span className="ml-2">{tokens}</span>
        </div>
        <ThemeToggle />
        <span className="text-sm font-medium">{user?.name}</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src={userImage} alt={user?.name || ""} />
                <AvatarFallback>
                  <Image
                    src={userImage}
                    alt={user?.name || ""}
                    width={32}
                    height={32}
                  />
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{user?.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              <Link href="/dashboard/user-settings">Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <Link href="/dashboard/user-settings">Settings</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
