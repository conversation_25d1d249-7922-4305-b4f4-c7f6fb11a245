"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  FileText,
  ExternalLink,
  Calendar,
  Gavel,
  Tag,
  BarChart,
  ChevronRight
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { useState, useMemo } from "react"
import { ClaimDetails } from "./claim-details"
import { FilterPanel, FilterOptions } from "./filter-panel"

interface SimilarClaimsListProps {
  claims: any[]
}

export function SimilarClaimsList({ claims }: SimilarClaimsListProps) {
  const [selectedClaim, setSelectedClaim] = useState<any | null>(null)
  const [filters, setFilters] = useState<FilterOptions>({
    courts: [],
    decisionTypes: [],
    dateRange: {
      from: undefined,
      to: undefined
    },
    keywords: []
  })

  // Format date if it exists
  const formatDateString = (dateString: string) => {
    if (!dateString) return "N/A"
    try {
      return new Date(dateString).toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    } catch (e) {
      return dateString
    }
  }

  // Extract available filter options from claims
  const availableCourts = useMemo(() => {
    const courts = new Set<string>()
    claims.forEach(claim => {
      if (claim.court_name) courts.add(claim.court_name)
    })
    return Array.from(courts)
  }, [claims])

  const availableDecisionTypes = useMemo(() => {
    const types = new Set<string>()
    claims.forEach(claim => {
      if (claim.decision_type) types.add(claim.decision_type)
    })
    return Array.from(types)
  }, [claims])

  const availableKeywords = useMemo(() => {
    const keywords = new Set<string>()
    claims.forEach(claim => {
      claim.keywords?.forEach((keyword: string) => {
        keywords.add(keyword)
      })
    })
    return Array.from(keywords)
  }, [claims])

  // Apply filters to claims
  const filteredClaims = useMemo(() => {
    return claims.filter(claim => {
      // Filter by court
      if (filters.courts.length > 0 && !filters.courts.includes(claim.court_name)) {
        return false
      }

      // Filter by decision type
      if (filters.decisionTypes.length > 0 && !filters.decisionTypes.includes(claim.decision_type)) {
        return false
      }

      // Filter by date range
      if (filters.dateRange.from || filters.dateRange.to) {
        try {
          const claimDate = new Date(claim.decision_date)

          if (filters.dateRange.from && claimDate < filters.dateRange.from) {
            return false
          }

          if (filters.dateRange.to && claimDate > filters.dateRange.to) {
            return false
          }
        } catch (e) {
          console.error('Error parsing date:', e)
          // If date parsing fails, don't filter out the claim
        }
      }

      // Filter by keywords
      if (filters.keywords.length > 0) {
        const hasKeyword = filters.keywords.some(keyword =>
          claim.keywords?.includes(keyword)
        )
        if (!hasKeyword) {
          return false
        }
      }

      return true
    })
  }, [claims, filters])

  const handleApplyFilters = (newFilters: FilterOptions) => {
    setFilters(newFilters)
  }

  return (
    <div className="space-y-6">
      <FilterPanel
        onApplyFilters={handleApplyFilters}
        availableCourts={availableCourts}
        availableDecisionTypes={availableDecisionTypes}
        availableKeywords={availableKeywords}
      />
      <Card>
        <CardHeader>
          <CardTitle>Similar Claims</CardTitle>
          <CardDescription>
            Found {filteredClaims.length} of {claims.length} similar claims based on your description
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredClaims.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No claims match the current filters</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setFilters({
                    courts: [],
                    decisionTypes: [],
                    dateRange: { from: undefined, to: undefined },
                    keywords: []
                  })}
                >
                  Clear Filters
                </Button>
              </div>
            ) : (
              filteredClaims.map((claim, index) => (
              <Card key={index} className="overflow-hidden hover:border-primary/50 transition-colors">
                <CardHeader className="bg-muted/50 py-3">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <div>
                      <CardTitle className="text-base">{claim.process_number}</CardTitle>
                      <CardDescription>{claim.court_name} • {formatDateString(claim.decision_date)}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={claim.decision_type === "PROCEDENTE" ? "default" :
                                      claim.decision_type === "IMPROCEDENTE" ? "destructive" :
                                      "outline"}>
                        {claim.decision_type}
                      </Badge>
                      <div className="flex items-center gap-1 bg-primary/10 text-primary rounded-full px-2 py-1 text-xs">
                        <BarChart className="h-3 w-3" />
                        <span>{(claim.similarity_score * 100).toFixed(0)}%</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="py-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 flex-wrap">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      {claim.keywords?.slice(0, 3).map((keyword: string, i: number) => (
                        <Badge key={i} variant="secondary">{keyword}</Badge>
                      ))}
                      {claim.keywords?.length > 3 && (
                        <Badge variant="outline">+{claim.keywords.length - 3}</Badge>
                      )}
                    </div>

                    <p className="text-sm text-muted-foreground line-clamp-2">{claim.summary}</p>

                    <div className="pt-2 flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Progress value={claim.similarity_score * 100} className="w-24 h-2" />
                        <span className="text-xs text-muted-foreground">Similarity</span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => window.open(claim.url, '_blank')}>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="default" size="sm" onClick={() => setSelectedClaim(claim)}>
                          Details
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )))
            }
          </div>
        </CardContent>
      </Card>

      {selectedClaim && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">Claim Details</h2>
            <Button variant="ghost" onClick={() => setSelectedClaim(null)}>Close</Button>
          </div>
          <ClaimDetails caseData={selectedClaim} />
        </div>
      )}
    </div>
  )
}
