"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Search, FileText, AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { SimilarClaimsList } from "./similar-claims-list"
import { SearchHistory } from "./search-history"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"

// Define the history item interface
interface HistoryItem {
  id: string
  text: string
  timestamp: number
  type: 'url' | 'description'
}

// Sample data for demonstration
const sampleSimilarClaims = [
  {
    id: "68051dfb752b63edc09d7528",
    process_number: "21154/19.7T8LSB.L1-2",
    court_name: "Tribunal da Relacao de Lisboa",
    decision_date: "2024-11-07",
    decision_type: "IMPROCEDENTE",
    similarity_score: 0.92,
    keywords: ["RESPONSABILIDADE CIVIL", "PERDA DE CHANCE", "ADVOGADO", "SEGURO", "INDEMNIZACAO"],
    summary: "Insurance case where the court ruled against the insurance company, stating it was responsible for compensating the plaintiff for damages resulting from the negligence of the defendant lawyer.",
    url: "https://www.dgsi.pt/jtrl.nsf/33182fc732316039802565fa00497eec/672fb9b269be9b2980258b63004fc517?OpenDocument"
  },
  {
    id: "68051dfb752b63edc09d7529",
    process_number: "1234/18.5T8LSB.L1-6",
    court_name: "Tribunal da Relacao de Lisboa",
    decision_date: "2023-05-15",
    decision_type: "PROCEDENTE",
    similarity_score: 0.85,
    keywords: ["RESPONSABILIDADE CIVIL", "ADVOGADO", "SEGURO", "INDEMNIZACAO"],
    summary: "Case involving lawyer's professional liability where the court found the lawyer responsible for failing to file an appeal within the legal deadline.",
    url: "https://www.example.com/case/1234"
  },
  {
    id: "68051dfb752b63edc09d7530",
    process_number: "5678/20.2T8PRT.P1",
    court_name: "Tribunal da Relacao do Porto",
    decision_date: "2022-09-22",
    decision_type: "PARCIALMENTE PROCEDENTE",
    similarity_score: 0.78,
    keywords: ["RESPONSABILIDADE CIVIL", "SEGURO", "INDEMNIZACAO", "CONTRATO"],
    summary: "Insurance dispute where the court partially upheld the claim, finding that some policy exclusions were valid while others were not enforceable against the insured party.",
    url: "https://www.example.com/case/5678"
  },
  {
    id: "68051dfb752b63edc09d7531",
    process_number: "9012/19.3T8LSB.L1-7",
    court_name: "Tribunal da Relacao de Lisboa",
    decision_date: "2021-11-30",
    decision_type: "IMPROCEDENTE",
    similarity_score: 0.72,
    keywords: ["RESPONSABILIDADE CIVIL", "SEGURO", "CLAUSULA CLAIMS MADE"],
    summary: "Case where the court upheld the validity of a 'claims made' clause in a professional liability insurance policy.",
    url: "https://www.example.com/case/9012"
  },
  {
    id: "68051dfb752b63edc09d7532",
    process_number: "3456/17.8T8FNC.L1",
    court_name: "Tribunal da Relacao de Lisboa",
    decision_date: "2020-06-18",
    decision_type: "PROCEDENTE",
    similarity_score: 0.68,
    keywords: ["RESPONSABILIDADE CIVIL", "PERDA DE CHANCE", "INDEMNIZACAO"],
    summary: "Case where the court recognized the concept of 'loss of chance' as a compensable damage in a professional liability context.",
    url: "https://www.example.com/case/3456"
  }
]

export function SimilarClaimsSearch() {
  const [description, setDescription] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [similarClaims, setSimilarClaims] = useState<any[] | null>(null)
  const [numResults, setNumResults] = useState<number>(5)
  const [minSimilarity, setMinSimilarity] = useState<number>(0.6)

  // Function to add a search to history
  const addToSearchHistory = (searchText: string) => {
    // Get existing history from localStorage
    const storedHistory = localStorage.getItem('complexClaimsSearchHistory')
    let history: HistoryItem[] = []

    if (storedHistory) {
      try {
        history = JSON.parse(storedHistory)
      } catch (e) {
        console.error('Failed to parse search history:', e)
      }
    }

    // Create new history item
    const newItem: HistoryItem = {
      id: Date.now().toString(),
      text: searchText,
      timestamp: Date.now(),
      type: 'description'
    }

    // Add to history, avoiding duplicates
    const existingItemIndex = history.findIndex((item: HistoryItem) =>
      item.type === 'description' && item.text === searchText
    )

    if (existingItemIndex !== -1) {
      // Remove the existing item so we can add it to the top
      history.splice(existingItemIndex, 1)
    }

    // Add new item at the beginning
    history.unshift(newItem)

    // Limit history to 20 items
    if (history.length > 20) {
      history = history.slice(0, 20)
    }

    // Save back to localStorage
    localStorage.setItem('complexClaimsSearchHistory', JSON.stringify(history))
  }

  const handleSearch = async () => {
    if (!description || description.length < 20) {
      setError("Please enter a more detailed claim description (at least 20 characters)")
      return
    }

    setIsLoading(true)
    setError(null)
    setSimilarClaims(null)

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay and return sample data
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Filter sample data based on the number of results and minimum similarity
      const filteredClaims = sampleSimilarClaims
        .filter(claim => claim.similarity_score >= minSimilarity)
        .slice(0, numResults)

      if (filteredClaims.length === 0) {
        setError("No similar claims found with the current criteria")
      } else {
        setSimilarClaims(filteredClaims)
        // Add to search history
        addToSearchHistory(description)
      }
    } catch (err) {
      setError("An error occurred while searching for similar claims")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle selecting an item from history
  const handleSelectHistoryItem = (item: string) => {
    setDescription(item)
    handleSearch()
  }

  return (
    <div className="space-y-6">
      <SearchHistory type="description" onSelectItem={handleSelectHistoryItem} />
      <Card>
        <CardHeader>
          <CardTitle>Find Similar Claims</CardTitle>
          <CardDescription>
            Enter a claim description to find similar historical cases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              placeholder="Enter a detailed description of the claim..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-[150px]"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="num-results">Number of Results</Label>
                  <span className="text-sm text-muted-foreground">{numResults}</span>
                </div>
                <Slider
                  id="num-results"
                  min={1}
                  max={10}
                  step={1}
                  value={[numResults]}
                  onValueChange={(value) => setNumResults(value[0])}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="min-similarity">Minimum Similarity</Label>
                  <span className="text-sm text-muted-foreground">{(minSimilarity * 100).toFixed(0)}%</span>
                </div>
                <Slider
                  id="min-similarity"
                  min={0.5}
                  max={0.95}
                  step={0.05}
                  value={[minSimilarity]}
                  onValueChange={(value) => setMinSimilarity(value[0])}
                />
              </div>
            </div>

            <Button onClick={handleSearch} disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Find Similar Claims
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {similarClaims && <SimilarClaimsList claims={similarClaims} />}
    </div>
  )
}
