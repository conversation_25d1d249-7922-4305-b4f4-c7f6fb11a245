"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Search, ExternalLink, FileText, User, Users, Calendar, Gavel, Tag } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ClaimDetails } from "./claim-details"
import { SearchHistory } from "./search-history"

// Define the history item interface
interface HistoryItem {
  id: string
  text: string
  timestamp: number
  type: 'url' | 'description'
}

// Sample data for demonstration
const sampleCaseData = {
  '_id': 'ObjectId("68051dfb752b63edc09d7528")',
  'url': 'https://www.dgsi.pt/jtrl.nsf/33182fc732316039802565fa00497eec/672fb9b269be9b2980258b63004fc517?OpenDocument',
  'acordaos_trl': 'Acordao do Tribunal da Relacao de Lisboa',
  'processo': ', 21154/19.7T8LSB.L1-2',
  '21154/19.7t8lsb.l1-2': '',
  'relator': 'CARLOS CASTELO BRANCO',
  'descritores': ['RESPONSABILIDADE CIVIL',
   ' PERDA DE CHANCE',
   ' ADVOGADO',
   ' SEGURO',
   ' INDEMNIZACAO'],
  '': '',
  'no_do_documento': 'RL',
  'data_do_acordao': '07/11/2024',
  'votacao': 'UNANIMIDADE',
  'texto_integral': 'S',
  'texto_parcial': 'N',
  'meio_processual': 'APELACAO',
  'decisao': 'IMPROCEDENTE',
  'sumario': 'I) Atento o disposto no artigo 101.s, n.s 4, do Regime Juridico do Contrato de Seguro, e inoponivel ao autor (lesado/beneficiario de contrato de seguro), alheio r relacao contratual titulada pela apolice de seguro de responsabilidade civil profissional, a invocacao pela seguradora de que ocorreu falta de oportuna comunicacao ou participacao dos factos geradores de uma reclamacao por responsabilidade civil., II) O dano da perda de chance processual, fundamento da obrigacao de indemnizar, tem de ser consistente e serio, cabendo ao lesado o onus da prova de tal consistencia e seriedade., III) Verificada a instauracao de uma acao para efetivacao de responsabilidade civil pelo reu - advogado - em representacao do autor -lesado em acidente de viacao - proposta contra o Instituto de Seguros de Portugal/Fundo de Garantia Automovel, em razao de se desconhecer o condutor interveniente no acidente e tendo sido julgada improcedente tal acao, em virtude de nao ter o autor produzido a correspondente prova, por nao ter sido satisfeita a 2.S prestacao da taxa de justica - que o autor nao pagou, sem que o reu o tenha avisado para proceder ao respetivo pagamento - e por o advogado reu nao ter comparecido em audiencia de discussao e julgamento, vindo a inviabilizar, com tal conduta, a producao da prova arrolada pelo autor, determinou o reu que o autor perdesse a oportunidade de produzir a correspondente prova e de obter vencimento da referida causa que, com elevada probabilidade e de forma consistente e seria, teria lugar., (Sumario elaborado pelo relator nos termos do disposto no artigo 663s, ns 7, do CPC).',
  'court_name': 'Tribunal da Relacao de Lisboa',
  'judge': 'CARLOS CASTELO BRANCO',
  'decision_date': '2024-11-07T00:00:00Z',
  'decision_type': 'IMPROCEDENTE',
  'unanimous_decision': true,
  'procedure_type': 'APELACAO',
  'document_number': 'RL',
  'process_number': '21154/19.7T8LSB.L1-2',
  'parties': [
    {'name': 'A', 'role': 'plaintiff', 'party_type': 'person'},
    {'name': 'B', 'role': 'defendant', 'party_type': 'lawyer'},
    {'name': 'MAPFRE - SEGUROS GERAIS, S.A.',
     'role': 'third_party',
     'party_type': 'insurance_company'}
  ],
  'insurance_company': 'MAPFRE - SEGUROS GERAIS, S.A.',
  'insurance_lob': 'liability',
  'contract_number': '(...)58',
  'contract_date': '2014-01-01T00:00:00Z',
  'coverage_period': ['2014-01-01T00:00:00Z', '2015-01-01T00:00:00Z'],
  'incident_description': "Accident involving the plaintiff and an unidentified vehicle, for which the plaintiff sought compensation through legal action represented by the defendant lawyer, which was deemed unsuccessful due to the lawyer's negligence.",
  'incident_date': '2011-06-17T00:00:00Z',
  'incident_location': 'Estrada Municipal 1205 no sentido Santa Eulalia - Monfirre, concelho de Mafra.',
  'dispute_reason': "The insurance company, MAPFRE, contests its responsibility to cover the damages, arguing that the facts were known to the insured (the lawyer) before the insurance policy came into effect, and also invoking a 'claims made' clause.",
  'claimed_amounts': [{'item': 'Indemnizacao pelos danos sofridos',
     'amount': 200000.0}],
  'total_claimed': 200000.0,
  'compensations_awarded': 49440.43,
  'eli5_summary': 'In an insurance case, the court ruled against the insurance company MAPFRE, stating that it was responsible for compensating the plaintiff for damages resulting from the negligence of the defendant lawyer. The court determined that the insurance company could not use certain clauses in the policy to avoid compensating the injured third party.',
  'fraud': false,
  'keywords': ['RESPONSABILIDADE CIVIL',
    'PERDA DE CHANCE',
    'ADVOGADO',
    'SEGURO',
    'INDEMNIZACAO'],
}

export function HistoricCaseSearch() {
  const [url, setUrl] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [caseData, setCaseData] = useState<any>(null)

  // Function to add a search to history
  const addToSearchHistory = (searchText: string) => {
    // Get existing history from localStorage
    const storedHistory = localStorage.getItem('complexClaimsSearchHistory')
    let history: HistoryItem[] = []

    if (storedHistory) {
      try {
        history = JSON.parse(storedHistory)
      } catch (e) {
        console.error('Failed to parse search history:', e)
      }
    }

    // Create new history item
    const newItem: HistoryItem = {
      id: Date.now().toString(),
      text: searchText,
      timestamp: Date.now(),
      type: 'url'
    }

    // Add to history, avoiding duplicates
    const existingItemIndex = history.findIndex((item: HistoryItem) =>
      item.type === 'url' && item.text === searchText
    )

    if (existingItemIndex !== -1) {
      // Remove the existing item so we can add it to the top
      history.splice(existingItemIndex, 1)
    }

    // Add new item at the beginning
    history.unshift(newItem)

    // Limit history to 20 items
    if (history.length > 20) {
      history = history.slice(0, 20)
    }

    // Save back to localStorage
    localStorage.setItem('complexClaimsSearchHistory', JSON.stringify(history))
  }

  const handleSearch = async () => {
    if (!url) {
      setError("Please enter a URL to search")
      return
    }

    setIsLoading(true)
    setError(null)
    setCaseData(null)

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay and return sample data
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Check if the URL matches our sample data
      if (url.includes('dgsi.pt') || url === 'sample') {
        setCaseData(sampleCaseData)
        // Add to search history
        addToSearchHistory(url)
      } else {
        setError("No case found for the provided URL")
      }
    } catch (err) {
      setError("An error occurred while searching for the case")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle selecting an item from history
  const handleSelectHistoryItem = (item: string) => {
    setUrl(item)
    handleSearch()
  }

  return (
    <div className="space-y-6">
      <SearchHistory type="url" onSelectItem={handleSelectHistoryItem} />
      <Card>
        <CardHeader>
          <CardTitle>Search for Historic Case</CardTitle>
          <CardDescription>
            Enter the URL of a case to retrieve detailed information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                placeholder="Enter case URL..."
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="w-full"
              />
            </div>
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </>
              )}
            </Button>
          </div>
          <div className="mt-2 text-sm text-muted-foreground">
            Example: https://www.dgsi.pt/jtrl.nsf/33182fc732316039802565fa00497eec/672fb9b269be9b2980258b63004fc517?OpenDocument
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {caseData && <ClaimDetails caseData={caseData} />}
    </div>
  )
}
