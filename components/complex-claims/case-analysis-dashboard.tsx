"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, Search, FileText, AlertCircle, Bar<PERSON>hart } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { SearchHistory } from "./search-history"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CaseStrategyAssistant } from "./dashboard/case-strategy-assistant"
import { LegalFramework } from "./dashboard/legal-framework"
import { JudgeIntelligence } from "./dashboard/judge-intelligence"
import { BriefingGenerator } from "./dashboard/briefing-generator"
import { RealTimeQA } from "./dashboard/real-time-qa"
import { PreparationToolkit } from "./dashboard/preparation-toolkit"
import { ContextAwareSuggestions } from "./dashboard/context-aware-suggestions"

// Define the history item interface
interface HistoryItem {
  id: string
  text: string
  timestamp: number
  type: 'url' | 'description' | 'case-analysis'
}

export function CaseAnalysisDashboard() {
  const [caseDescription, setCaseDescription] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [dashboardData, setDashboardData] = useState<any | null>(null)
  const [activeTab, setActiveTab] = useState<string>("strategy")

  // Function to add a search to history
  const addToSearchHistory = (searchText: string) => {
    // Get existing history from localStorage
    const storedHistory = localStorage.getItem('complexClaimsSearchHistory')
    let history: HistoryItem[] = []

    if (storedHistory) {
      try {
        history = JSON.parse(storedHistory)
      } catch (e) {
        console.error('Failed to parse search history:', e)
      }
    }

    // Create new history item
    const newItem: HistoryItem = {
      id: Date.now().toString(),
      text: searchText,
      timestamp: Date.now(),
      type: 'case-analysis'
    }

    // Add to history, avoiding duplicates
    const existingItemIndex = history.findIndex((item: HistoryItem) =>
      item.type === 'case-analysis' && item.text === searchText
    )

    if (existingItemIndex !== -1) {
      // Remove the existing item so we can add it to the top
      history.splice(existingItemIndex, 1)
    }

    // Add new item at the beginning
    history.unshift(newItem)

    // Limit history to 20 items
    if (history.length > 20) {
      history = history.slice(0, 20)
    }

    // Save back to localStorage
    localStorage.setItem('complexClaimsSearchHistory', JSON.stringify(history))
  }

  const handleGenerateDashboard = async () => {
    if (!caseDescription || caseDescription.length < 50) {
      setError("Please enter a more detailed case description (at least 50 characters)")
      return
    }

    setIsLoading(true)
    setError(null)
    setDashboardData(null)

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay and return sample data
      await new Promise(resolve => setTimeout(resolve, 2500))

      // Sample dashboard data
      const sampleDashboardData = {
        caseInfo: {
          title: "Insurance Claim Dispute - Auto Accident",
          parties: {
            plaintiff: "Michael Chen (Vehicle Owner)",
            defendant: "National Auto Insurance Co.",
            others: ["City Transit Authority (Third-party)", "Precision Auto Repair (Expert Witness)"]
          },
          jurisdiction: "9th Circuit, Northern District of California",
          judge: "Hon. Maria Rodriguez",
          filingDate: "2023-10-22",
          caseType: "Auto Insurance Contract Dispute",
          claimAmount: "$85,000",
          status: "Active - Discovery Phase"
        },
        strategy: {
          summary: "Auto insurance claim dispute over a severe collision at an intersection. Insurance company denied full coverage citing contributory negligence and pre-existing vehicle damage, while plaintiff argues the accident was caused entirely by a third party and all damage resulted from this collision.",
          legalIssues: [
            "Interpretation of 'collision coverage' policy language",
            "Applicability of comparative negligence principles",
            "Duty to investigate claim thoroughly",
            "Bad faith denial of full compensation"
          ],
          strengths: [
            "Police report indicating third party ran red light",
            "Expert testimony supporting all damage from single collision",
            "Photographic evidence of accident scene and vehicle damage",
            "History of premium payments without prior claims"
          ],
          weaknesses: [
            "Delayed professional inspection (2 days after accident)",
            "Minor pre-existing scratch on rear bumper (unrelated to collision)",
            "Potential contributory factor of speed (5mph over limit)",
            "Limited witness testimony beyond police report"
          ],
          suggestedTheories: [
            {
              theory: "Breach of Contract",
              likelihood: 0.85,
              explanation: "Strong basis for arguing the denial violates express terms of the policy covering collision damage regardless of fault determination."
            },
            {
              theory: "Insurance Bad Faith",
              likelihood: 0.70,
              explanation: "Evidence suggests insurer failed to properly investigate before partial denial, potentially constituting bad faith."
            },
            {
              theory: "Negligent Misrepresentation",
              likelihood: 0.45,
              explanation: "Statements by insurance agent regarding coverage scope for collision damage may support misrepresentation claim."
            }
          ],
          winningArguments: [
            {
              argument: "The collision was a covered event under the policy regardless of fault determination",
              priority: 1,
              reasoning: "Policy language explicitly covers collision damage without fault-based exclusions"
            },
            {
              argument: "Insurer failed to conduct reasonable investigation before partial denial",
              priority: 2,
              reasoning: "Adjuster spent only 20 minutes examining vehicle and did not consult with collision repair experts"
            },
            {
              argument: "Policy exclusions for pre-existing damage are ambiguous and should be construed against drafter",
              priority: 3,
              reasoning: "California law requires ambiguous terms to be interpreted in favor of insured"
            }
          ]
        },
        legalFramework: {
          statutes: [
            {
              citation: "Cal. Ins. Code § 530",
              relevance: "High",
              summary: "An insurer is liable for a loss of which a peril insured against was the proximate cause."
            },
            {
              citation: "Cal. Ins. Code § 11580.2",
              relevance: "High",
              summary: "Sets forth requirements for uninsured motorist coverage in California."
            },
            {
              citation: "Cal. Civ. Code § 1636",
              relevance: "Medium",
              summary: "Contract interpretation should give effect to mutual intention of parties."
            }
          ],
          caseLaw: [
            {
              citation: "Vardanyan v. AMCO Ins. Co., 243 Cal.App.4th 779 (2015)",
              relevance: "High",
              holding: "When policy language is ambiguous, it must be interpreted in favor of coverage that would be reasonably expected by the insured."
            },
            {
              citation: "Wilson v. 21st Century Ins. Co., 42 Cal.4th 713 (2007)",
              relevance: "High",
              holding: "An insurer must fully investigate claims before denying coverage, and failure to do so may constitute bad faith."
            },
            {
              citation: "Hibbs v. Allstate Ins. Co., 193 Cal.App.4th 809 (2011)",
              relevance: "Medium",
              holding: "Collision coverage applies regardless of fault determination unless explicitly excluded in policy language."
            }
          ],
          proceduralRules: [
            {
              rule: "Cal. Code Civ. Proc. § 437c",
              deadline: "Motion for Summary Judgment due 60 days before trial",
              note: "Opposing party has 14 days to respond"
            },
            {
              rule: "Local Rule 7-2",
              deadline: "Motions must be filed 35 days before hearing date",
              note: "Oppositions due 21 days before hearing"
            },
            {
              rule: "FRCP 26(a)(2)",
              deadline: "Expert disclosures due 90 days before trial",
              note: "Rebuttal experts due 30 days after initial disclosures"
            }
          ],
          constitutionalIssues: [
            {
              issue: "Due Process",
              relevance: "Low",
              explanation: "No significant constitutional issues identified in this contract dispute"
            }
          ]
        },
        judgeProfile: {
          name: "Hon. Maria Rodriguez",
          background: {
            education: "Stanford Law School, J.D., 1995",
            priorWork: "Partner at Davis & Wright (Insurance Defense), Assistant U.S. Attorney",
            appointed: "2015, Obama Administration",
            expertise: "Insurance law, commercial litigation"
          },
          rulingHistory: [
            {
              case: "Ramirez v. Progressive Insurance",
              outcome: "Ruled for policyholder",
              reasoning: "Found ambiguity in policy language regarding collision coverage exclusions"
            },
            {
              case: "Peterson v. State Farm",
              outcome: "Ruled for insurer",
              reasoning: "Policyholder failed to provide timely notice of claim"
            },
            {
              case: "Garcia v. Allstate",
              outcome: "Ruled for policyholder",
              reasoning: "Insurer's investigation was inadequate and constituted bad faith"
            }
          ],
          rhetoricalPreferences: {
            style: "Formalistic with strong emphasis on policy language",
            emotionUse: "Prefers fact-based arguments over emotional appeals",
            citations: "Favors California Supreme Court precedents and clear statutory language",
            questioning: "Known for active questioning on policy interpretation"
          },
          successPatterns: {
            summary: "Judge Rodriguez has ruled for policyholders in 65% of insurance cases",
            motionSuccess: {
              "Summary Judgment": "32% success rate for insurers, 45% for policyholders",
              "Motion to Dismiss": "28% success rate overall",
              "Discovery Motions": "70% granted at least partially"
            },
            favoredAuthorities: [
              "Cal. Supreme Court's Wilson v. 21st Century decision",
              "Principles of contra proferentem (construing ambiguities against drafter)",
              "California Insurance Code § 530"
            ]
          }
        },
        briefingTemplates: {
          mainArguments: [
            {
              title: "The collision constitutes a covered event under the policy regardless of fault determination",
              outline: "I. Policy language explicitly covers collision damage\nII. Evidence demonstrates the collision was the sole cause of damage\n   A. Police report and witness testimony\n   B. Expert analysis of vehicle damage patterns\nIII. California courts have consistently interpreted similar language to cover collision regardless of fault\n   A. Hibbs v. Allstate precedent\n   B. Application to present facts",
              citations: ["Hibbs v. Allstate Ins. Co., 193 Cal.App.4th 809 (2011)", "Cal. Ins. Code § 11580.2", "Wilson v. 21st Century Ins. Co., 42 Cal.4th 713 (2007)"]
            },
            {
              title: "Insurer's investigation was inadequate and constitutes bad faith",
              outline: "I. California law imposes duty of thorough investigation\nII. Defendant's investigation fell below industry standards\n   A. Limited time spent examining vehicle\n   B. Failure to consult appropriate collision experts\nIII. Bad faith liability arises from unreasonable investigation\n   A. Legal standard for bad faith\n   B. Application to defendant's conduct",
              citations: ["Wilson v. 21st Century Ins. Co., 42 Cal.4th 713 (2007)", "Egan v. Mutual of Omaha Ins. Co., 24 Cal.3d 809 (1979)"]
            }
          ],
          anticipatedCounterarguments: [
            {
              argument: "Some damage pre-existed the collision",
              rebuttal: "Expert testimony will establish that while a minor scratch existed on the rear bumper, it was unrelated to the collision damage. The collision damage pattern is consistent with a single high-impact event as documented in the accident report and supported by expert analysis."
            },
            {
              argument: "Plaintiff's speed contributed to the accident severity",
              rebuttal: "The police report indicates plaintiff was traveling only 5mph over the speed limit, while the third party ran a red light at high speed. Expert testimony will show that even at the posted speed limit, the damage would have been virtually identical given the force of impact."
            },
            {
              argument: "Plaintiff failed to take evasive action",
              rebuttal: "Accident reconstruction and witness testimony confirm the plaintiff had less than one second to react before impact, making evasive action impossible. California law does not require superhuman reflexes to qualify for full insurance coverage."
            }
          ],
          rhetoricalEnhancements: [
            {
              type: "Analogy",
              content: "Just as a homeowner who suffers storm damage isn't expected to prevent the weather, a driver with the right of way shouldn't be penalized for another driver's blatant traffic violation. The defendant's attempt to shift blame undermines the very purpose of collision coverage."
            },
            {
              type: "Policy Framing",
              content: "Auto insurance contracts represent a promise of security in exchange for premium payments. When insurers deny legitimate claims through strained interpretations of policy language, they undermine the very purpose of insurance and the reasonable expectations of policyholders."
            },
            {
              type: "Burden Shifting",
              content: "The defendant bears the burden of proving that an exclusion applies clearly and unambiguously. Their failure to demonstrate how this collision falls within any policy exclusion means the court must find in favor of full coverage."
            }
          ]
        },
        preparationToolkit: {
          trialChecklist: [
            "File motion in limine to exclude evidence of minor pre-existing scratch",
            "Prepare collision expert for cross-examination on damage patterns",
            "Create visual aids showing accident reconstruction and timeline",
            "Prepare client for testimony regarding driving history and accident details",
            "Subpoena complete claims file including internal communications",
            "Prepare for voir dire questions regarding insurance company bias"
          ],
          witnessStrategy: {
            plaintiff: {
              key: "Focus on immediate actions taken after collision, driving history, and communications with insurer",
              preparation: "Practice describing accident sequence in detail, emphasizing sudden nature and lack of time to react"
            },
            expert: {
              key: "Establish collision damage patterns through technical analysis",
              preparation: "Prepare clear visual aids explaining impact mechanics and resulting damage"
            },
            insurance_adjuster: {
              key: "Expose limited investigation and predetermined conclusion",
              preparation: "Prepare timeline of adjuster's actions compared to industry standards"
            }
          },
          exhibitStrategy: [
            {
              exhibit: "Photographs of vehicle damage",
              purpose: "Demonstrate severe nature of collision damage",
              presentation: "Before/after comparison with annotations highlighting impact points"
            },
            {
              exhibit: "Accident reconstruction report",
              purpose: "Technical evidence of collision dynamics",
              presentation: "Highlight key conclusions with supporting diagrams and simulations"
            },
            {
              exhibit: "Insurance policy",
              purpose: "Show coverage for collision damage regardless of fault",
              presentation: "Highlight relevant provisions with plain language explanation"
            },
            {
              exhibit: "Vehicle maintenance records",
              purpose: "Demonstrate responsible vehicle ownership",
              presentation: "Timeline of regular maintenance activities"
            }
          ],
          objections: [
            {
              anticipated: "Relevance objection to driving history",
              response: "Evidence demonstrates plaintiff's overall safe driving practices, directly rebutting defendant's claim of contributory negligence"
            },
            {
              anticipated: "Hearsay objection to witness statements at scene",
              response: "Admissible under present sense impression exception, as statements were made while observing the accident scene"
            },
            {
              anticipated: "Expert qualification challenge to accident reconstructionist",
              response: "Expert has 12 years of experience specifically analyzing vehicle collisions in insurance contexts, with relevant certifications in accident reconstruction"
            }
          ]
        },
        contextAwareSuggestions: {
          toneCalibration: {
            judgePreference: "Judge Rodriguez responds best to formal, technically precise arguments with clear policy analysis",
            recommendation: "Maintain professional, matter-of-fact tone focusing on policy language and precedent rather than emotional appeals about client hardship"
          },
          settlementEstimate: {
            range: "$65,000 - $78,000",
            basis: "Based on similar auto collision cases in this jurisdiction with comparable policy limits and damage extent",
            factors: [
              "Judge Rodriguez's history of splitting liability in similar cases",
              "Strength of police report evidence",
              "Potential for bad faith multiplier if case proceeds to trial",
              "Litigation costs likely to exceed $25,000 through trial"
            ]
          },
          additionalConsiderations: [
            "Consider requesting neutral auto damage appraiser evaluation as alternative dispute resolution",
            "Explore possibility of bifurcating contract and bad faith claims",
            "Research defendant's counsel's history in similar auto cases for insight into strategy",
            "Consider retaining additional expert on auto insurance industry standards"
          ]
        }
      }

      setDashboardData(sampleDashboardData)
      // Add to search history
      addToSearchHistory(caseDescription)
    } catch (err) {
      setError("An error occurred while generating the case analysis dashboard")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle selecting an item from history
  const handleSelectHistoryItem = (item: string) => {
    setCaseDescription(item)
    handleGenerateDashboard()
  }

  return (
    <div className="space-y-6">
      <SearchHistory type="case-analysis" onSelectItem={handleSelectHistoryItem} />
      <Card>
        <CardHeader>
          <CardTitle>Case Analysis Dashboard</CardTitle>
          <CardDescription>
            Enter a detailed case description to generate a comprehensive analysis dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Textarea
              placeholder="Enter a detailed description of your case including key facts, parties involved, legal issues, and relevant jurisdiction..."
              value={caseDescription}
              onChange={(e) => setCaseDescription(e.target.value)}
              className="min-h-[150px]"
            />

            <Button onClick={handleGenerateDashboard} disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Dashboard...
                </>
              ) : (
                <>
                  <BarChart className="mr-2 h-4 w-4" />
                  Generate Case Analysis Dashboard
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {dashboardData && (
        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                <div>
                  <CardTitle>{dashboardData.caseInfo.title}</CardTitle>
                  <CardDescription className="mt-1">
                    {dashboardData.caseInfo.jurisdiction} • {dashboardData.caseInfo.judge} • {dashboardData.caseInfo.status}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Export Dashboard
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Tabs defaultValue="strategy" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7">
              <TabsTrigger value="strategy">Strategy</TabsTrigger>
              <TabsTrigger value="legal">Legal Framework</TabsTrigger>
              <TabsTrigger value="judge">Judge Profile</TabsTrigger>
              <TabsTrigger value="briefing">Briefing</TabsTrigger>
              <TabsTrigger value="qa">Q&A</TabsTrigger>
              <TabsTrigger value="preparation">Preparation</TabsTrigger>
              <TabsTrigger value="context">Context</TabsTrigger>
            </TabsList>

            <TabsContent value="strategy" className="space-y-6">
              <CaseStrategyAssistant data={dashboardData.strategy} />
            </TabsContent>

            <TabsContent value="legal" className="space-y-6">
              <LegalFramework data={dashboardData.legalFramework} />
            </TabsContent>

            <TabsContent value="judge" className="space-y-6">
              <JudgeIntelligence data={dashboardData.judgeProfile} />
            </TabsContent>

            <TabsContent value="briefing" className="space-y-6">
              <BriefingGenerator data={dashboardData.briefingTemplates} />
            </TabsContent>

            <TabsContent value="qa" className="space-y-6">
              <RealTimeQA caseData={dashboardData} />
            </TabsContent>

            <TabsContent value="preparation" className="space-y-6">
              <PreparationToolkit data={dashboardData.preparationToolkit} />
            </TabsContent>

            <TabsContent value="context" className="space-y-6">
              <ContextAwareSuggestions data={dashboardData.contextAwareSuggestions} />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  )
}
