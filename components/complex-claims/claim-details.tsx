"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  FileText,
  User,
  Users,
  Calendar,
  Gavel,
  Tag,
  MapPin,
  Clock,
  AlertTriangle,
  DollarSign,
  CheckCircle,
  XCircle,
  Info,
  Scale,
  Building,
  FileCheck,
  Download,
  File,
  FileType,
  Share2
} from "lucide-react"
import { formatDate } from "@/lib/utils"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface ClaimDetailsProps {
  caseData: any
}

export function ClaimDetails({ caseData }: ClaimDetailsProps) {
  // Format date if it exists
  const formatDateString = (dateString: string) => {
    if (!dateString) return "N/A"
    try {
      return new Date(dateString).toLocaleDateString('pt-PT', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    } catch (e) {
      return dateString
    }
  }

  // Export functions
  const exportAsPDF = () => {
    // In a real implementation, this would generate a PDF
    // For now, we'll just show an alert
    alert('Exporting as PDF...')
    // In a real implementation, you would use a library like jsPDF or call a backend API
    console.log('Exporting case data as PDF:', caseData.process_number)
  }

  const exportAsJSON = () => {
    // Create a JSON file and trigger download
    const dataStr = JSON.stringify(caseData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr)

    const exportFileDefaultName = `claim_${caseData.process_number.replace(/\//g, '-')}.json`

    // Create a temporary link element
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.style.display = 'none'

    // Append to the document body
    document.body.appendChild(linkElement)

    // Trigger the download
    linkElement.click()

    // Clean up - use setTimeout to ensure the click event has time to process
    setTimeout(() => {
      document.body.removeChild(linkElement)
    }, 100)
  }

  const shareCaseLink = () => {
    // In a real implementation, this would copy the link to clipboard or open a share dialog
    if (navigator.clipboard) {
      navigator.clipboard.writeText(caseData.url || window.location.href)
        .then(() => alert('Link copied to clipboard!'))
        .catch(err => console.error('Failed to copy link:', err))
    } else {
      alert('Copy this link: ' + (caseData.url || window.location.href))
    }
  }

  return (
    <Card className="border-2 border-primary/20">
      <CardHeader className="bg-muted/50">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div>
            <CardTitle className="text-2xl">{caseData.process_number}</CardTitle>
            <CardDescription className="text-base">
              {caseData.court_name} • {formatDateString(caseData.decision_date)}
            </CardDescription>
          </div>
          <div className="flex flex-wrap gap-2">
            <Badge variant={caseData.decision_type === "PROCEDENTE" ? "default" :
                          caseData.decision_type === "IMPROCEDENTE" ? "destructive" :
                          "outline"}>
              {caseData.decision_type}
            </Badge>
            <Badge variant="outline">{caseData.procedure_type}</Badge>
            {caseData.unanimous_decision && (
              <Badge variant="secondary">Unanimous</Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <Tabs defaultValue="summary" className="space-y-4">
          <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="parties">Parties</TabsTrigger>
            <TabsTrigger value="incident">Incident</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="legal">Legal Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Info className="h-5 w-5 text-primary" />
                  Case Overview
                </h3>
                <p className="text-muted-foreground">{caseData.eli5_summary || "No summary available"}</p>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Tag className="h-5 w-5 text-primary" />
                  Keywords
                </h3>
                <div className="flex flex-wrap gap-2">
                  {caseData.keywords?.map((keyword: string, index: number) => (
                    <Badge key={index} variant="secondary">{keyword}</Badge>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5 text-primary" />
                Case Summary
              </h3>
              <div className="p-4 bg-muted rounded-md text-sm">
                {caseData.sumario || "No detailed summary available"}
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={exportAsPDF}>
                    <File className="mr-2 h-4 w-4" />
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={exportAsJSON}>
                    <FileType className="mr-2 h-4 w-4" />
                    Export as JSON
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={shareCaseLink}>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Link
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button variant="outline" onClick={() => window.open(caseData.url, '_blank')}>
                View Original Document
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="parties" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Parties Involved
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {caseData.parties?.map((party: any, index: number) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader className="bg-muted/50 py-3">
                      <CardTitle className="text-base flex items-center gap-2">
                        {party.party_type === 'person' ? (
                          <User className="h-4 w-4" />
                        ) : party.party_type === 'lawyer' ? (
                          <Scale className="h-4 w-4" />
                        ) : party.party_type === 'insurance_company' ? (
                          <Building className="h-4 w-4" />
                        ) : (
                          <Users className="h-4 w-4" />
                        )}
                        {party.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Role:</span>
                        <Badge variant="outline" className="capitalize">{party.role}</Badge>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-muted-foreground">Type:</span>
                        <Badge variant="secondary" className="capitalize">{party.party_type}</Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {caseData.insurance_company && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
                    <Building className="h-5 w-5 text-primary" />
                    Insurance Details
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="p-4 bg-muted rounded-md">
                      <div className="text-sm text-muted-foreground">Insurance Company</div>
                      <div className="font-medium">{caseData.insurance_company}</div>
                    </div>

                    <div className="p-4 bg-muted rounded-md">
                      <div className="text-sm text-muted-foreground">Contract Number</div>
                      <div className="font-medium">{caseData.contract_number || "N/A"}</div>
                    </div>

                    <div className="p-4 bg-muted rounded-md">
                      <div className="text-sm text-muted-foreground">Line of Business</div>
                      <div className="font-medium capitalize">{caseData.insurance_lob || "N/A"}</div>
                    </div>

                    <div className="p-4 bg-muted rounded-md">
                      <div className="text-sm text-muted-foreground">Contract Date</div>
                      <div className="font-medium">{formatDateString(caseData.contract_date)}</div>
                    </div>

                    <div className="p-4 bg-muted rounded-md col-span-1 md:col-span-2">
                      <div className="text-sm text-muted-foreground">Coverage Period</div>
                      <div className="font-medium">
                        {caseData.coverage_period ?
                          `${formatDateString(caseData.coverage_period[0])} to ${formatDateString(caseData.coverage_period[1])}` :
                          "N/A"}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="incident" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-primary" />
                Incident Details
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-muted rounded-md">
                  <div className="text-sm text-muted-foreground">Date</div>
                  <div className="font-medium">{formatDateString(caseData.incident_date)}</div>
                </div>

                <div className="p-4 bg-muted rounded-md">
                  <div className="text-sm text-muted-foreground">Location</div>
                  <div className="font-medium">{caseData.incident_location || "N/A"}</div>
                </div>
              </div>

              <div className="p-4 bg-muted rounded-md">
                <div className="text-sm text-muted-foreground">Description</div>
                <div className="font-medium mt-1">{caseData.incident_description || "No description available"}</div>
              </div>

              {caseData.dispute_reason && (
                <div className="p-4 bg-muted rounded-md">
                  <div className="text-sm text-muted-foreground">Dispute Reason</div>
                  <div className="font-medium mt-1">{caseData.dispute_reason}</div>
                </div>
              )}

              <div className="flex items-center gap-4 mt-2">
                {caseData.deaths !== null && (
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive">{caseData.deaths || 0} Deaths</Badge>
                  </div>
                )}

                {caseData.injureds !== null && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{caseData.injureds || 0} Injured</Badge>
                  </div>
                )}

                {caseData.fraud !== null && (
                  <div className="flex items-center gap-2">
                    <Badge variant={caseData.fraud ? "destructive" : "outline"}>
                      {caseData.fraud ? "Fraud Detected" : "No Fraud"}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="financial" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-primary" />
                Financial Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="py-3">
                    <CardTitle className="text-base">Total Claimed</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {caseData.total_claimed ?
                        new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' }).format(caseData.total_claimed) :
                        "N/A"}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="py-3">
                    <CardTitle className="text-base">Compensation Awarded</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {caseData.compensations_awarded ?
                        new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' }).format(caseData.compensations_awarded) :
                        "N/A"}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="py-3">
                    <CardTitle className="text-base">Recovery Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {caseData.total_claimed && caseData.compensations_awarded ?
                        `${((caseData.compensations_awarded / caseData.total_claimed) * 100).toFixed(1)}%` :
                        "N/A"}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {caseData.claimed_amounts && caseData.claimed_amounts.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-md font-semibold mb-2">Claimed Amounts</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-muted">
                          <th className="text-left p-2 border">Item</th>
                          <th className="text-right p-2 border">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {caseData.claimed_amounts.map((item: any, index: number) => (
                          <tr key={index} className="border-b">
                            <td className="p-2 border">{item.item}</td>
                            <td className="text-right p-2 border">
                              {new Intl.NumberFormat('pt-PT', { style: 'currency', currency: 'EUR' }).format(item.amount)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="legal" className="space-y-4">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Scale className="h-5 w-5 text-primary" />
                Legal Analysis
              </h3>

              {caseData.contractual_issues && caseData.contractual_issues.length > 0 && (
                <div>
                  <h4 className="text-md font-semibold mb-2">Contractual Issues</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {caseData.contractual_issues.map((issue: string, index: number) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </div>
              )}

              {caseData.contractual_disputes && caseData.contractual_disputes.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-md font-semibold mb-2">Contractual Disputes</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {caseData.contractual_disputes.map((dispute: string, index: number) => (
                      <li key={index}>{dispute}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {caseData.arguments_pro_plaintiff && caseData.arguments_pro_plaintiff.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Arguments for Plaintiff
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {caseData.arguments_pro_plaintiff.map((arg: string, index: number) => (
                        <li key={index}>{arg}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {caseData.arguments_pro_defendant && caseData.arguments_pro_defendant.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                      Arguments for Defendant
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {caseData.arguments_pro_defendant.map((arg: string, index: number) => (
                        <li key={index}>{arg}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {caseData.arguments_con_plaintiff && caseData.arguments_con_plaintiff.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold mb-2 flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-500" />
                      Arguments Against Plaintiff
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {caseData.arguments_con_plaintiff.map((arg: string, index: number) => (
                        <li key={index}>{arg}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {caseData.arguments_con_defendant && caseData.arguments_con_defendant.length > 0 && (
                  <div>
                    <h4 className="text-md font-semibold mb-2 flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-500" />
                      Arguments Against Defendant
                    </h4>
                    <ul className="list-disc pl-5 space-y-1">
                      {caseData.arguments_con_defendant.map((arg: string, index: number) => (
                        <li key={index}>{arg}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {caseData.legal_basis && caseData.legal_basis.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-md font-semibold mb-2 flex items-center gap-2">
                    <FileCheck className="h-4 w-4 text-primary" />
                    Legal Basis
                  </h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {caseData.legal_basis.map((basis: string, index: number) => (
                      <li key={index}>{basis}</li>
                    ))}
                  </ul>
                </div>
              )}

              {caseData.judge_rationale && (
                <div className="p-4 bg-muted rounded-md mt-4">
                  <h4 className="text-md font-semibold mb-2">Judge's Rationale</h4>
                  <p>{caseData.judge_rationale}</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
