"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, Gavel, Clock, AlertTriangle } from "lucide-react"

interface LegalFrameworkProps {
  data: {
    statutes: {
      citation: string
      relevance: string
      summary: string
    }[]
    caseLaw: {
      citation: string
      relevance: string
      holding: string
    }[]
    proceduralRules: {
      rule: string
      deadline: string
      note: string
    }[]
    constitutionalIssues: {
      issue: string
      relevance: string
      explanation: string
    }[]
  }
}

export function LegalFramework({ data }: LegalFrameworkProps) {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="statutes" className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="statutes">Statutes</TabsTrigger>
          <TabsTrigger value="cases">Case Law</TabsTrigger>
          <TabsTrigger value="procedural">Procedural Rules</TabsTrigger>
          <TabsTrigger value="constitutional">Constitutional</TabsTrigger>
        </TabsList>
        
        <TabsContent value="statutes">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5" />
                Relevant Statutes
              </CardTitle>
              <CardDescription>
                Key statutory provisions applicable to your case
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-4">
                  {data.statutes.map((statute, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{statute.citation}</h4>
                        <Badge variant={
                          statute.relevance === "High" ? "default" : 
                          statute.relevance === "Medium" ? "secondary" : 
                          "outline"
                        }>
                          {statute.relevance} Relevance
                        </Badge>
                      </div>
                      <p className="text-sm">{statute.summary}</p>
                      {index < data.statutes.length - 1 && <Separator className="my-2" />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="cases">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gavel className="mr-2 h-5 w-5" />
                Relevant Case Law
              </CardTitle>
              <CardDescription>
                Precedents and similar cases with key holdings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-4">
                  {data.caseLaw.map((case_, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{case_.citation}</h4>
                        <Badge variant={
                          case_.relevance === "High" ? "default" : 
                          case_.relevance === "Medium" ? "secondary" : 
                          "outline"
                        }>
                          {case_.relevance} Relevance
                        </Badge>
                      </div>
                      <p className="text-sm">{case_.holding}</p>
                      {index < data.caseLaw.length - 1 && <Separator className="my-2" />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="procedural">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-5 w-5" />
                Procedural Rules & Deadlines
              </CardTitle>
              <CardDescription>
                Important procedural requirements and timelines
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.proceduralRules.map((rule, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                      <h4 className="font-medium">{rule.rule}</h4>
                      <Badge variant="outline" className="md:ml-auto">
                        {rule.deadline}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{rule.note}</p>
                    {index < data.proceduralRules.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="constitutional">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Constitutional Constraints
              </CardTitle>
              <CardDescription>
                Constitutional issues and considerations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.constitutionalIssues.map((issue, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{issue.issue}</h4>
                      <Badge variant={
                        issue.relevance === "High" ? "default" : 
                        issue.relevance === "Medium" ? "secondary" : 
                        "outline"
                      }>
                        {issue.relevance} Relevance
                      </Badge>
                    </div>
                    <p className="text-sm">{issue.explanation}</p>
                    {index < data.constitutionalIssues.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
