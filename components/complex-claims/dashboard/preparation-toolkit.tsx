"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, User, UserPlus, FileCheck, AlertTriangle } from "lucide-react"

interface PreparationToolkitProps {
  data: {
    trialChecklist: string[]
    witnessStrategy: {
      [key: string]: {
        key: string
        preparation: string
      }
    }
    exhibitStrategy: {
      exhibit: string
      purpose: string
      presentation: string
    }[]
    objections: {
      anticipated: string
      response: string
    }[]
  }
}

export function PreparationToolkit({ data }: PreparationToolkitProps) {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="checklist" className="space-y-4">
        <TabsList className="grid grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="checklist">Trial Checklist</TabsTrigger>
          <TabsTrigger value="witnesses">Witness Strategy</TabsTrigger>
          <TabsTrigger value="exhibits">Exhibit Strategy</TabsTrigger>
          <TabsTrigger value="objections">Objections</TabsTrigger>
        </TabsList>
        
        <TabsContent value="checklist">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5" />
                Trial Day Checklist
              </CardTitle>
              <CardDescription>
                Key items to prepare before trial
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {data.trialChecklist.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="h-6 w-6 flex items-center justify-center rounded-full border mr-2 flex-shrink-0">
                      {index + 1}
                    </div>
                    <span className="text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="witnesses">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Witness & Examination Strategy
              </CardTitle>
              <CardDescription>
                Preparation guidance for key witnesses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(data.witnessStrategy).map(([type, strategy], index) => (
                  <div key={type} className="space-y-3">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">
                        {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Key Focus</h4>
                      <p className="text-sm">{strategy.key}</p>
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Preparation Notes</h4>
                      <p className="text-sm">{strategy.preparation}</p>
                    </div>
                    
                    {index < Object.entries(data.witnessStrategy).length - 1 && <Separator />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="exhibits">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileCheck className="mr-2 h-5 w-5" />
                Exhibit Strategy
              </CardTitle>
              <CardDescription>
                Key exhibits and presentation guidance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {data.exhibitStrategy.map((exhibit, index) => (
                  <div key={index} className="space-y-3">
                    <h4 className="font-medium">{exhibit.exhibit}</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="text-sm font-medium">Purpose</h5>
                        <p className="text-sm">{exhibit.purpose}</p>
                      </div>
                      
                      <div>
                        <h5 className="text-sm font-medium">Presentation Strategy</h5>
                        <p className="text-sm">{exhibit.presentation}</p>
                      </div>
                    </div>
                    
                    {index < data.exhibitStrategy.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="objections">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5" />
                Mock Objections Generator
              </CardTitle>
              <CardDescription>
                Anticipated objections and prepared responses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {data.objections.map((objection, index) => (
                  <div key={index} className="space-y-3">
                    <div className="space-y-1">
                      <Badge variant="destructive">Anticipated Objection</Badge>
                      <p className="text-sm font-medium">{objection.anticipated}</p>
                    </div>
                    
                    <div className="space-y-1">
                      <Badge variant="default">Your Response</Badge>
                      <p className="text-sm">{objection.response}</p>
                    </div>
                    
                    {index < data.objections.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
