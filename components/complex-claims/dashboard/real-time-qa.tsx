"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { MessageSquare, Send, Loader2, <PERSON>r, <PERSON><PERSON>, <PERSON><PERSON>, Trash } from "lucide-react"

interface RealTimeQAProps {
  caseData: any
}

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  sources?: string[]
}

export function RealTimeQA({ caseData }: RealTimeQAProps) {
  const [question, setQuestion] = useState<string>('')
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Ask me anything about this case, legal precedents, or the judge. I can provide insights based on the case analysis.',
    }
  ])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  
  const handleSendQuestion = async () => {
    if (!question.trim()) return
    
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: question
    }
    
    setMessages(prev => [...prev, userMessage])
    setQuestion('')
    setIsLoading(true)
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Generate sample response based on question
    let response = ''
    let sources: string[] = []
    
    if (question.toLowerCase().includes('judge') || question.toLowerCase().includes('rodriguez')) {
      response = "Judge Rodriguez has ruled favorably for policyholders in 65% of insurance cases, particularly when there's evidence of inadequate investigation by insurers. She tends to focus heavily on policy language and expects well-structured arguments with clear citations to California precedent."
      sources = [
        "Judicial profile database",
        "Westlake HOA v. Travelers (2022)",
        "Mendoza v. Liberty Mutual (2021)"
      ]
    } else if (question.toLowerCase().includes('precedent') || question.toLowerCase().includes('case law')) {
      response = "The key precedent for your case is Julian v. Hartford Underwriters Ins. Co. (2005), which established that in California, if a covered peril is the predominant cause of loss, the insurer must cover the claim even if excluded perils contributed. This 'efficient proximate cause' doctrine strongly supports your position that the sudden pipe burst is covered despite any pre-existing conditions."
      sources = [
        "Julian v. Hartford Underwriters Ins. Co., 35 Cal.4th 747 (2005)",
        "Garvey v. State Farm Fire & Casualty Co., 48 Cal.3d 395 (1989)",
        "Cal. Ins. Code § 530"
      ]
    } else if (question.toLowerCase().includes('deadline') || question.toLowerCase().includes('timeline')) {
      response = "Your key deadlines are:\n1. Expert disclosures due 90 days before trial (FRCP 26(a)(2))\n2. Motion for Summary Judgment due 60 days before trial (Cal. Code Civ. Proc. § 437c)\n3. Oppositions to MSJ due 14 days after filing\n4. Motions in limine due 30 days before trial"
      sources = [
        "FRCP 26(a)(2)",
        "Cal. Code Civ. Proc. § 437c",
        "Local Rule 7-2"
      ]
    } else {
      response = "Based on the case analysis, the strongest argument is that the pipe burst constitutes a 'sudden and accidental' event explicitly covered by the policy. The expert testimony and immediate reporting strongly support this position. Judge Rodriguez has previously ruled favorably in similar cases where policy language regarding water damage was found to be ambiguous, as seen in Mendoza v. Liberty Mutual."
      sources = [
        "Policy analysis section",
        "Mendoza v. Liberty Mutual precedent",
        "Expert testimony summary"
      ]
    }
    
    // Add assistant response
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: response,
      sources: sources
    }
    
    setMessages(prev => [...prev, assistantMessage])
    setIsLoading(false)
  }
  
  const clearConversation = () => {
    setMessages([
      {
        id: '1',
        role: 'assistant',
        content: 'Ask me anything about this case, legal precedents, or the judge. I can provide insights based on the case analysis.',
      }
    ])
  }
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        alert("Copied to clipboard!")
      })
      .catch(err => {
        console.error('Failed to copy: ', err)
      })
  }
  
  return (
    <div className="space-y-4">
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Real-Time Case Q&A
            </CardTitle>
            <Button variant="outline" size="icon" onClick={clearConversation}>
              <Trash className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Ask questions about the case, legal precedents, or strategy
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <ScrollArea className="flex-1 pr-4 mb-4">
            <div className="space-y-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] ${message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} rounded-lg p-3`}>
                    <div className="flex items-center gap-2 mb-1">
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                      <span className="text-xs font-medium">
                        {message.role === 'user' ? 'You' : 'Assistant'}
                      </span>
                      {message.role === 'assistant' && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-6 w-6 ml-auto"
                          onClick={() => copyToClipboard(message.content)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <p className="text-sm whitespace-pre-line">{message.content}</p>
                    {message.sources && message.sources.length > 0 && (
                      <div className="mt-2">
                        <Separator className="my-2" />
                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">Sources:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {message.sources.map((source, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {source}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-muted rounded-lg p-3">
                    <div className="flex items-center">
                      <Bot className="h-4 w-4 mr-2" />
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
          
          <div className="flex gap-2">
            <Input
              placeholder="Ask a question about the case..."
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !isLoading) {
                  handleSendQuestion()
                }
              }}
              disabled={isLoading}
            />
            <Button onClick={handleSendQuestion} disabled={isLoading || !question.trim()}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
