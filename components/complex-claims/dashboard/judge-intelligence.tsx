"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, MessageSquare, <PERSON><PERSON><PERSON> } from "lucide-react"

interface JudgeIntelligenceProps {
  data: {
    name: string
    background: {
      education: string
      priorWork: string
      appointed: string
      expertise: string
    }
    rulingHistory: {
      case: string
      outcome: string
      reasoning: string
    }[]
    rhetoricalPreferences: {
      style: string
      emotionUse: string
      citations: string
      questioning: string
    }
    successPatterns: {
      summary: string
      motionSuccess: {
        [key: string]: string
      }
      favoredAuthorities: string[]
    }
  }
}

export function JudgeIntelligence({ data }: JudgeIntelligenceProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="mr-2 h-5 w-5" />
            Judicial Profile: {data.name}
          </CardTitle>
        </Card<PERSON><PERSON>er>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm mb-2">Education & Background</h4>
              <ul className="space-y-1">
                <li className="text-sm"><span className="font-medium">Education:</span> {data.background.education}</li>
                <li className="text-sm"><span className="font-medium">Prior Work:</span> {data.background.priorWork}</li>
                <li className="text-sm"><span className="font-medium">Appointed:</span> {data.background.appointed}</li>
                <li className="text-sm"><span className="font-medium">Expertise:</span> {data.background.expertise}</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-sm mb-2">Rhetorical Preferences</h4>
              <ul className="space-y-1">
                <li className="text-sm"><span className="font-medium">Style:</span> {data.rhetoricalPreferences.style}</li>
                <li className="text-sm"><span className="font-medium">Emotion:</span> {data.rhetoricalPreferences.emotionUse}</li>
                <li className="text-sm"><span className="font-medium">Citations:</span> {data.rhetoricalPreferences.citations}</li>
                <li className="text-sm"><span className="font-medium">Questioning:</span> {data.rhetoricalPreferences.questioning}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Previous Rulings in Similar Cases
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.rulingHistory.map((ruling, index) => (
              <div key={index} className="space-y-2">
                <h4 className="font-medium text-sm">{ruling.case}</h4>
                <div className="flex items-center">
                  <Badge variant={
                    ruling.outcome.includes("for policyholder") ? "default" : 
                    ruling.outcome.includes("for insurer") ? "destructive" : 
                    "secondary"
                  }>
                    {ruling.outcome}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{ruling.reasoning}</p>
                {index < data.rulingHistory.length - 1 && <Separator className="my-2" />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart className="mr-2 h-5 w-5" />
            Win/Loss Patterns
          </CardTitle>
          <CardDescription>{data.successPatterns.summary}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium text-sm mb-2">Success Rates by Motion Type</h4>
            <div className="space-y-3">
              {Object.entries(data.successPatterns.motionSuccess).map(([motion, rate], index) => (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{motion}</span>
                    <span className="text-sm text-muted-foreground">{rate}</span>
                  </div>
                  <Progress 
                    value={parseInt(rate.match(/\d+/)?.[0] || "0")} 
                    className="h-2" 
                  />
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-sm mb-2">Commonly Cited Authorities</h4>
            <ul className="space-y-1">
              {data.successPatterns.favoredAuthorities.map((authority, index) => (
                <li key={index} className="text-sm flex items-start">
                  <span className="mr-2">•</span>
                  {authority}
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
