"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { FileText, Shield, Lightbulb, Copy, Download } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface BriefingGeneratorProps {
  data: {
    mainArguments: {
      title: string
      outline: string
      citations: string[]
    }[]
    anticipatedCounterarguments: {
      argument: string
      rebuttal: string
    }[]
    rhetoricalEnhancements: {
      type: string
      content: string
    }[]
  }
}

export function BriefingGenerator({ data }: BriefingGeneratorProps) {
  const [selectedArgument, setSelectedArgument] = useState<number>(0)
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        alert("Copied to clipboard!")
      })
      .catch(err => {
        console.error('Failed to copy: ', err)
      })
  }
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="arguments" className="space-y-4">
        <TabsList className="grid grid-cols-1 md:grid-cols-3">
          <TabsTrigger value="arguments">Draft Arguments</TabsTrigger>
          <TabsTrigger value="counterarguments">Counterarguments</TabsTrigger>
          <TabsTrigger value="rhetorical">Rhetorical Enhancers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="arguments">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle className="text-base">Arguments</CardTitle>
                <CardDescription>
                  Select an argument to view details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {data.mainArguments.map((arg, index) => (
                    <Button 
                      key={index}
                      variant={selectedArgument === index ? "default" : "outline"}
                      className="w-full justify-start text-left h-auto py-3"
                      onClick={() => setSelectedArgument(index)}
                    >
                      <FileText className="mr-2 h-4 w-4 flex-shrink-0" />
                      <span className="truncate">{arg.title}</span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    {data.mainArguments[selectedArgument]?.title}
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="icon"
                      onClick={() => copyToClipboard(
                        `${data.mainArguments[selectedArgument]?.title}\n\n${data.mainArguments[selectedArgument]?.outline}\n\nCitations:\n${data.mainArguments[selectedArgument]?.citations.join('\n')}`
                      )}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-sm mb-2">Argument Outline</h4>
                  <div className="bg-muted p-4 rounded-md">
                    <pre className="text-sm whitespace-pre-wrap font-mono">
                      {data.mainArguments[selectedArgument]?.outline}
                    </pre>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-sm mb-2">Citations</h4>
                  <ul className="space-y-1">
                    {data.mainArguments[selectedArgument]?.citations.map((citation, index) => (
                      <li key={index} className="text-sm">
                        {citation}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="counterarguments">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="mr-2 h-5 w-5" />
                Anticipated Counterarguments
              </CardTitle>
              <CardDescription>
                Prepare for opposing counsel's likely arguments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-6">
                  {data.anticipatedCounterarguments.map((counter, index) => (
                    <div key={index} className="space-y-3">
                      <div className="space-y-1">
                        <Badge variant="outline">Opposing Argument</Badge>
                        <h4 className="font-medium">{counter.argument}</h4>
                      </div>
                      
                      <div className="space-y-1">
                        <Badge>Your Rebuttal</Badge>
                        <p className="text-sm">{counter.rebuttal}</p>
                      </div>
                      
                      <div className="flex justify-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => copyToClipboard(
                            `Opposing Argument: ${counter.argument}\n\nRebuttal: ${counter.rebuttal}`
                          )}
                        >
                          <Copy className="mr-2 h-3 w-3" />
                          Copy
                        </Button>
                      </div>
                      
                      {index < data.anticipatedCounterarguments.length - 1 && <Separator />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="rhetorical">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="mr-2 h-5 w-5" />
                Rhetorical Enhancers
              </CardTitle>
              <CardDescription>
                Persuasive devices to strengthen your arguments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {data.rhetoricalEnhancements.map((enhancement, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{enhancement.type}</h4>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(enhancement.content)}
                      >
                        <Copy className="mr-2 h-3 w-3" />
                        Copy
                      </Button>
                    </div>
                    <p className="text-sm">{enhancement.content}</p>
                    {index < data.rhetoricalEnhancements.length - 1 && <Separator className="my-2" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
