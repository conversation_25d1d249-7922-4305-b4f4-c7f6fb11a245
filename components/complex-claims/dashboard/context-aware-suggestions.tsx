"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MessageSquare, DollarSign, Lightbulb } from "lucide-react"

interface ContextAwareSuggestionsProps {
  data: {
    toneCalibration: {
      judgePreference: string
      recommendation: string
    }
    settlementEstimate: {
      range: string
      basis: string
      factors: string[]
    }
    additionalConsiderations: string[]
  }
}

export function ContextAwareSuggestions({ data }: ContextAwareSuggestionsProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="mr-2 h-5 w-5" />
            Tone Calibration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Judge's Preference</h4>
            <p className="text-sm">{data.toneCalibration.judgePreference}</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Recommendation</h4>
            <p className="text-sm">{data.toneCalibration.recommendation}</p>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-5 w-5" />
            Settlement Estimator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">Estimated Range</h4>
            <Badge variant="secondary" className="text-lg font-bold">
              {data.settlementEstimate.range}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Basis for Estimate</h4>
            <p className="text-sm">{data.settlementEstimate.basis}</p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Key Factors</h4>
            <ul className="space-y-1">
              {data.settlementEstimate.factors.map((factor, index) => (
                <li key={index} className="text-sm flex items-start">
                  <span className="mr-2">•</span>
                  {factor}
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="mr-2 h-5 w-5" />
            Additional Strategic Considerations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            {data.additionalConsiderations.map((consideration, index) => (
              <li key={index} className="text-sm flex items-start">
                <Badge variant="outline" className="mr-2 mt-0.5">
                  {index + 1}
                </Badge>
                {consideration}
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
