"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Scale, CheckCircle, XCircle, Lightbulb, ListChecks } from "lucide-react"

interface CaseStrategyAssistantProps {
  data: {
    summary: string
    legalIssues: string[]
    strengths: string[]
    weaknesses: string[]
    suggestedTheories: {
      theory: string
      likelihood: number
      explanation: string
    }[]
    winningArguments: {
      argument: string
      priority: number
      reasoning: string
    }[]
  }
}

export function CaseStrategyAssistant({ data }: CaseStrategyAssistantProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Scale className="mr-2 h-5 w-5" />
            Case Summary Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm">{data.summary}</p>
          
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Key Legal Issues</h4>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {data.legalIssues.map((issue, index) => (
                <li key={index} className="flex items-start">
                  <Badge variant="outline" className="mr-2 mt-0.5">
                    {index + 1}
                  </Badge>
                  <span className="text-sm">{issue}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h4 className="font-medium text-sm flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                Case Strengths
              </h4>
              <ul className="space-y-2">
                {data.strengths.map((strength, index) => (
                  <li key={index} className="text-sm flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    {strength}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-sm flex items-center">
                <XCircle className="mr-2 h-4 w-4 text-red-500" />
                Case Weaknesses
              </h4>
              <ul className="space-y-2">
                {data.weaknesses.map((weakness, index) => (
                  <li key={index} className="text-sm flex items-start">
                    <span className="text-red-500 mr-2">•</span>
                    {weakness}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="mr-2 h-5 w-5" />
            Suggested Legal Theories
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {data.suggestedTheories.map((theory, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">{theory.theory}</h4>
                <Badge variant={theory.likelihood > 0.7 ? "default" : theory.likelihood > 0.5 ? "secondary" : "outline"}>
                  {(theory.likelihood * 100).toFixed(0)}% Likelihood
                </Badge>
              </div>
              <Progress value={theory.likelihood * 100} className="h-2" />
              <p className="text-sm text-muted-foreground">{theory.explanation}</p>
              {index < data.suggestedTheories.length - 1 && <Separator className="my-2" />}
            </div>
          ))}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ListChecks className="mr-2 h-5 w-5" />
            Winning Argument Prioritization
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {data.winningArguments.map((arg, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-start">
                <Badge className="mr-2 mt-0.5">{arg.priority}</Badge>
                <div>
                  <h4 className="font-medium text-sm">{arg.argument}</h4>
                  <p className="text-sm text-muted-foreground mt-1">{arg.reasoning}</p>
                </div>
              </div>
              {index < data.winningArguments.length - 1 && <Separator className="my-3" />}
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
