"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>cw, Trash2 } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface SearchHistoryProps {
  type: 'url' | 'description' | 'case-analysis'
  onSelectItem: (item: string) => void
}

interface HistoryItem {
  id: string
  text: string
  timestamp: number
  type: 'url' | 'description' | 'case-analysis'
}

export function SearchHistory({ type, onSelectItem }: SearchHistoryProps) {
  const [history, setHistory] = useState<HistoryItem[]>([])

  // Load history from localStorage on component mount
  useEffect(() => {
    const storedHistory = localStorage.getItem('complexClaimsSearchHistory')
    if (storedHistory) {
      try {
        const parsedHistory = JSON.parse(storedHistory)
        setHistory(parsedHistory)
      } catch (e) {
        console.error('Failed to parse search history:', e)
        setHistory([])
      }
    }
  }, [])

  // Filter history by type
  const filteredHistory = history.filter(item => item.type === type)

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleString('pt-PT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Truncate text for display
  const truncateText = (text: string, maxLength: number = 40) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  // Clear all history
  const clearAllHistory = () => {
    if (confirm('Are you sure you want to clear all search history?')) {
      localStorage.removeItem('complexClaimsSearchHistory')
      setHistory([])
    }
  }

  // Clear history item
  const clearHistoryItem = (id: string) => {
    const updatedHistory = history.filter(item => item.id !== id)
    localStorage.setItem('complexClaimsSearchHistory', JSON.stringify(updatedHistory))
    setHistory(updatedHistory)
  }

  // Clear history by type
  const clearHistoryByType = () => {
    const typeLabel =
      type === 'url' ? 'URL' :
      type === 'description' ? 'description' :
      'case analysis';

    if (confirm(`Are you sure you want to clear all ${typeLabel} search history?`)) {
      const updatedHistory = history.filter(item => item.type !== type)
      localStorage.setItem('complexClaimsSearchHistory', JSON.stringify(updatedHistory))
      setHistory(updatedHistory)
    }
  }

  if (filteredHistory.length === 0) {
    return null
  }

  return (
    <Card>
      <CardHeader className="py-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center">
            <Clock className="mr-2 h-4 w-4" />
            Recent Searches
          </CardTitle>
          <div className="flex gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={clearHistoryByType}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Clear {type} history</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <CardDescription>
          Your recent {type === 'url' ? 'URL' : type === 'description' ? 'description' : 'case analysis'} searches
        </CardDescription>
      </CardHeader>
      <CardContent className="py-2">
        <ScrollArea className="h-[120px] pr-4">
          <div className="space-y-2">
            {filteredHistory.slice(0, 5).map((item) => (
              <div key={item.id} className="flex items-center justify-between bg-muted/50 p-2 rounded-md">
                <div className="flex-1 truncate">
                  <div className="flex items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-left font-normal hover:text-primary"
                      onClick={() => onSelectItem(item.text)}
                    >
                      <RotateCcw className="mr-2 h-3 w-3" />
                      {truncateText(item.text)}
                    </Button>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {formatTimestamp(item.timestamp)}
                  </div>
                </div>
                <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => clearHistoryItem(item.id)}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
