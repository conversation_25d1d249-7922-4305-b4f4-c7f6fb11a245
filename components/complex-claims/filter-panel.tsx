"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Filter,
  ChevronDown,
  ChevronUp,
  Calendar,
  Gavel,
  Building,
  Tag,
  X
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface FilterPanelProps {
  onApplyFilters: (filters: FilterOptions) => void
  availableKeywords: string[]
  availableCourts: string[]
  availableDecisionTypes: string[]
}

export interface FilterOptions {
  courts: string[]
  decisionTypes: string[]
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
  keywords: string[]
}

export function FilterPanel({
  onApplyFilters,
  availableKeywords,
  availableCourts,
  availableDecisionTypes
}: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [selectedCourts, setSelectedCourts] = useState<string[]>([])
  const [selectedDecisionTypes, setSelectedDecisionTypes] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined
  })
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])

  const toggleExpand = () => {
    setIsExpanded(!isExpanded)
  }

  const handleCourtChange = (court: string) => {
    setSelectedCourts(prev =>
      prev.includes(court)
        ? prev.filter(c => c !== court)
        : [...prev, court]
    )
  }

  const handleDecisionTypeChange = (type: string) => {
    setSelectedDecisionTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }

  const handleKeywordChange = (keyword: string) => {
    setSelectedKeywords(prev =>
      prev.includes(keyword)
        ? prev.filter(k => k !== keyword)
        : [...prev, keyword]
    )
  }

  const applyFilters = () => {
    onApplyFilters({
      courts: selectedCourts,
      decisionTypes: selectedDecisionTypes,
      dateRange: {
        from: dateRange.from || undefined,
        to: dateRange.to || undefined
      },
      keywords: selectedKeywords
    })
  }

  const clearFilters = () => {
    setSelectedCourts([])
    setSelectedDecisionTypes([])
    setDateRange({
      from: undefined,
      to: undefined
    })
    setSelectedKeywords([])

    onApplyFilters({
      courts: [],
      decisionTypes: [],
      dateRange: {
        from: undefined,
        to: undefined
      },
      keywords: []
    })
  }

  const hasActiveFilters = () => {
    return (
      selectedCourts.length > 0 ||
      selectedDecisionTypes.length > 0 ||
      dateRange.from !== undefined ||
      dateRange.to !== undefined ||
      selectedKeywords.length > 0
    )
  }

  return (
    <Card>
      <CardHeader className="py-3">
        <div
          className="flex items-center justify-between cursor-pointer"
          onClick={toggleExpand}
        >
          <CardTitle className="text-base flex items-center">
            <Filter className="mr-2 h-4 w-4" />
            Filter Results
            {hasActiveFilters() && (
              <Badge variant="secondary" className="ml-2">
                {selectedCourts.length + selectedDecisionTypes.length +
                 (dateRange.from ? 1 : 0) + selectedKeywords.length} active
              </Badge>
            )}
          </CardTitle>
          <Button variant="ghost" size="icon">
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        {!isExpanded && hasActiveFilters() && (
          <div className="flex flex-wrap gap-1 mt-2">
            {selectedCourts.map(court => (
              <Badge key={`court-${court}`} variant="outline" className="flex items-center gap-1">
                <Building className="h-3 w-3" />
                {court}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleCourtChange(court)
                  }}
                />
              </Badge>
            ))}

            {selectedDecisionTypes.map(type => (
              <Badge key={`type-${type}`} variant="outline" className="flex items-center gap-1">
                <Gavel className="h-3 w-3" />
                {type}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDecisionTypeChange(type)
                  }}
                />
              </Badge>
            ))}

            {dateRange.from && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {dateRange.from ? format(dateRange.from, "dd/MM/yyyy") : ""}
                {dateRange.to && ` - ${dateRange.to ? format(dateRange.to, "dd/MM/yyyy") : ""}`}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    setDateRange({ from: undefined, to: undefined })
                  }}
                />
              </Badge>
            )}

            {selectedKeywords.map(keyword => (
              <Badge key={`keyword-${keyword}`} variant="outline" className="flex items-center gap-1">
                <Tag className="h-3 w-3" />
                {keyword}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleKeywordChange(keyword)
                  }}
                />
              </Badge>
            ))}
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium flex items-center mb-2">
                <Building className="mr-2 h-4 w-4" />
                Courts
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availableCourts.map(court => (
                  <div key={court} className="flex items-center space-x-2">
                    <Checkbox
                      id={`court-${court}`}
                      checked={selectedCourts.includes(court)}
                      onCheckedChange={() => handleCourtChange(court)}
                    />
                    <Label htmlFor={`court-${court}`} className="text-sm font-normal">
                      {court}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium flex items-center mb-2">
                <Gavel className="mr-2 h-4 w-4" />
                Decision Types
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availableDecisionTypes.map(type => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}`}
                      checked={selectedDecisionTypes.includes(type)}
                      onCheckedChange={() => handleDecisionTypeChange(type)}
                    />
                    <Label htmlFor={`type-${type}`} className="text-sm font-normal">
                      {type}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium flex items-center mb-2">
                <Calendar className="mr-2 h-4 w-4" />
                Date Range
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.from && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateRange.from && dateRange.from instanceof Date ? format(dateRange.from, "PPP") : "From date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={dateRange.from}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.to && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateRange.to && dateRange.to instanceof Date ? format(dateRange.to, "PPP") : "To date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={dateRange.to}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium flex items-center mb-2">
                <Tag className="mr-2 h-4 w-4" />
                Keywords
              </Label>
              <div className="flex flex-wrap gap-2">
                {availableKeywords.map(keyword => (
                  <Badge
                    key={keyword}
                    variant={selectedKeywords.includes(keyword) ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => handleKeywordChange(keyword)}
                  >
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex justify-between pt-2">
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
              <Button onClick={applyFilters}>
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
