"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FileUploader } from "@/components/file-uploader"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useState } from "react"

interface WorkflowFormData {
  referenceNumber: string;
  priority: string;
  description: string;
  attachments: File[];
}
interface WorkflowFormProps {
  workflowTitle: string;
  workflowType: string;
  onSubmit: (data: WorkflowFormData) => void;
  isEditing?: boolean;
}



export function WorkflowForm({ workflowTitle, workflowType, onSubmit }: WorkflowFormProps) {
  const [formData, setFormData] = useState<WorkflowFormData>({
    referenceNumber: "",
    priority: "",
    description: "",
    attachments: [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const getFormFields = () => {
    switch (workflowType) {
      case 'FNOL Processing':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="incidentDate">Incident Date</Label>
              <Input
                id="incidentDate"
                type="date"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="policyNumber">Policy Number</Label>
              <Input
                id="policyNumber"
                placeholder="Enter policy number"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="incidentType">Incident Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select incident type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="accident">Accident</SelectItem>
                  <SelectItem value="theft">Theft</SelectItem>
                  <SelectItem value="damage">Property Damage</SelectItem>
                  <SelectItem value="injury">Personal Injury</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )

      case 'Fraud Detection':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="claimNumber">Claim Number</Label>
              <Input
                id="claimNumber"
                placeholder="Enter claim number"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="suspiciousActivity">Suspicious Activity Description</Label>
              <Textarea
                id="suspiciousActivity"
                placeholder="Describe the suspicious activity"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="evidenceFiles">Supporting Evidence</Label>
              <FileUploader
                id="evidenceFiles"
                onFilesSelected={(files) => setFormData(prev => ({ ...prev, attachments: files }))}
                accept="image/*,.pdf"
                multiple
              />
            </div>
          </>
        )

      case 'Vehicle Appraisal':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="vehicleVin">Vehicle VIN</Label>
              <Input
                id="vehicleVin"
                placeholder="Enter vehicle VIN"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="vehiclePhotos">Vehicle Photos</Label>
              <FileUploader
                id="vehiclePhotos"
                onFilesSelected={(files) => setFormData(prev => ({ ...prev, attachments: files }))}
                accept="image/*"
                multiple
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="damageDescription">Damage Description</Label>
              <Textarea
                id="damageDescription"
                placeholder="Describe the vehicle damage"
                required
              />
            </div>
          </>
        )

      default:
        return (
          <Alert>
            <AlertDescription>
              No specific form fields available for this workflow type. Please provide general information below.
            </AlertDescription>
          </Alert>
        )
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="referenceNumber">Reference Number</Label>
        <Input
          id="referenceNumber"
          value={formData.referenceNumber}
          onChange={(e) => setFormData(prev => ({ ...prev, referenceNumber: e.target.value }))}
          placeholder="Enter reference number"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="priority">Priority</Label>
        <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
          <SelectTrigger>
            <SelectValue placeholder="Select priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {getFormFields()}

      <div className="space-y-2">
        <Label htmlFor="description">Additional Notes</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Enter any additional notes"
        />
      </div>

      <Button type="submit" className="w-full">
        Run Workflow
      </Button>
    </form>
  )
}

