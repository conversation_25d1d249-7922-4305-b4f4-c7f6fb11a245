import { Card, CardContent } from "@/components/ui/card"
import { ImageCarousel } from "@/components/image-carousel"
import { cn } from "@/lib/utils"

interface VehicleCardProps {
  vehicle: {
    Source_Url: string;
    Brand: string;
    Cilinder: string;
    Potency: string;
    Potency_Value: number;
    Potency_Unit: string;
    Milage: string;
    Fuel: string;
    Gearbox: string;
    Year: number;
    Location: string;
    Seller: string;
    Price: number;
    Currency: string;
    Image: string[];
    Doors?: number;
    Model: string;
  };
  compact?: boolean; // Add prop for compact mode
}

export function VehicleCard({ vehicle, compact = false }: VehicleCardProps) {
  const title = `${vehicle.Brand} ${vehicle.Model}`;

  const handleClick = () => {
    window.open(vehicle.Source_Url, '_blank');
  };

  return (
    <Card 
      className={cn(
        "overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300 group relative max-w-full",
        compact && "h-full" // Ensure consistent height in compact mode
      )}
      onClick={handleClick}
    >
      {/* Title above image - smaller in compact mode */}
      <div className={cn(
        compact ? "p-1.5 pb-1" : "p-3 pb-2"
      )}>
        <h3 className={cn(
          "font-semibold truncate group-hover:text-primary transition-colors",
          compact ? "text-sm" : "text-base"
        )}>
          {title}
        </h3>
      </div>

      {/* Image carousel with positioned elements */}
      <div className="relative">
        <ImageCarousel 
          images={vehicle.Image?.length > 0 ? vehicle.Image : ['/images/vehicle-placeholder.jpg']} 
          alt={title}
          compact={compact} // Pass the compact prop
        />
        
        {/* Check if vehicle is sold and add sold overlay if needed */}
        {vehicle.Price <= 0 && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/40 z-15">
            <div className="bg-black/80 text-white px-3 py-1 rounded-md font-bold transform rotate-[-5deg]">
              SOLD
            </div>
          </div>
        )}
        
        {/* Fuel type badge - top left */}
        <div className="absolute top-2 left-2 z-10">
          <span className={cn(
            "bg-black/75 text-white font-medium rounded-md",
            compact ? "text-[10px] px-1.5 py-0.5" : "text-xs px-2 py-1"
          )}>
            {vehicle.Fuel || "Vehicle"}
          </span>
        </div>
        
        {/* Price badge - BOTTOM LEFT */}
        {vehicle.Price > 0 && (
          <div className="absolute bottom-2 left-2 z-20">
            <div className={cn(
              "bg-primary/90 text-white font-medium rounded-sm shadow-sm",
              compact ? "text-sm px-1.5 py-0.5" : "text-base px-2 py-1"
            )}>
              {vehicle.Price?.toLocaleString() || "N/A"} {vehicle.Currency || "€"}
            </div>
          </div>
        )}
      </div>
      
      {compact ? (
        // Ultra compact info for the row layout
        <div className="p-2 flex justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1.5">
            <span className="w-3 h-3 flex-shrink-0">🗓️</span>
            <span>{vehicle.Year !== -1 ? vehicle.Year : 'N/A'}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="w-3 h-3 flex-shrink-0">🛣️</span>
            <span>{vehicle.Milage}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="w-3 h-3 flex-shrink-0">⚙️</span>
            <span>{vehicle.Gearbox === "Manual" ? "Man" : vehicle.Gearbox === "Automatic" ? "Auto" : vehicle.Gearbox || "Man"}</span>
          </div>
        </div>
      ) : (
        // Original layout for normal view
        <CardContent className="p-4">
          {/* Specs in a compact layout */}
          <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 flex-shrink-0">🗓️</span>
              <span>{vehicle.Year !== -1 ? vehicle.Year : 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 flex-shrink-0">🛣️</span>
              <span>{vehicle.Milage}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 flex-shrink-0">⚙️</span>
              <span>{vehicle.Gearbox || "Manual"}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="w-4 h-4 flex-shrink-0">⚡</span>
              <span>{vehicle.Potency || "N/A"}</span>
            </div>
          </div>
          
          {/* Location if available */}
          {vehicle.Location && (
            <div className="mt-3 text-xs text-muted-foreground flex items-center">
              <span className="w-4 h-4 flex-shrink-0">📍</span>
              <span className="ml-1">{vehicle.Location}</span>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}

