"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { DollarSign, Calendar, BarChart2, ChevronLeft, ChevronRight, Users, Search, X, RefreshCw, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

type LeakageStatus = "Open" | "Closed" | "Pending" | "Confirmed" | "Rejected";

interface Leakage {
  id: string;
  openDate: string;
  urgencyLevel: string;
  leakage_type: string;
  product: string;
  status: LeakageStatus;
  confirmedLeakage?: boolean;
  estimatedLoss?: number;
  pendingRecovery?: number;
  actualRecovery?: number;
  observation?: string;
  modelScore: number;
}

export function LeakageCard({ leakage }: { leakage: Leakage }) {
  const [leakageStatus, setLeakageStatus] = useState<LeakageStatus>(leakage.status);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pendingRecovery, setPendingRecovery] = useState("0");
  const [actualRecovery, setActualRecovery] = useState("0");
  const [confirmedLeakage, setConfirmedLeakage] = useState(false);
  const [observation, setObservation] = useState("");

  const handleStatusChange = () => {
    if (leakageStatus === "Open") {
      setIsModalOpen(true);
    } else {
      setLeakageStatus("Open");
      setConfirmedLeakage(false);
      setPendingRecovery("0");
      setActualRecovery("0");
      setObservation("");
    }
  };

  const handleCloseConfirmation = () => {
    setLeakageStatus("Closed");
    setIsModalOpen(false);
  };

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
      <div className={`h-2 bg-${leakage.urgencyLevel.toLowerCase()}-500`} />
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold mb-1">{leakage.id}</h3>
            <p className="text-sm text-gray-500">{leakage.product}</p>
          </div>
          <Badge variant={leakageStatus === "Open" ? "default" : "secondary"}>{leakageStatus}</Badge>
        </div>

        <div className="flex items-center mb-4">
          <Calendar className="w-4 h-4 mr-2 text-gray-400" />
          <span className="text-sm">{leakage.openDate}</span>
        </div>

        <div className="bg-muted p-4 rounded-lg mb-4">
          <h4 className="text-sm font-semibold">Leakage Type</h4>
          <p className="text-sm mb-2">{leakage.leakage_type}</p>
          <div className="flex items-center">
            <BarChart2 className="w-4 h-4 mr-2 text-gray-400" />
            <span className="text-sm">Score: {leakage.modelScore.toFixed(2)}</span>
          </div>
          <Progress value={leakage.modelScore * 100} className="h-2 mb-2" />
        </div>

        <div className="flex justify-end space-x-2">
          <Button onClick={handleStatusChange} size="sm" variant="outline" className="flex items-center">
            {leakageStatus === "Open" ? (
              <><X className="w-4 h-4 mr-2" /> Close</>
            ) : (
              <><RefreshCw className="w-4 h-4 mr-2" /> Reopen</>
            )}
          </Button>
          <Button size="sm" className="flex items-center">
            <Search className="w-4 h-4 mr-2" /> Inspect
          </Button>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Leakage Closure</DialogTitle>
              <DialogDescription>
                Please provide details about the leakage closure.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="confirmed-leakage"
                  checked={confirmedLeakage}
                  onCheckedChange={setConfirmedLeakage}
                />
                <Label htmlFor="confirmed-leakage">Confirmed Leakage</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="pending-recovery">Pending Recovery</Label>
                <Input
                  id="pending-recovery"
                  type="number"
                  value={pendingRecovery}
                  onChange={(e) => setPendingRecovery(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="actual-recovery">Actual Recovery</Label>
                <Input
                  id="actual-recovery"
                  type="number"
                  value={actualRecovery}
                  onChange={(e) => setActualRecovery(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleCloseConfirmation}>Confirm Closure</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
