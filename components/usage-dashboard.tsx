"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from 'lucide-react'
// import Lenis from '@studio-freight/lenis'
import { UsageOverviewCards } from "./usage/usage-overview-cards"
import { FeatureUsageChart } from "./usage/feature-usage-chart"
import { CostBreakdownChart } from "./usage/cost-breakdown-chart"
import { UsageTrendsChart } from "./usage/usage-trends-chart"
import { FeatureUsageTable } from "./usage/feature-usage-table"
import { RecommendationsCard } from "./usage/recommendations-card"

const timeRanges = [
  { value: "7d", label: "Last 7 days" },
  { value: "30d", label: "Last 30 days" },
  { value: "90d", label: "Last 90 days" },
  { value: "1y", label: "Last year" },
]

export function UsageDashboard() {


  const [selectedTimeRange, setSelectedTimeRange] = useState("30d")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Usage Dashboard</h1>
        <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            {timeRanges.map((range) => (
              <SelectItem key={range.value} value={range.value}>
                {range.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <UsageOverviewCards timeRange={selectedTimeRange} />

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Feature Usage Distribution</CardTitle>
            <CardDescription>Breakdown of feature usage across the platform</CardDescription>
          </CardHeader>
          <CardContent>
            <FeatureUsageChart timeRange={selectedTimeRange} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cost Breakdown</CardTitle>
            <CardDescription>Cost distribution by feature category</CardDescription>
          </CardHeader>
          <CardContent>
            <CostBreakdownChart timeRange={selectedTimeRange} />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usage Trends</CardTitle>
          <CardDescription>Track feature usage patterns over time</CardDescription>
        </CardHeader>
        <CardContent>
          <UsageTrendsChart timeRange={selectedTimeRange} />
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Detailed Feature Usage</CardTitle>
            <CardDescription>Usage statistics for individual features</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="analytics">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="models">Models</TabsTrigger>
                <TabsTrigger value="workflows">Workflows</TabsTrigger>
                <TabsTrigger value="tools">Tools</TabsTrigger>
                <TabsTrigger value="other">Other</TabsTrigger>
              </TabsList>
              <TabsContent value="analytics">
                <FeatureUsageTable 
                  category="Analytics" 
                  timeRange={selectedTimeRange} 
                />
              </TabsContent>
              <TabsContent value="models">
                <FeatureUsageTable 
                  category="Models" 
                  timeRange={selectedTimeRange} 
                />
              </TabsContent>
              <TabsContent value="workflows">
                <FeatureUsageTable 
                  category="Workflows" 
                  timeRange={selectedTimeRange} 
                />
              </TabsContent>
              <TabsContent value="tools">
                <FeatureUsageTable 
                  category="Tools" 
                  timeRange={selectedTimeRange} 
                />
              </TabsContent>
              <TabsContent value="other">
                <FeatureUsageTable 
                  category="Other" 
                  timeRange={selectedTimeRange} 
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
            <CardDescription>Optimize your usage and costs</CardDescription>
          </CardHeader>
          <CardContent>
            <RecommendationsCard timeRange={selectedTimeRange} />
          </CardContent>
        </Card>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Usage Notice</AlertTitle>
        <AlertDescription>
          Usage data is updated hourly. Costs are estimated based on your current plan and may vary.
        </AlertDescription>
      </Alert>
    </div>
  )
}

