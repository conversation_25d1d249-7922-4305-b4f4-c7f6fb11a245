'use client'

import { useState, useEffect, useCallback } from 'react'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { useSession } from 'next-auth/react' 

export default function ProfilePic() {
  const { data: session } = useSession()
  const [uploading, setUploading] = useState(false)
  const [userData, setUserData] = useState<any>(null)
  const [imageUrl, setImageUrl] = useState<string>('/avatars/01.png')

  const userId = session?.user?.id

  // Fetch user data from the API
  const fetchUserData = useCallback(async () => {
    if (!userId) return

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch user data')
      }

      const data = await response.json()
      setUserData(data)
      setImageUrl(data.profile_pic || '/avatars/01.png')
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }, [userId])

  useEffect(() => {
    fetchUserData()
  }, [fetchUserData])

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0]) return;
  
    setUploading(true);
    const file = e.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId || '');
  
    try {
      const response = await fetch('/api/upload-profile-pic', {
        method: 'POST',
        body: formData,
      });
  
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
  
      const data = await response.json();
  
      // Notify other components about the update
      window.dispatchEvent(new CustomEvent('profile-pic-updated', { detail: { image: data.image_url } }));
  
      // Update local state
      await fetchUserData();
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  };
  

  return (
    <div className="flex items-center space-x-4">
      <Avatar className="h-20 w-20">
        <AvatarImage src={imageUrl} alt={userData?.username || 'User'} />
        <AvatarFallback>
          {userData?.username
            ? userData.username
                .split(' ')
                .map((n: string) => n[0])
                .join('')
                .toUpperCase()
            : '?'}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col space-y-2">
        <Button
          type="button"
          variant="outline"
          disabled={uploading}
          onClick={() => document.getElementById('profile-pic-input')?.click()}
        >
          {uploading ? 'Uploading...' : 'Change Picture'}
        </Button>
        <input
          id="profile-pic-input"
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>
    </div>
  )
}
