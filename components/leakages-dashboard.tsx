"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import LeakagesByTypeChart from "./charts/leakages-by-type-chart"
import LeakagesTrendChart from "./charts/leakages-trend-chart"
import TopLeakagesTable from "./top-leakages-table"
import LeakagesByCauseChart from "./charts/leakages-by-cause-chart"

export default function LeakagesDashboard() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card className="col-span-full">
        <CardHeader>
          <CardTitle>Leakages Trend</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <LeakagesTrendChart />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Leakages by Type</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <LeakagesByTypeChart />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Leakages by Cause</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <LeakagesByCauseChart />
        </CardContent>
      </Card>
      <Card className="col-span-full">
        <CardHeader>
          <CardTitle>Top Leakages</CardTitle>
        </CardHeader>
        <CardContent>
          <TopLeakagesTable />
        </CardContent>
      </Card>
    </div>
  )
}

