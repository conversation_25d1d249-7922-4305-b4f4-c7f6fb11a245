"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Settings, Info, PlayCircle, Pencil } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { WorkflowForm } from "./workflow-form"
import { WorkflowProgress } from "./workflow-progress"
import { WorkflowReport } from "./workflow-report"
import { useState } from "react"

interface Task {
  name: string;
  description: string;
}

interface WorkflowCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  tasks: Task[];
  successRate: number;
  averageTime: string;
  status: string;
  type: string;
}
interface WorkflowFormData {
  referenceNumber: string;
  priority: string;
  description: string;
  attachments: File[];
}
type WorkflowState = "idle" | "running" | "completed"

export function WorkflowCard({
  title,
  description,
  icon,
  tasks,
  successRate,
  averageTime,
  status,
  type
}: WorkflowCardProps) {
  const [isActive, setIsActive] = useState(status === "active");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [workflowState, setWorkflowState] = useState<WorkflowState>("idle");
  const [workflowSuccess, setWorkflowSuccess] = useState(false);
  const [executionStartTime, setExecutionStartTime] = useState<Date | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleRunWorkflow = (formData: WorkflowFormData) => {
    setWorkflowState("running");
    setExecutionStartTime(new Date());
    
    // Transform tasks for the progress component
    const progressTasks = tasks.map(task => ({
      ...task,
      status: "pending" as const,
      progress: 0
    }));

    // Show progress dialog
    setWorkflowProgress(progressTasks);
  };

  const [workflowProgress, setWorkflowProgress] = useState<Array<Task & { status: "pending" | "running" | "completed" | "failed", progress: number }>>([]);

  const handleWorkflowComplete = (success: boolean) => {
    setWorkflowSuccess(success);
    setWorkflowState("completed");
  };

  const getExecutionTime = () => {
    if (!executionStartTime) return "N/A";
    const endTime = new Date();
    const diff = endTime.getTime() - executionStartTime.getTime();
    return `${(diff / 1000).toFixed(1)} seconds`;
  };

  const getMockResults = (success: boolean) => ({
    summary: success
      ? `Successfully completed ${title} workflow with all tasks executed as expected.`
      : `Workflow execution encountered issues during processing.`,
    details: success
      ? tasks.map(task => `Successfully completed: ${task.name}`)
      : [...tasks.slice(0, -1).map(task => `Completed: ${task.name}`), `Failed: ${tasks[tasks.length - 1].name}`],
    recommendations: success
      ? ["Review the generated reports", "Proceed with the next steps in the process"]
      : ["Check system logs for detailed error information", "Retry the workflow after addressing the issues"],
    warnings: success
      ? []
      : ["Some tasks may need manual review", "Data consistency should be verified"]
  });

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setWorkflowState("idle");
    setWorkflowProgress([]);
    setExecutionStartTime(null);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setIsDialogOpen(true);
    setWorkflowState("idle");
  };

  return (
    <>
      <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                {icon}
              </div>
              <div>
                <CardTitle className="text-lg">{title}</CardTitle>
                <CardDescription className="mt-1">{description}</CardDescription>
              </div>
            </div>
            <Badge variant={type === "core" ? "default" : "secondary"}>
              {type === "core" ? "Core" : "Custom"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Success Rate</span>
              <span className="font-medium">{successRate}%</span>
            </div>
            <Progress value={successRate} className="h-2" />
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Average Time</span>
              <p className="font-medium">{averageTime}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Status</span>
              <p className="font-medium">{isActive ? "Active" : "Inactive"}</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Tasks</div>
            <div className="grid grid-cols-2 gap-2">
              {tasks.map((task, index) => (
                <TooltipProvider key={index}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="justify-start">
                        {task.name}
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{task.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>
          </div>

          <div className="flex justify-between pt-2">
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => {
                  setIsEditing(false);
                  setIsDialogOpen(true);
                }}
                disabled={!isActive}
              >
                <PlayCircle className="mr-2 h-4 w-4" />
                Run
              </Button>
            </div>
            <div className="space-x-2">
              <Button variant="ghost" size="sm">
                <Info className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {isEditing ? `Edit Workflow: ${title}` : 
                workflowState === "idle" ? `Run Workflow: ${title}` :
                workflowState === "running" ? "Executing Workflow" :
                "Workflow Results"}
            </DialogTitle>
            <DialogDescription>
              {isEditing ? "Modify the workflow settings and configuration." :
                workflowState === "idle" ? "Enter the required information to start this workflow." :
                workflowState === "running" ? "Please wait while the workflow is being executed." :
                workflowSuccess ? "Workflow completed successfully." : "Workflow execution failed."}
            </DialogDescription>
          </DialogHeader>
          
          {(workflowState === "idle" || isEditing) && (
            <WorkflowForm
              workflowTitle={title}
              workflowType={title}
              onSubmit={isEditing ? (data) => {
                console.log('Saving workflow changes:', data);
                setIsEditing(false);
                setIsDialogOpen(false);
              } : handleRunWorkflow}
              isEditing={isEditing}
            />
          )}
          
          {workflowState === "running" && !isEditing && (
            <WorkflowProgress
              tasks={workflowProgress}
              onComplete={handleWorkflowComplete}
            />
          )}
          
          {workflowState === "completed" && !isEditing && (
            <WorkflowReport
              success={workflowSuccess}
              workflowTitle={title}
              executionTime={getExecutionTime()}
              results={getMockResults(workflowSuccess)}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

