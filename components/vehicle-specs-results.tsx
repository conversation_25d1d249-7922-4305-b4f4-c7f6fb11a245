import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface VehicleSpecs {
  brand: string
  model: string
  fuelType: string
  vin: string
  version: string
  hp: number
  kw: number
  cilindrada: number
  segment: string
}

export function VehicleSpecsResults({ specs }: { specs: VehicleSpecs }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Specifications</CardTitle>
        <CardDescription>Detailed information about the vehicle</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Brand</p>
            <p className="text-xl font-bold">{specs.brand}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Model</p>
            <p className="text-xl font-bold">{specs.model}</p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium">VIN</p>
          <p className="text-xl font-bold">{specs.vin}</p>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Version</p>
            <p className="text-xl font-bold">{specs.version}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Fuel Type</p>
            <p className="text-xl font-bold">{specs.fuelType}</p>
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <p className="text-sm font-medium">Horsepower</p>
            <p className="text-xl font-bold">{specs.hp} HP</p>
          </div>
          <div>
            <p className="text-sm font-medium">Kilowatts</p>
            <p className="text-xl font-bold">{specs.kw} kW</p>
          </div>
          <div>
            <p className="text-sm font-medium">Engine Displacement</p>
            <p className="text-xl font-bold">{specs.cilindrada} cc</p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium">Segment</p>
          <p className="text-xl font-bold">{specs.segment}</p>
        </div>
      </CardContent>
    </Card>
  )
}

