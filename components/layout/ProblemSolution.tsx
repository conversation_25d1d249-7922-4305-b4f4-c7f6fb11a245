import React, { useState } from 'react';
import { Safari } from "@/components/ui/mac";
import SneakPeak from "@/components/ui/sneakpeak";
import dynamic from 'next/dynamic';
import { AvatarCircles } from '@/components/users';
import { Button } from "@/components/ui/button"
import { Calendly<PERSON>utton } from "@/components/ui/calendly-button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from 'lucide-react';
import { motion } from 'framer-motion';

// Import LineShadowText with no SSR since it uses client hooks
const ProcessHeading = dynamic(() => import('@/components/layout/ProcessHeading').then(mod => mod.ProcessHeading), {
  ssr: false,
});

export function SafariDemo() {
  return (
    <div className="relative flex justify-center">
      <Safari
        url="rekover.ai/demo"
        width={1000}
        height={625}
        content={
          <SneakPeak />
        }
      />
    </div>
  );
}

const ProblemSolution = () => {
  // State for the demo form
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [demoDate, setDemoDate] = useState('');
  const [demoTime, setDemoTime] = useState('');

  // Handle demo form submission
  const handleDemoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle demo booking logic here
    console.log({ name, email, company, demoDate, demoTime });
    // Reset form or close dialog, etc.
    setName('');
    setEmail('');
    setCompany('');
    setDemoDate('');
    setDemoTime('');
    // Consider adding logic to close the dialog after submission
  };

  // Reduced number of testimonial avatars for better visual appearance
  const testimonialAvatars = [
    {
      imageUrl: "https://avatars.githubusercontent.com/u/********",
      profileUrl: "https://github.com/dillionverma",
    },
    {
      imageUrl: "https://avatars.githubusercontent.com/u/********",
      profileUrl: "https://github.com/tomonarifeehan",
    },
    {
      imageUrl: "https://avatars.githubusercontent.com/u/*********",
      profileUrl: "https://github.com/BankkRoll",
    },
    {
      imageUrl: "https://avatars.githubusercontent.com/u/********",
      profileUrl: "https://github.com/safethecode",
    }
  ];

  return (
    <section className="bg-[#141b2b] pt-0 pb-24 relative">
      {/* Top gradient overlay for smooth transition */}
      <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-[#141b2b]/80 to-[#141b2b] z-[1]"></div>

      {/* Decorative elements for continuity */}
      <div className="absolute top-0 left-0 right-0 h-32 overflow-hidden">
        <div className="w-full h-full opacity-10">
          {[...Array(10)].map((_, i) => (
            <div
              key={i}
              className="absolute h-1 bg-gradient-to-r from-transparent via-[#0be5a9]/30 to-transparent"
              style={{
                top: `${i * 3}px`,
                left: '0',
                right: '0',
                transform: `scaleX(${0.8 + (i * 0.02)})`
              }}
            ></div>
          ))}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        {/* Problem-Solution Statements */}
        <div className="text-center mb-16 relative">
          {/* Top Line Insurance Fraud & Claims Inefficiencies Are Costing Businesses Billions.*/}
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[120%] h-px bg-gradient-to-r from-transparent via-[#0be5a9]/20 to-transparent" />

          <div className="py-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-16">
              <span className="text-gray-400">Insurance Companies Need </span>
              <span className="text-white"> AI Solutions Built by </span>
              <span className="text-gray-400">Industry Experts.</span>
            </h2>
            <div className="space-y-6">
              <p className="text-2xl md:text-3xl text-gray-300">
                <span className="text-white font-semibold">Custom AI solutions</span> tailored to insurance-specific challenges and workflows.
              </p>
              <p className="text-2xl md:text-3xl text-gray-300">
                <span className="text-white font-semibold">Marketplace of features</span> ready to deploy via SaaS or API integration.
              </p>
            </div>
          </div>
          {/* Bottom Line */}
          <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-[120%] h-px bg-gradient-to-r from-transparent via-[#0be5a9]/20 to-transparent" />
        </div>

        {/* Process Section */}
        <div>
          {/* Replace static heading with dynamic ProcessHeading component */}
          <ProcessHeading />

          <div className="flex flex-col md:flex-row gap-12">
            {/* Left Column - 25% */}
            <div className="md:w-1/4 space-y-8">
              <div>
                <h3 className="text-2xl font-bold text-white mb-4">How We Work</h3>
                <p className="text-gray-400">
                  We develop <span className="text-white">end-to-end AI solutions</span> for the insurance industry, from <span className="text-white">conception to deployment</span> and <span className="text-white">maintenance</span>, delivered via <span className="text-white">intuitive UI</span> and <span className="text-white">API integrations</span>.
                </p>
              </div>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="text-[#0be5a9] text-lg font-bold">01.</div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Consultation</h4>
                    <p className="text-gray-400 text-sm">We analyze your <span className="text-white">business needs</span>, <span className="text-white">existing systems</span>, and <span className="text-white">data sources</span> to create a <span className="text-white">custom AI strategy</span>.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="text-[#0be5a9] text-lg font-bold">02.</div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Development</h4>
                    <p className="text-gray-400 text-sm">Our <span className="text-white">AI and insurance experts</span> build custom solutions or integrate <span className="text-white">pre-built marketplace features</span> with your <span className="text-white">existing systems</span>.</p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="text-[#0be5a9] text-lg font-bold">03.</div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Implementation</h4>
                    <p className="text-gray-400 text-sm">We deploy solutions via <span className="text-white">SaaS platform</span> or <span className="text-white">API integration</span>, providing <span className="text-white">ongoing support</span> and <span className="text-white">continuous improvement</span>.</p>
                  </div>
                </div>
              </div>

              {/* Updated testimonial section with improved styling */}
              <div className="flex items-center gap-3 bg-[#1a2436] rounded-lg p-3">
                <AvatarCircles
                  avatarUrls={testimonialAvatars}
                  numPeople={10}
                  className="justify-center scale-90"
                />
                <p className="text-gray-400 text-sm text-left">
                  Trusted by <span className="text-white font-semibold">10+</span> users
                </p>
              </div>
            </div>
            {/* Right Column - 75% */}
            <div className="md:w-3/4 relative">
              <SafariDemo />
              <div className="mt-8 text-center">
                {/* CTA section */}
                <div className="inline-flex items-center space-x-4">
                  <span className="text-gray-400">Streamline claims and reduce fraud with our AI solution</span>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <CalendlyButton
                      className="bg-[#f69323] text-white px-6 py-2 rounded-full font-medium text-sm hover:bg-[#f69323]/90 transition-colors"
                      calendlyUrl="https://calendly.com/francisco-rekover/30min?primary_color=e3af13"
                    >
                      <Calendar className="w-4 h-4 mr-2" /> Request Demo
                    </CalendlyButton>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSolution;