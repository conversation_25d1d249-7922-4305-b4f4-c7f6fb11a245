import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { motion } from "motion/react";
import { usePathname } from 'next/navigation';

const LoadingScreen = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [shouldShow, setShouldShow] = useState(false);
  const text = "REKOVER";
  const pathname = usePathname();

  useEffect(() => {
    // For testing - remove this comment when it's working properly
    setShouldShow(true);
    
    // Skip loading screen in development mode to allow HMR to work properly
    if (process.env.NODE_ENV === 'development') {
      // Comment out this line for testing in development
      // setShouldShow(false);
      // setIsLoading(false);
      // return;
    }
    
    // Check if navigation was triggered by header button click
    const isHeaderNavigated = sessionStorage.getItem('headerNavigated') === 'true';
    
    // If navigated through header buttons, don't show loading screen
    if (isHeaderNavigated) {
      sessionStorage.removeItem('headerNavigated');
      setShouldShow(false);
      return;
    }
    
    // Check if this is the first visit
    const isFirstVisit = !sessionStorage.getItem('hasVisited');
    
    // Check if this is a page refresh - using a more reliable method
    const isPageRefresh = 
      performance.getEntriesByType && 
      (performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming)?.type === 'reload';
    
    // Show loading screen only on first visit or page refresh
    if (isFirstVisit || isPageRefresh) {
      setShouldShow(true);
      
      // Set a timeout to hide loading after 2 seconds
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    } else {
      // Don't show loading screen
      // setShouldShow(false);  // Comment out for testing
      // setIsLoading(false);   // Comment out for testing
    }
    
    // Track that the user has visited
    sessionStorage.setItem('hasVisited', 'true');
    
    // For testing, add a timeout to hide the loading screen
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [pathname]);

  // Animation variants for text
  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  // If shouldShow is false, don't render the component at all
  if (!shouldShow) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-transform duration-1000 ${
        !isLoading ? 'translate-y-full' : ''
      }`}
      style={{
        background: `radial-gradient(circle at center, #1a2436 0%, #141b2b 100%)`
      }}
    >
      <div className="text-center relative">
        <div className="absolute inset-0 blur-3xl opacity-20" 
          style={{
            background: `radial-gradient(circle at center, #0be5a9 0%, transparent 70%)`
          }}
        />
        
        {/* Logo image with increased size */}
        <div className="relative h-36 w-36 mx-auto mb-6 z-10">
          <Image
            src="/landingpage/favicon.png"
            alt="Rekover Labs Logo"
            fill
            priority
            style={{ objectFit: 'contain' }}
            className="z-10"
          />
        </div>
        
        {/* Animated text with framer-motion */}
        <motion.div 
          className="flex items-center justify-center relative z-10"
          variants={container}
          initial="hidden"
          animate="visible"
        >
          {text.split('').map((letter, index) => (
            <motion.span
              key={index}
              variants={item}
              className="text-4xl font-bold inline-block"
              style={{
                color: index < 7 ? 'white' : '#0be5a9',
              }}
            >
              {letter}
            </motion.span>
          ))}
        </motion.div>
        
        <div className="mt-4 h-1 w-32 mx-auto bg-[#0be5a9]/20 rounded-full overflow-hidden relative z-10">
          <div className="h-full bg-[#0be5a9] animate-loading-bar" />
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;