import React from 'react';
import { Marque<PERSON> } from '@/components/ui/marquee';

const Banner = () => {
  const logos = [
    {
      src: "/landingpage/nvidia.png",
      alt: "NVIDIA"
    },
    {
      src: "/landingpage/openai.png",
      alt: "OpenAI"
    },
    {
      src: "/landingpage/github.png",
      alt: "GitHub"
    },
    {
      src: "/landingpage/aihub.png",
      alt: "AI Hub"
    }
  ];

  const LogoItem = ({ src, alt }: { src: string; alt: string }) => (
    <div className="mx-8 flex-shrink-0">
      <img
        src={src}
        alt={alt}
        className="h-10 sm:h-12 w-auto object-contain opacity-50 grayscale hover:opacity-100 hover:grayscale-0 transition-all duration-300"
      />
    </div>
  );

  return (
    <div className="relative bg-[#1a2436] py-6 overflow-hidden">
      {/* Top Line */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[120%] h-px bg-gradient-to-r from-transparent via-[#0be5a9]/20 to-transparent" />
      
      <div className="relative">
        <Marquee 
          pauseOnHover 
          className="[--duration:30s] [--gap:2rem]"
        >
          {logos.map((logo, index) => (
            <LogoItem key={index} {...logo} />
          ))}
        </Marquee>
      </div>

      {/* Bottom Line */}
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-[120%] h-px bg-gradient-to-r from-transparent via-[#0be5a9]/20 to-transparent" />
      
      {/* Fade effects */}
      <div className="pointer-events-none absolute inset-y-0 left-0 w-20 sm:w-40 bg-gradient-to-r from-[#1a2436] via-[#1a2436]/70 to-transparent z-10" />
      <div className="pointer-events-none absolute inset-y-0 right-0 w-20 sm:w-40 bg-gradient-to-l from-[#1a2436] via-[#1a2436]/70 to-transparent z-10" />
    </div>
  );
};

export default Banner;