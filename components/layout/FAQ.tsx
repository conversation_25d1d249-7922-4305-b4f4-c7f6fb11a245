import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

// Define categories
const categories = [
  "Integration & Setup",
  "Features & Capabilities",
  "Security & Compliance",
  "Support & Customization",
  "Implementation & Updates",
  "General"
];

const faqs: FAQItem[] = [
  {
    category: "Integration & Setup",
    question: "How does Rekover AI integrate with existing systems?",
    answer: "Rekover AI is designed for seamless integration with your existing insurance systems. We offer flexible APIs and SDKs that allow for easy connection to your current workflows. Our team also provides dedicated support to ensure a smooth integration process, minimizing disruption to your operations."
  },
  {
    category: "Features & Capabilities",
    question: "What industries is Rekover AI designed for?",
    answer: "While Rekover AI is primarily focused on the insurance industry, its powerful AI and machine learning capabilities can be adapted for various sectors. Our core strengths lie in claims processing, fraud detection, and risk assessment, making it particularly suitable for property & casualty, life, health, and specialty insurance providers."
  },
  {
    category: "Security & Compliance",
    question: "Is Rekover AI GDPR compliant?",
    answer: "Yes, Rekover AI is fully GDPR compliant. We take data privacy and security very seriously. Our systems are designed with privacy in mind, ensuring that all personal data is processed in accordance with GDPR regulations. We also provide tools and features to help our clients maintain their own GDPR compliance when using our platform."
  },
  {
    category: "Features & Capabilities",
    question: "Can Rekover AI handle multiple languages?",
    answer: "Rekover AI supports multiple languages, making it suitable for global insurance operations. Our natural language processing models can be trained on various languages to ensure accurate understanding and processing of claims and documents in different languages."
  },
  {
    category: "Security & Compliance",
    question: "How does Rekover AI ensure the security of sensitive insurance data?",
    answer: "Security is a top priority at Rekover AI. We employ industry-standard encryption protocols, regular security audits, and strict access controls. All data is stored in secure, compliant cloud environments. We also offer features like data anonymization and role-based access control to further enhance data protection."
  },
  {
    category: "Support & Customization",
    question: "What kind of support does Rekover AI offer?",
    answer: "We offer comprehensive support to all our clients. This includes 24/7 technical support, regular system updates, and dedicated account managers for enterprise clients. We also provide extensive documentation, video tutorials, and training sessions to help your team make the most of Rekover AI."
  },
  {
    category: "Support & Customization",
    question: "Can Rekover AI be customized for specific insurance products or processes?",
    answer: "Yes, Rekover AI is highly customizable. We understand that every insurance company has unique needs and processes. Our platform can be tailored to specific insurance products, claim types, or operational workflows. Our team works closely with clients to ensure the system meets their specific requirements."
  },
  {
    category: "Implementation & Updates",
    question: "How does Rekover AI handle updates and new feature releases?",
    answer: "We continuously improve Rekover AI with regular updates and new feature releases. These updates are typically rolled out automatically with no downtime. For significant changes, we provide advance notice and, if needed, optional transition periods. We also maintain detailed changelogs and provide guidance on leveraging new features."
  },
  {
    category: "Features & Capabilities",
    question: "What kind of AI models does Rekover AI use?",
    answer: "Rekover AI utilizes a variety of cutting-edge AI models, including deep learning networks, natural language processing models, and computer vision algorithms. These models are continuously trained on vast amounts of insurance-specific data, ensuring high accuracy in tasks like fraud detection, risk assessment, and claims processing."
  },
  {
    category: "Implementation & Updates",
    question: "How quickly can we see results after implementing Rekover AI?",
    answer: "The timeline for seeing results can vary depending on the scale of implementation and the specific use cases. However, many of our clients start seeing improvements in efficiency and accuracy within the first few weeks of implementation. Full realization of benefits, including significant cost savings and fraud reduction, typically becomes apparent within 3-6 months."
  },
  {
    category: "General",
    question: "Does Rekover AI offer a trial period?",
    answer: "Yes, we offer a 30-day trial period for eligible companies. This allows you to experience the power of Rekover AI firsthand and see how it can benefit your specific operations. During the trial, you'll have access to most of our features and dedicated support to help you evaluate the platform effectively."
  },
  {
    category: "Security & Compliance",
    question: "How does Rekover AI stay updated with changing insurance regulations?",
    answer: "Our team of insurance and legal experts constantly monitors changes in insurance regulations across various jurisdictions. We regularly update our AI models and rule sets to reflect these changes, ensuring that our system always operates in compliance with the latest regulations. We also provide timely updates and guidance to our clients regarding regulatory changes that may affect their operations."
  }
];

const FAQ = () => {
  const [openQuestionIndex, setOpenQuestionIndex] = useState<number | null>(null);
  // State to track open categories, initialized with the first three
  const [openCategories, setOpenCategories] = useState<string[]>(categories.slice(0, 3));

  // Function to toggle category visibility
  const toggleCategory = (categoryName: string) => {
    setOpenCategories(prevOpen => 
      prevOpen.includes(categoryName)
        ? prevOpen.filter(name => name !== categoryName)
        : [...prevOpen, categoryName]
    );
    // Close any open question when collapsing a category
    if (openCategories.includes(categoryName)) {
        setOpenQuestionIndex(null);
    }
  };

  return (
    <section className="bg-gradient-to-b from-gray-800 to-gray-900 py-24 relative">
      {/* Horizontal line at the top with fading on both ends */}
      <div className="h-px w-full bg-gradient-to-r from-transparent via-[#0be5a9]/30 to-transparent absolute top-0 left-0"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative">
        {/* Header Section */}
        <div className="mb-16">
          <span className="text-[#0be5a9] text-sm font-mono mb-2 block">★ FAQ</span>
          <h2 className="text-6xl font-bold text-white tracking-tight mb-4 uppercase">
            FREQUENTLY ASKED QUESTIONS
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl">
            Essential information about Rekover AI&apos;s capabilities.
          </p>
        </div>

        {/* FAQ Items - Grouped by Category */}
        <div className="space-y-4">
          {categories.map((category) => {
            const isCategoryOpen = openCategories.includes(category);
            return (
              <div key={category} className="border border-[#0be5a9]/10 rounded-lg overflow-hidden">
                {/* Make category header clickable */}
                <button
                  onClick={() => toggleCategory(category)}
                  className="w-full text-left bg-[#1a2436]/30 hover:bg-[#1a2436]/50 transition-colors"
                >
                  <div className="flex items-center justify-between py-4 px-6">
                      <h3 className="text-xl font-semibold text-[#0be5a9]">
                          {category}
                      </h3>
                      <Plus 
                          className={`w-5 h-5 text-[#0be5a9] transition-transform ${
                              isCategoryOpen ? 'rotate-45' : ''
                          }`}
                      />
                  </div>
                </button>
                
                {/* Collapsible content for questions */}
                <AnimatePresence>
                  {isCategoryOpen && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="py-2 px-2 space-y-1">
                        {faqs.map((faq, index) => (
                          faq.category === category && (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 0.2 }}
                              className="relative bg-[#141b2b]/40 rounded-md"
                            >
                              <button
                                onClick={() => setOpenQuestionIndex(openQuestionIndex === index ? null : index)}
                                className="w-full text-left"
                              >
                                <div className="flex items-center justify-between py-3 px-4 hover:bg-[#1a2436]/30 rounded-t-md transition-colors">
                                  <div className="flex items-center gap-3">
                                    <span className="text-[#0be5a9] font-mono text-base">•</span>
                                    <h4 className="text-base font-medium text-white">
                                      {faq.question}
                                    </h4>
                                  </div>
                                  <Plus 
                                    className={`w-4 h-4 text-[#0be5a9] transition-transform ${
                                      openQuestionIndex === index ? 'rotate-45' : ''
                                    }`}
                                  />
                                </div>
                                
                                <AnimatePresence>
                                  {openQuestionIndex === index && (
                                    <motion.div
                                      initial={{ height: 0, opacity: 0 }}
                                      animate={{ height: "auto", opacity: 1 }}
                                      exit={{ height: 0, opacity: 0 }}
                                      transition={{ duration: 0.2 }}
                                      className="overflow-hidden bg-[#141b2b]/20 rounded-b-md"
                                    >
                                      <div className="pl-10 pr-4 pb-3 pt-2">
                                        <p className="text-gray-400 text-sm leading-relaxed">
                                          {faq.answer}
                                        </p>
                                      </div>
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </button>
                            </motion.div>
                          )
                        ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            );
          })}
        </div>

        {/* Bottom Section */}
        <div className="mt-16 flex justify-end">
          <button className="bg-[#0be5a9]/10 text-[#0be5a9] px-6 py-2 rounded-sm border border-[#0be5a9]/20 font-mono text-sm hover:bg-[#0be5a9]/20 transition-colors">
            CONNECT WITH US
          </button>
        </div>
      </div>
    </section>
  );
};

export default FAQ;