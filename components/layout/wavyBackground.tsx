import { cn } from "@/lib/utils"
import React, { useEffect, useRef, useState } from "react";
import { createNoise3D } from "simplex-noise";

export const WavyBackground = ({
  children,
  className,
  containerClassName,
  colors,
  waveWidth,
  backgroundFill,
  blur = 10,
  speed = "fast",
  waveOpacity = 0.5,
  waveAmplitude = 40,
  waveFrequency = 1000,
  ...props
}: {
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  colors?: string[];
  waveWidth?: number;
  backgroundFill?: string;
  blur?: number;
  speed?: "slow" | "fast";
  waveOpacity?: number;
  waveAmplitude?: number;
  waveFrequency?: number;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const noise = createNoise3D();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isSafari, setIsSafari] = useState(false);
  
  const getSpeed = () => {
    switch (speed) {
      case "slow":
        return 0.001;
      case "fast":
        return 0.002;
      default:
        return 0.001;
    }
  };

  const waveColors = colors ?? [
    "#f1f5f9",
    "#e2e8f0",
    "#cbd5e1",
    "#94a3b8",
    "#e2e8f0",
  ];

  useEffect(() => {
    // Detect Safari browser
    setIsSafari(
      typeof window !== "undefined" &&
        navigator.userAgent.includes("Safari") &&
        !navigator.userAgent.includes("Chrome")
    );
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext("2d");
    if (!ctx) return;
    
    // Initialize canvas dimensions
    const updateDimensions = () => {
      if (!canvas || !ctx) return;
      const { innerWidth, innerHeight } = window;
      canvas.width = innerWidth;
      canvas.height = innerHeight;
      setDimensions({ width: innerWidth, height: innerHeight });
      
      // Apply blur directly to context if not Safari
      if (!isSafari) {
        ctx.filter = `blur(${blur}px)`;
      }
    };
    
    updateDimensions();
    window.addEventListener("resize", updateDimensions);

    let nt = 0;
    
    const drawWave = () => {
      if (!canvas || !ctx) return;
      const { width, height } = dimensions;
      
      // Clear canvas with background fill
      ctx.fillStyle = backgroundFill || "white";
      ctx.globalAlpha = waveOpacity || 0.5;
      ctx.fillRect(0, 0, width, height);
      
      // Increment noise time
      nt += getSpeed();
      
      // Draw each wave (increased to 8 for more layered effect)
      for (let i = 0; i < 8; i++) {
        ctx.beginPath();
        ctx.lineWidth = waveWidth || 30; // Smaller default width
        ctx.strokeStyle = waveColors[i % waveColors.length];
        
        // Use smaller step size (3) for smoother waves
        for (let x = 0; x < width; x += 3) {
          // Use waveFrequency and waveAmplitude to control size
          const y = noise(x / waveFrequency, 0.3 * i, nt) * waveAmplitude;
          ctx.lineTo(x, y + height * 0.5);
        }
        
        ctx.stroke();
        ctx.closePath();
      }
      
      // Continue animation loop
      animationRef.current = requestAnimationFrame(drawWave);
    };
    
    // Start animation
    drawWave();
    
    // Cleanup
    return () => {
      window.removeEventListener("resize", updateDimensions);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [blur, backgroundFill, waveColors, waveOpacity, waveWidth, isSafari, dimensions.width, dimensions.height, waveAmplitude, waveFrequency]);

  return (
    <div
      className={cn(
        "h-screen flex flex-col items-center justify-center",
        containerClassName
      )}
    >
      <canvas
        className="absolute inset-0 z-0"
        ref={canvasRef}
        id="canvas"
        style={{
          ...(isSafari ? { filter: `blur(${blur}px)` } : {}),
        }}
      />
      <div className={cn("relative z-10", className)} {...props}>
        {children}
      </div>
    </div>
  );
};