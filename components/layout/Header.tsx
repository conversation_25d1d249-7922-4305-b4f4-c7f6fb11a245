'use client'

import Image from "next/image"
import { useState, useEffect } from "react"
import { ShimmerButton } from "@/components/ui/shimmerButton"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"

export default function Header() {
  const navItems = ["Home", "Assistants", "People", "Careers", "Contact"]
  const [activeItem, setActiveItem] = useState(0)
  const [scrolled, setScrolled] = useState(false) // Add back scrolled state for floating navbar
  const router = useRouter()
  const pathname = usePathname()

  // Set active item based on current path
  useEffect(() => {
    if (pathname === '/') {
      setActiveItem(0); // Home
    } else if (pathname === '/assistants') {
      setActiveItem(1); // Assistants
    } else if (pathname === '/people') {
      setActiveItem(2); // People
    } else if (pathname === '/careers') {
      setActiveItem(3); // Careers
    } else if (pathname === '/contact') {
      setActiveItem(4); // Contact
    }
  }, [pathname]);

  // Add back scroll detection for floating navbar only
  useEffect(() => {
    const handleScroll = () => {
      // Get the hero section height to determine when to show floating navbar
      const heroSection = document.getElementById('hero')
      if (heroSection) {
        const heroHeight = heroSection.offsetHeight
        setScrolled(window.scrollY > heroHeight * 0.85) // Show floating navbar after scrolling past 85% of hero
      } else {
        // If hero doesn't exist (on other pages), show after scrolling down a bit
        setScrolled(window.scrollY > 300)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, []);

  const handleNavClick = (index: number, item: string) => {
    setActiveItem(index)

    // Set flag to indicate this is a header navigation (to prevent loading screen)
    sessionStorage.setItem('headerNavigated', 'true');

    // For Services, navigate to the services page
    if (item === "Assistants") {
      router.push("/services")
      return
    }

    // For Home, navigate to the homepage when on other pages
    if (item === "Home" && pathname !== '/') {
      router.push("/")
      return
    }

    // For other items, just navigate to their respective pages
    if (item === "Team") {
      router.push("/team")
      return
    }

    if (item === "People") {
      router.push("/people")
      return
    }

    if (item === "Careers") {
      router.push("/careers")
      return
    }

    if (item === "Contact") {
      router.push("/contact")
      return
    }
  }

  return (
    <>
      {/* Main header - positioned absolutely at the top, not changing on scroll */}
      <header className="absolute w-full top-0 z-40 bg-transparent flex justify-center">
        <div className="w-full max-w-7xl px-4 sm:px-6">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center cursor-pointer" onClick={() => {
              sessionStorage.setItem('headerNavigated', 'true');
              router.push("/");
            }}>
              <div className="h-16 w-48 relative">
                <Image
                  src="/landingpage/logoHeader.png"
                  alt="Rekover Labs Logo"
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </div>
            </div>

            <div className="hidden md:block">
              <div className="bg-[#141b2b]/90 backdrop-blur-md rounded-xl border border-[#0be5a9]/20 shadow-lg">
                <nav className="flex items-center px-6 py-3">
                  <Link href="/" className="text-gray-300 hover:text-white transition-colors mx-4">
                    Home
                  </Link>
                  <Link href="/services" className="text-gray-300 hover:text-white transition-colors mx-4">
                    Assistants
                  </Link>
                  <Link href="/people" className="text-gray-300 hover:text-white transition-colors mx-4">
                    People
                  </Link>
                  <Link href="/careers" className="text-gray-300 hover:text-white transition-colors mx-4">
                    Careers
                  </Link>
                  <Link href="/contact" className="text-gray-300 hover:text-white transition-colors mx-4">
                    Contact
                  </Link>
                </nav>
              </div>
            </div>

            <ShimmerButton
              shimmerColor="#0a0e16"
              background="#0fe7aa"
              className="rounded-full"
              onClick={() => {
                sessionStorage.setItem('headerNavigated', 'true');
                router.push("/login");
              }}
            >
              <span className="font-medium text-black">Login</span>
            </ShimmerButton>
          </div>
        </div>
      </header>

      {/* Floating navbar - only appears when scrolled past hero section */}
      {scrolled && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 animate-fade-in-up">
          <div className="bg-[#141b2b]/90 backdrop-blur-md rounded-full border border-[#0be5a9]/20 shadow-lg px-2">
            <nav className="flex items-center px-4 py-2">
              {navItems.map((item, index) => {
                const isActive = activeItem === index;
                return (
                  <button
                    key={index}
                    className="text-white transition-colors px-3 py-1 text-sm font-medium mx-1 relative"
                    onClick={() => handleNavClick(index, item)}
                  >
                    {item}
                    <span className={`absolute bottom-0 left-0 w-full h-0.5 ${isActive ? 'bg-[#0be5a9]' : 'bg-[#0be5a9]/30'} rounded-full`} />
                  </button>
                );
              })}

              {/* Minimalist login button */}
              <div className="border-l border-[#0be5a9]/30 mx-2 h-5"></div>
              <button
                onClick={() => {
                  sessionStorage.setItem('headerNavigated', 'true');
                  router.push("/login");
                }}
                className="text-[#0be5a9] hover:text-[#0be5a9]/80 transition-colors px-3 py-1 text-sm font-medium mx-1"
              >
                Login
              </button>
            </nav>
          </div>
        </div>
      )}
    </>
  )
}