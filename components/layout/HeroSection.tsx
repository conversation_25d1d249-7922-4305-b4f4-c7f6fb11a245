'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button"
import { CalendlyButton } from "@/components/ui/calendly-button"
import { AnimatedGridPattern } from '@/components/ui/animated-grid-pattern';
import { AnimatedBeamMultipleOutputDemo } from '@/components/ui/AnimatedBeamDemo';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from 'lucide-react';
import { motion } from 'framer-motion';

interface HeroSectionProps {
  loadingComplete?: boolean;
}

export default function HeroSection({ loadingComplete = false }: HeroSectionProps) {
  // State for the demo form
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [demoDate, setDemoDate] = useState('');
  const [demoTime, setDemoTime] = useState('');

  // Calculate animation delays based on loading state
  const getAnimationDelay = (baseDelay: number): string => {
    // If loading is complete, use the base delay
    // If loading is not complete, delay by an additional 3 seconds (or more if needed)
    const additionalDelay = loadingComplete ? 0 : 3;
    return `${baseDelay + additionalDelay}s`;
  };

  // Handle demo form submission
  const handleDemoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle demo booking logic here
    console.log({ name, email, company, demoDate, demoTime });
    // Reset form or close dialog, etc.
    setName('');
    setEmail('');
    setCompany('');
    setDemoDate('');
    setDemoTime('');
    // Consider adding logic to close the dialog after submission
  };

  return (
    <div className="relative min-h-screen bg-[#141b2b] overflow-hidden" id="hero">
      {/* Animated Grid Background */}
      <AnimatedGridPattern
        numSquares={50}
        maxOpacity={0.75}
        duration={3}
        repeatDelay={1}
        className="[mask-image:radial-gradient(1000px_1000px_at_center,white,transparent)] inset-x-0 inset-y-[-100%] h-[300%] skew-y-12"
      />

      {/* Bottom gradient overlay for smooth transition */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#141b2b] to-transparent z-[1]"></div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 flex items-center min-h-screen">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 w-full items-center">
          {/* Text Content - ~60% */}
          <div className="lg:col-span-7">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-5 opacity-0 animate-slideInLeft" style={{ animationDelay: getAnimationDelay(0.2), animationFillMode: 'forwards' }}>
              <span className="text-[#0be5a9] opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(0.7), animationFillMode: 'forwards' }}>Driving Change</span> Through AI Excellence in Insurance
            </h1>
            <p className="text-gray-300 text-lg mb-6 max-w-2xl leading-relaxed opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(1.0), animationFillMode: 'forwards' }}>
              We empower insurance companies with <strong>expertise</strong>, <strong>insights</strong>, and <strong>AI-powered solutions</strong> that transform operations. Our technology combines <strong>industry knowledge</strong> with <strong>cutting-edge AI</strong> to deliver measurable results through our <strong>SDK</strong> and <strong>API platform</strong>.
            </p>
            {/* Bullet Points with Animation */}
            <div className="space-y-4 mb-8 max-w-2xl">
              <div className="flex items-start space-x-3 opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(1.3), animationFillMode: 'forwards' }}>
                <span className="text-[#f69323] flex-shrink-0 mt-0.5 text-xl">✓</span>
                <span className="text-gray-200 text-lg">Industry-specific AI models trained by insurance experts</span>
              </div>
              <div className="flex items-start space-x-3 opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(1.5), animationFillMode: 'forwards' }}>
                <span className="text-[#f69323] flex-shrink-0 mt-0.5 text-xl">✓</span>
                <span className="text-gray-200 text-lg">Seamless integration through our powerful SDK and API</span>
              </div>
              <div className="flex items-start space-x-3 opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(1.7), animationFillMode: 'forwards' }}>
                <span className="text-[#f69323] flex-shrink-0 mt-0.5 text-xl">✓</span>
                <span className="text-gray-200 text-lg">Rich external data sources for unparalleled insights</span>
              </div>
            </div>

            <div className="flex space-x-4 opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(2.0), animationFillMode: 'forwards' }}>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <CalendlyButton
                  className="bg-[#f69323] text-white px-6 py-3 rounded-full font-medium hover:bg-[#f69323]/90 transition-colors"
                  calendlyUrl="https://calendly.com/francisco-rekover/30min?primary_color=e3af13"
                >
                  <Calendar className="w-4 h-4 mr-2" /> Book a Demo
                </CalendlyButton>
              </motion.div>
              <Link href="/login" passHref>
                <Button className="bg-transparent border-2 border-[#0be5a9] text-white px-6 py-3 rounded-full font-medium hover:bg-[#0be5a9]/10 transition-colors">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>

          {/* Animated Beam Demo */}
          <div className="lg:col-span-5 relative flex items-center justify-center opacity-0 animate-fadeIn" style={{ animationDelay: getAnimationDelay(1.2), animationFillMode: 'forwards' }}>
            <AnimatedBeamMultipleOutputDemo className="w-full h-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}