import { ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, useAnimation } from 'framer-motion';
import React, { useState, useEffect } from 'react';
import { RoadmapStage } from '@/components/roadmapStage';
import { VerticalRuler } from '@/components/roadmapRuler';

const stages = [
  {
    date: 'MAY 2018',
    period: 'Current Stage',
    title: 'Token Pre-Sale',
    description:
      'Token Pre-Sale - Blockchain platform development begins. Start of Token Pre-Sale Campaign. Supervision of Token Sale setup complete.',
    isCurrent: true,
    isCompleted: true,
  },
  {
    date: 'MAY - JUNE 2018',
    period: '6 Months',
    title: 'Development',
    description:
      'Identify and approach influencers within blockchain industry and open dialogue with them to learn more about Design Inc. and become involved with...',
    isCompleted: false,
  },
  {
    date: 'JUNE - OCTOBER 2018',
    period: '9 Months',
    title: 'Marketing',
    description:
      'Marketing continues to promote the several arms of Design Inc. globally, offices established on several different countries across different...',
  },
  {
    date: 'NOVEMBER 2018',
    period: '12 Months',
    title: 'Global Expansion',
    description:
      'Global expansion continues with new departments and offices opening...',
  },
];

export const Roadmap: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);
  const [activeStage, setActiveStage] = useState(0);
  const progressControls = useAnimation();
  const [isNavigating, setIsNavigating] = useState(false);
  const [showLeftFade, setShowLeftFade] = useState(false);
  // Add a ref to track if initial animation should be aborted
  const initialAnimationAbortRef = React.useRef(false);
  
  // Initial animation sequence - used only on first load
  const initialAnimation = async () => {
    try {
      // Start with width 0 (centered at the first dot position)
      await progressControls.start({ 
        width: '0%', 
        left: '0%',
        transition: { duration: 0.1 } 
      });
      
      if (initialAnimationAbortRef.current) return;
      
      // Animate to first dot
      await progressControls.start({ 
        width: '9%', 
        transition: { 
          duration: 2.5, 
          ease: "easeInOut" 
        } 
      });
      
      if (initialAnimationAbortRef.current) return;
      
      setAnimationComplete(true);
      setActiveStage(0);
      
      // Add delay before continuing animation
      await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(resolve, 1500);
        // Create cleanup function that will abort the timeout if needed
        const checkAbort = () => {
          if (initialAnimationAbortRef.current) {
            clearTimeout(timeoutId);
            reject(new Error('Animation aborted'));
          }
        };
        // Check periodically if we should abort
        const intervalId = setInterval(checkAbort, 100);
        setTimeout(() => clearInterval(intervalId), 1500);
      });
      
      if (initialAnimationAbortRef.current) return;
      
      // Reset width to previous endpoint and start position to first dot
      await progressControls.start({
        width: '0%',
        left: '9%',
        transition: { duration: 0.01 }
      });
      
      if (initialAnimationAbortRef.current) return;
      
      // Animate to second dot (starting from first dot)
      await progressControls.start({ 
        width: '26%', 
        transition: { 
          duration: 2.5, 
          ease: "easeInOut" 
        } 
      });
      
      if (initialAnimationAbortRef.current) return;
      
      setActiveStage(1);
      
      // Add short pause between stages
      await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(resolve, 1500);
        // Create cleanup function that will abort the timeout if needed
        const checkAbort = () => {
          if (initialAnimationAbortRef.current) {
            clearTimeout(timeoutId);
            reject(new Error('Animation aborted'));
          }
        };
        // Check periodically if we should abort
        const intervalId = setInterval(checkAbort, 100);
        setTimeout(() => clearInterval(intervalId), 1500);
      });
      
      if (initialAnimationAbortRef.current) return;
      
      // Reset width to previous endpoint and start position to second dot
      await progressControls.start({
        width: '0%',
        left: '35%',
        transition: { duration: 0.01 }
      });
      
      if (initialAnimationAbortRef.current) return;
      
      // Animate to third dot (starting from second dot)
      await progressControls.start({ 
        width: '28%', 
        transition: { 
          duration: 2.5, 
          ease: "easeInOut" 
        } 
      });
      
      if (initialAnimationAbortRef.current) return;
      
      setActiveStage(2);
    } catch {
      // This will catch the rejection from our abort logic
      console.log('Initial animation aborted');
    }
  };

  // Special animation for navigation - different from initial animation
  const navigateAnimation = async (index: number) => {
    // When navigating, we start with the penultimate dot active
    // Immediately set the penultimate dot (middle dot) as active
    setActiveStage(index + 1);
    
    // For different navigation positions, with progress bar stopping short
    // Start earlier and end slightly before the destination
    const positionMap = [
      { startLeft: '6%', endWidth: '24%' },   // First position: start earlier but end shorter
      { startLeft: '32%', endWidth: '26%' },  // Second position: start earlier but end shorter
      { startLeft: '60%', endWidth: '26%' }   // Third position: start earlier but end shorter
    ];
    
    // Start the progress bar at an earlier position before the penultimate dot
    await progressControls.start({
      width: '0%',
      left: positionMap[index].startLeft,
      transition: { duration: 0.01 }
    });
    
    // Animate to slightly before the last visible dot
    await progressControls.start({ 
      width: positionMap[index].endWidth, 
      transition: { 
        duration: 2.5, 
        ease: "easeInOut" 
      } 
    });
    
    // Update active stage to the last visible dot
    setActiveStage(index + 2);
  };

  const handlePrevious = () => {
    // Navigate to the previous slide
    const newIndex = Math.max(0, currentIndex - 1);
    setCurrentIndex(newIndex);
    
    // Immediately hide the progress line by setting its width to 0%
    progressControls.start({
      width: '0%',
      transition: { duration: 0.1 }
    });
    
    // If we're going back to the first position, hide the left fade
    if (newIndex === 0) {
      setShowLeftFade(false);
    }
    
    // We still want to maintain which dots are red (completed)
    // So we don't change the activeStage value
    // This keeps the coloring of the dots consistent
  };

  const handleNext = () => {
    const newIndex = Math.min(stages.length - 3, currentIndex + 1);
    setCurrentIndex(newIndex);
    
    // Mark that we should abort the initial animation if it's running
    initialAnimationAbortRef.current = true;
    
    // Set isNavigating to true to prevent initialAnimation from restarting
    setIsNavigating(true);
    
    // Show the left fade effect when navigating to the right
    setShowLeftFade(true);
    
    // Reset the progress bar immediately before starting navigation animation
    progressControls.start({
      width: '0%',
      left: '0%',
      transition: { duration: 0.01 }
    }).then(() => {
      navigateAnimation(newIndex);
    });
  };

  // Start the animation when component mounts, but only if not navigating
  useEffect(() => {
    if (!isNavigating) {
      initialAnimation();
    }
  }, []);

  return (
    <div className="relative overflow-hidden bg-transparent">
      {/* Vertical Ruler positioned at far left */}
      <div className="absolute left-0 top-0 h-full bg-transparent">
        <VerticalRuler />
      </div>

      <div className="container mx-auto px-4 pt-24 pb-16 bg-transparent">
        <div className="relative ml-24 bg-transparent">
      
          {/* Stages container with overflow */}
          <div className="relative overflow-hidden bg-transparent pb-16">
            {/* Timeline container with controlled width and fade effects */}
            <div className="relative w-full max-w-[1200px] mx-auto">
              {/* Position the line BEHIND everything with lower z-index */}
              <div className="absolute top-[48px] left-0 right-0 h-[2px] bg-gray-600/30 z-[5] overflow-hidden">
                <motion.div 
                  className="absolute top-0 h-full bg-[#ff4d6d] z-[10]"
                  initial={{ width: '0%', left: '0%' }}
                  animate={progressControls}
                />
              </div>
              
              {/* NEW: Left-side fade - only visible after first next navigation */}
              {showLeftFade && (
                <div className="absolute left-0 top-0 bottom-0 w-[20%] pointer-events-none z-[60]" 
                     style={{
                       background: 'linear-gradient(to left, #111827 0%, #111827 100%)',
                       maskImage: 'linear-gradient(to left, transparent, black)',
                       WebkitMaskImage: 'linear-gradient(to left, transparent, black)'
                     }}
                />
              )}
              
              {/* Timeline fade overlays to match stage fading */}
              <div className="absolute inset-0 pointer-events-none z-[15]">
                {/* Minimal left fade, almost invisible */}
                <div className="absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-gray-900 to-transparent" />
              </div>
              
              {/* Stages - higher z-index than the line */}
              <div 
                className="flex gap-24 transition-transform duration-500 bg-transparent relative z-[20]"
                style={{ transform: `translateX(-${currentIndex * 360}px)` }}
              >
                {stages.map((stage, index) => (
                  <RoadmapStage 
                    key={index} 
                    stage={{
                      ...stage,
                      isAnimated: index === 0 && !animationComplete && !isNavigating,
                      isCurrent: (animationComplete || isNavigating) && index === activeStage,
                      isCompleted: (animationComplete || isNavigating) && index < activeStage
                    }} 
                  />
                ))}
              </div>
              
              {/* ENHANCED: Strong right-side fade using Tailwind classes */}
              <div className="absolute right-0 top-0 bottom-0 w-[40%] pointer-events-none z-[60]" 
                   style={{
                     background: 'linear-gradient(to right, #111827 0%, #111827 100%)',
                     maskImage: 'linear-gradient(to right, transparent, black)',
                     WebkitMaskImage: 'linear-gradient(to right, transparent, black)'
                   }}
              />
            </div>

            {/* Navigation buttons - styled for better visibility */}
            <div className="absolute bottom-2 right-4 flex gap-4 z-[70]">
              <button
                onClick={handlePrevious}
                disabled={currentIndex === 0}
                className="p-3 rounded-full bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors border border-gray-700/50 shadow-md"
              >
                <ChevronLeft size={20} />
              </button>
              <button
                onClick={handleNext}
                disabled={currentIndex >= stages.length - 3}
                className="p-3 rounded-full bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors border border-gray-700/50 shadow-md"
              >
                <ChevronRight size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};