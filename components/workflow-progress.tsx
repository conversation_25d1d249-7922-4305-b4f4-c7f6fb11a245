"use client"

import { useEffect, useState } from "react"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { CheckCircle2, Loader2, XCircle } from 'lucide-react'

interface Task {
  name: string
  description: string
  status: "pending" | "running" | "completed" | "failed"
  progress: number
}

interface WorkflowProgressProps {
  tasks: Task[]
  onComplete: (success: boolean) => void
}

export function WorkflowProgress({ tasks, onComplete }: WorkflowProgressProps) {
  const [currentTasks, setCurrentTasks] = useState<Task[]>(tasks)
  const [overallProgress, setOverallProgress] = useState(0)

  useEffect(() => {
    let currentTaskIndex = 0

    const runTask = async () => {
      if (currentTaskIndex >= tasks.length) {
        onComplete(true)
        return
      }

      setCurrentTasks(prev => prev.map((task, index) => ({
        ...task,
        status: index === currentTaskIndex ? "running" : task.status
      })))

      // Simulate task progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 500))
        setCurrentTasks(prev => prev.map((task, index) => ({
          ...task,
          progress: index === currentTaskIndex ? progress : task.progress
        })))
      }

      // Randomly simulate task failure (10% chance)
      const taskSucceeded = Math.random() > 0.1

      setCurrentTasks(prev => prev.map((task, index) => ({
        ...task,
        status: index === currentTaskIndex ? (taskSucceeded ? "completed" : "failed") : task.status
      })))

      if (!taskSucceeded) {
        onComplete(false)
        return
      }

      currentTaskIndex++
      setOverallProgress((currentTaskIndex / tasks.length) * 100)

      // Schedule next task
      setTimeout(runTask, 500)
    }

    runTask()
  }, [tasks, onComplete])

  const getStatusIcon = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />
      case "running":
        return <Loader2 className="h-4 w-4 animate-spin" />
      default:
        return null
    }
  }

  const getStatusBadge = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return <Badge variant="default">Completed</Badge>
      case "failed":
        return <Badge variant="destructive">Failed</Badge>
      case "running":
        return <Badge variant="secondary">Running</Badge>
      default:
        return <Badge variant="outline">Pending</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Overall Progress</span>
          <span>{Math.round(overallProgress)}%</span>
        </div>
        <Progress value={overallProgress} className="h-2" />
      </div>

      <div className="space-y-4">
        {currentTasks.map((task, index) => (
          <div key={index} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(task.status)}
                <span className="font-medium">{task.name}</span>
              </div>
              {getStatusBadge(task.status)}
            </div>
            <Progress value={task.progress} className="h-2" />
            <p className="text-sm text-muted-foreground">{task.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

