"use client"

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  <PERSON>alogFooter,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Clock, BarChart, Activity, Zap, Database, Brain, PlayCircle, Edit } from 'lucide-react'
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { FileUploader } from "@/components/file-uploader"

interface TaskTemplate {
  id: string
  name: string
  description: string
  category: string
  type: string
  estimatedTime: string
  inputRequired: string[]
  outputProvided: string[]
  accuracy: number
  usageCount: number
  aiModels: string[]
  integrations: string[]
}

interface TaskTemplateCardProps {
  task: TaskTemplate
  onClick: () => void
  onEdit: (editedTask: TaskTemplate) => void
}

type TaskExecutionState = "idle" | "running" | "completed" | "failed"

export function TaskTemplateCard({ task, onClick, onEdit }: TaskTemplateCardProps) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isExecutionOpen, setIsExecutionOpen] = useState(false)
  const [executionState, setExecutionState] = useState<TaskExecutionState>("idle")
  const [progress, setProgress] = useState(0)
  const [executionResult, setExecutionResult] = useState<any>(null)
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isEditing, setIsEditing] = useState(false)
  const [editedTask, setEditedTask] = useState(task)

  const getTypeColor = (type: string) => {
    switch (type) {
      case "automated": return "bg-blue-500"
      case "hybrid": return "bg-purple-500"
      case "manual": return "bg-orange-500"
      default: return "bg-gray-500"
    }
  }

  const handleStartTask = () => {
    setIsExecutionOpen(true)
    setExecutionState("idle")
    setProgress(0)
    setExecutionResult(null)
    setUploadedFiles([])
  }

  const handleExecuteTask = async () => {
    setExecutionState("running")
    setProgress(0)

    // Simulate task execution
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 500))
      setProgress(i)
    }

    // Simulate success/failure (90% success rate)
    const success = Math.random() < 0.9
    setExecutionState(success ? "completed" : "failed")

    if (success) {
      setExecutionResult({
        status: "success",
        outputs: task.outputProvided.map(output => ({
          name: output,
          value: `Sample ${output.toLowerCase()} data`
        })),
        executionTime: `${(Math.random() * 2 + 1).toFixed(1)} seconds`,
        confidence: Math.round(Math.random() * 20 + 80)
      })
    } else {
      setExecutionResult({
        status: "error",
        message: "Task execution failed. Please try again.",
        error: "Internal processing error"
      })
    }
  }

  const handleCloseExecution = () => {
    setIsExecutionOpen(false)
    setExecutionState("idle")
    setProgress(0)
    setExecutionResult(null)
    setUploadedFiles([])
  }

  const handleEdit = (e: React.FormEvent) => {
    e.preventDefault()
    onEdit(editedTask)
    setIsEditing(false)
  }

  return (
    <>
      <Card className="cursor-pointer transition-all duration-300 hover:shadow-lg" onClick={onClick}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {task.name}
          </CardTitle>
          <Dialog open={isEditing} onOpenChange={setIsEditing}>
            <DialogTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm">
                <Edit className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Edit Task Template</DialogTitle>
                <DialogDescription>
                  Make changes to the task template. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleEdit}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">
                      Name
                    </Label>
                    <Input
                      id="name"
                      value={editedTask.name}
                      onChange={(e) => setEditedTask({ ...editedTask, name: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="description" className="text-right">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      value={editedTask.description}
                      onChange={(e) => setEditedTask({ ...editedTask, description: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="estimatedTime" className="text-right">
                      Est. Time
                    </Label>
                    <Input
                      id="estimatedTime"
                      value={editedTask.estimatedTime}
                      onChange={(e) => setEditedTask({ ...editedTask, estimatedTime: e.target.value })}
                      className="col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit">Save changes</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent className="p-6">
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-1">{task.name}</h3>
            <p className="text-sm text-muted-foreground line-clamp-2">{task.description}</p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between text-sm">
              <Badge variant="secondary">{task.category}</Badge>
              <Badge variant="outline" className="capitalize">{task.type}</Badge>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                {task.estimatedTime}
              </div>
              <div className="flex items-center">
                <Activity className="h-4 w-4 mr-2 text-muted-foreground" />
                {task.accuracy}% accuracy
              </div>
            </div>

            <Separator />

            <div className="flex justify-between items-center">
              <Button
                variant="default"
                size="sm"
                onClick={handleStartTask}
                className="flex items-center"
              >
                <PlayCircle className="h-4 w-4 mr-2" />
                Start Task
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDetailsOpen(true)}
              >
                View Details
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{task.name}</DialogTitle>
            <DialogDescription>{task.description}</DialogDescription>
          </DialogHeader>

          <div className="grid gap-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2 flex items-center">
                  <Database className="h-4 w-4 mr-2" />
                  Required Input
                </h4>
                <ul className="list-disc list-inside space-y-1">
                  {task.inputRequired.map((input, index) => (
                    <li key={index} className="text-sm">{input}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Provided Output
                </h4>
                <ul className="list-disc list-inside space-y-1">
                  {task.outputProvided.map((output, index) => (
                    <li key={index} className="text-sm">{output}</li>
                  ))}
                </ul>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-semibold mb-2 flex items-center">
                <Brain className="h-4 w-4 mr-2" />
                AI Models Used
              </h4>
              <div className="flex flex-wrap gap-2">
                {task.aiModels.map((model, index) => (
                  <Badge key={index} variant="secondary">
                    {model}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2 flex items-center">
                <BarChart className="h-4 w-4 mr-2" />
                System Integrations
              </h4>
              <div className="flex flex-wrap gap-2">
                {task.integrations.map((integration, index) => (
                  <Badge key={index} variant="outline">
                    {integration}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 bg-muted p-4 rounded-lg">
              <div>
                <div className="text-sm text-muted-foreground">Estimated Time</div>
                <div className="font-semibold">{task.estimatedTime}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Accuracy</div>
                <div className="font-semibold">{task.accuracy}%</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Usage Count</div>
                <div className="font-semibold">{task.usageCount}</div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Execution Dialog */}
      <Dialog open={isExecutionOpen} onOpenChange={handleCloseExecution}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Execute Task: {task.name}</DialogTitle>
            <DialogDescription>
              {executionState === "idle"
                ? "Provide the required input to execute this task"
                : executionState === "running"
                ? "Task is being executed..."
                : executionState === "completed"
                ? "Task completed successfully"
                : "Task execution failed"}
            </DialogDescription>
          </DialogHeader>

          {executionState === "idle" && (
            <div className="space-y-4">
              {task.inputRequired.map((input, index) => (
                <div key={index} className="space-y-2">
                  <Label>{input}</Label>
                  {input.toLowerCase().includes('file') || input.toLowerCase().includes('image') ? (
                    <FileUploader
                      id={`input-${index}`}
                      accept="*/*"
                      multiple
                      onFilesSelected={setUploadedFiles}
                    />
                  ) : (
                    <Input placeholder={`Enter ${input.toLowerCase()}`} />
                  )}
                </div>
              ))}
            </div>
          )}

          {executionState === "running" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Execution Progress</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} />
              </div>
              <p className="text-sm text-muted-foreground">
                Processing {task.name.toLowerCase()}...
              </p>
            </div>
          )}

          {executionState === "completed" && executionResult && (
            <div className="space-y-4">
              <Alert>
                <AlertDescription>
                  Task completed in {executionResult.executionTime} with {executionResult.confidence}% confidence
                </AlertDescription>
              </Alert>
              <div className="space-y-2">
                <h4 className="font-semibold">Output:</h4>
                {executionResult.outputs.map((output: any, index: number) => (
                  <div key={index} className="bg-muted p-2 rounded">
                    <span className="font-medium">{output.name}:</span> {output.value}
                  </div>
                ))}
              </div>
            </div>
          )}

          {executionState === "failed" && executionResult && (
            <Alert variant="destructive">
              <AlertDescription>
                {executionResult.message}
                <br />
                <span className="text-sm opacity-70">Error: {executionResult.error}</span>
              </AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            {executionState === "idle" && (
              <Button onClick={handleExecuteTask}>
                Execute Task
              </Button>
            )}
            {(executionState === "completed" || executionState === "failed") && (
              <Button onClick={handleCloseExecution}>
                Close
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

