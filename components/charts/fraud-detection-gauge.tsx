"use client"

import GaugeChart from 'react-gauge-chart'

export default function FraudDetectionGauge() {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <GaugeChart
        id="fraud-detection-gauge"
        nrOfLevels={3}
        colors={["#FF5F6D", "#FFC371", "#2ECC71"]}
        arcWidth={0.3}
        percent={0.92}
        textColor="#333333"
        needleColor="#464A4F"
        needleBaseColor="#464A4F"
        animate={true}
        formatTextValue={(value) => value + "%"}
        cornerRadius={0}
        marginInPercent={0.05}
      />
    </div>
  )
}

