"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { processingTime: "0-1 day", count: 120 },
  { processingTime: "1-2 days", count: 200 },
  { processingTime: "2-3 days", count: 300 },
  { processingTime: "3-4 days", count: 250 },
  { processingTime: "4-5 days", count: 180 },
  { processingTime: "5+ days", count: 100 },
]

export default function ClaimProcessingTimeChart() {
  return (
    <ChartContainer
      config={{
        count: {
          label: "Claims",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="processingTime" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="count" fill="var(--color-count)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

