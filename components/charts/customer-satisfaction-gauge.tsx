"use client"

import GaugeChart from 'react-gauge-chart'

export default function CustomerSatisfactionGauge() {
  return (
    <div className="w-full h-full flex items-center justify-center">
      <GaugeChart
        id="customer-satisfaction-gauge"
        nrOfLevels={5}
        colors={["#FF5F6D", "#FFC371", "#2ECC71"]}
        arcWidth={0.3}
        percent={0.88}
        textColor="#333333"
        needleColor="#464A4F"
        needleBaseColor="#464A4F"
        animate={true}
        formatTextValue={(value) => (Number(value) * 5).toFixed(1) + " / 5"}
        cornerRadius={0}
        marginInPercent={0.05}
      />
    </div>
  )
}

