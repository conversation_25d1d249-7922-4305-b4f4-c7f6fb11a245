"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

interface AcceptanceRatioGaugeProps {
  ratio: number
}

export default function AcceptanceRatioGauge({ ratio }: AcceptanceRatioGaugeProps) {
  const percentage = ratio * 100
  const strokeWidth = 10
  const radius = 80
  const circumference = 2 * Math.PI * radius
  const dashArray = circumference
  const dashOffset = circumference * (1 - ratio)
  const angleOffset = -90
  const angle = 180 * ratio + angleOffset

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-sm font-medium">Acceptance Ratio</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center">
          <svg width="200" height="120" viewBox="0 0 200 120">
            <path
              d="M20 100 A80 80 0 0 1 180 100"
              fill="none"
              stroke="#e2e8f0"
              strokeWidth={strokeWidth}
            />
            <path
              d="M20 100 A80 80 0 0 1 180 100"
              fill="none"
              stroke="#3b82f6"
              strokeWidth={strokeWidth}
              strokeDasharray={dashArray}
              strokeDashoffset={dashOffset}
              strokeLinecap="round"
            />
            <text x="100" y="112" textAnchor="middle" fontSize="24" fontWeight="bold">
              {percentage.toFixed(1)}%
            </text>
            <line
              x1="100"
              y1="100"
              x2={100 + 70 * Math.cos((angle * Math.PI) / 180)}
              y2={100 + 70 * Math.sin((angle * Math.PI) / 180)}
              stroke="#1e293b"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
          <div className="flex justify-between w-full mt-2 text-sm text-muted-foreground">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

