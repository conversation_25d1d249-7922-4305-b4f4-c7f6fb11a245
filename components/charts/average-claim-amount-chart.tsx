"use client"

import { Area, AreaChart, ResponsiveContainer, <PERSON>Axi<PERSON>, <PERSON><PERSON>xi<PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", amount: 5000 },
  { month: "Feb", amount: 5500 },
  { month: "Mar", amount: 6000 },
  { month: "Apr", amount: 5800 },
  { month: "May", amount: 6200 },
  { month: "Jun", amount: 6500 },
]

export default function AverageClaimAmountChart() {
  return (
    <ChartContainer
      config={{
        amount: {
          label: "Average Amount",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="month" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Area type="monotone" dataKey="amount" stroke="var(--color-amount)" fill="var(--color-amount)" fillOpacity={0.2} />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

