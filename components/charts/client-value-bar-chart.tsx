"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { segment: "Low", count: 400 },
  { segment: "Medium", count: 300 },
  { segment: "High", count: 200 },
  { segment: "VIP", count: 100 },
]

export default function ClientValueBarChart() {
  return (
    <ChartContainer
      config={{
        count: {
          label: "Clients",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="segment" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="count" fill="var(--color-count)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

