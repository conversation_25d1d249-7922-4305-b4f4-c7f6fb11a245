"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { cause: "Human Error", amount: 400000 },
  { cause: "System Glitch", amount: 300000 },
  { cause: "Policy Misinterpretation", amount: 200000 },
  { cause: "Incomplete Information", amount: 150000 },
  { cause: "Other", amount: 100000 },
]

export default function LeakagesByCauseChart() {
  return (
    <ChartContainer
      config={{
        amount: {
          label: "Amount ($)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="cause" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="amount" fill="var(--color-amount)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

