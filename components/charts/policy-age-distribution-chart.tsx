"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { age: "0-1", count: 5000 },
  { age: "1-2", count: 7000 },
  { age: "2-3", count: 6000 },
  { age: "3-4", count: 4000 },
  { age: "4-5", count: 2000 },
  { age: "5+", count: 565 },
]

export function PolicyAgeDistributionChart() {
  return (
    <ChartContainer
      config={{
        count: {
          label: "Policies",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="age" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="count" fill="var(--color-count)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

