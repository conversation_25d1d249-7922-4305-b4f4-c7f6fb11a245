"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, ReferenceArea } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { useMemo } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface PriceVsMileagePlotProps {
  data: Array<{
    Source_Url?: string
    Milage_Value: number
    Price: number
  }>
  showReferenceLines?: boolean
  userKmMin?: number  // User's minimum KM input
  userKmMax?: number  // User's maximum KM input
  showRegressionLine?: boolean  // New prop for consistency with year plot
  highlightedVehicles?: string[] // Array of Source_Url for vehicles to highlight
}

export function PriceVsMileagePlot({ 
  data, 
  showReferenceLines = true,
  userKmMin,
  userKmMax,
  showRegressionLine = true, // Default to true
  highlightedVehicles = [] // Default to empty array
}: PriceVsMileagePlotProps) {
  // Determine if data is in a loading or incomplete state
  const isDataReady = useMemo(() => {
    if (!data || data.length === 0) return false;
    return data.length >= 2;
  }, [data]);
  
  // Group data by highlight status - this replaces the chartData useMemo
  const { regularData, highlightedData } = useMemo(() => {
    const filtered = (data || []).filter((item) => 
      item.Milage_Value > 0 && item.Price > 0
    );
    
    const regular: { x: number; y: number }[] = [];
    const highlighted: { x: number; y: number }[] = [];
    
    filtered.forEach(item => {
      const point = { x: item.Milage_Value, y: item.Price };
      
      if (item.Source_Url && highlightedVehicles.includes(item.Source_Url)) {
        highlighted.push(point);
      } else {
        regular.push(point);
      }
    });
    
    return { 
      regularData: regular, 
      highlightedData: highlighted 
    };
  }, [data, highlightedVehicles]);
  
  // Use all data for calculations (both regular and highlighted)
  const chartData = useMemo(() => {
    return [...regularData, ...highlightedData];
  }, [regularData, highlightedData]);
  
  // Calculate linear regression coefficients
  const regression = useMemo(() => {
    if (chartData.length < 2) return null;
    
    const n = chartData.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    
    for (const point of chartData) {
      sumX += point.x;
      sumY += point.y;
      sumXY += point.x * point.y;
      sumXX += point.x * point.x;
    }
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    return { slope, intercept };
  }, [chartData]);
  
  // Calculate statistics for data-driven insights
  const stats = useMemo(() => {
    if (!chartData.length) return null;
    
    const prices = chartData.map(item => item.y);
    const mileages = chartData.map(item => item.x);
    
    return {
      maxPrice: Math.max(...prices),
      minPrice: Math.min(...prices),
      avgPrice: Math.round(prices.reduce((sum, val) => sum + val, 0) / prices.length),
      maxMileage: Math.max(...mileages),
      minMileage: Math.min(...mileages)
    };
  }, [chartData]);

  // Calculate domain values with some padding
  const xDomain = useMemo(() => {
    if (!stats) return [0, 160000];
    return [0, Math.ceil(stats.maxMileage * 1.1 / 10000) * 10000];
  }, [stats]);
  
  const yDomain = useMemo(() => {
    if (!stats) return [0, 35000];
    const minY = Math.max(0, Math.floor(stats.minPrice * 0.9 / 7000) * 7000);
    const maxY = Math.ceil(stats.maxPrice * 1.1 / 7000) * 7000;
    return [minY, maxY];
  }, [stats]);

  // Check if reference lines are too close to each other
  const minMaxTooClose = useMemo(() => {
    if (!userKmMin || !userKmMax) return false;
    // If the lines are within 15% of the chart width of each other, consider them "too close"
    const chartWidth = xDomain[1] - xDomain[0];
    return (userKmMax - userKmMin) < (chartWidth * 0.15);
  }, [userKmMin, userKmMax, xDomain]);

  // Calculate regression line endpoints
  const getYForX = (x: number) => {
    if (!regression) return null;
    return regression.slope * x + regression.intercept;
  };

  const minX = 0; // Mileage starts at 0
  const maxX = xDomain[1];
  const minY = getYForX(minX);
  const maxY = getYForX(maxX);

  // If data is not ready, show skeleton UI
  if (!isDataReady) {
    return (
      <div className="h-[300px] w-full flex flex-col space-y-2">
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>
        <Skeleton className="h-[260px] w-full" />
      </div>
    );
  }

  return (
    <ChartContainer
      config={{
        price: {
          label: "Price (EUR)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart margin={{ top: 20, right: 40, bottom: 20, left: 20 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="Mileage" 
            unit=" km" 
            domain={xDomain} 
            tickFormatter={value => `${Math.round(value / 1000)}k km`}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Price" 
            unit=" EUR" 
            domain={yDomain}
            tickFormatter={value => `${value.toLocaleString()}`} 
          />
          
          {/* Add subtle background for the selected km range */}
          {showReferenceLines && userKmMin && userKmMax && (
            <ReferenceArea 
              x1={userKmMin} 
              x2={userKmMax} 
              y1={yDomain[0]} 
              y2={yDomain[1]}
              fill="#a19f9fa7" 
              fillOpacity={0.5}
              stroke="none"
            />
          )}
          
          <ChartTooltip 
            content={<ChartTooltipContent />} 
            cursor={{ strokeDasharray: '3 3' }}
          />
          
          {/* Regression trend line */}
          {showRegressionLine && regression && (
            <ReferenceLine
              segment={[
                { x: minX, y: minY || 0 },
                { x: maxX, y: maxY || 0 }
              ]}
              stroke="#0060df"
              strokeWidth={2.5}
              ifOverflow="visible"
              strokeOpacity={0.8}
            />
          )}
          
          {/* Regular vehicles in blue */}
          <Scatter 
            name="Vehicles" 
            data={regularData} 
            fill="var(--color-price)" 
            opacity={0.8}
          />
          
          {/* Highlighted vehicles in green */}
          {highlightedData.length > 0 && (
            <Scatter 
              name="Selected Vehicles" 
              data={highlightedData} 
              fill="#22c55e" 
              opacity={0.9}
              strokeWidth={1.5}
              stroke="#15803d"
            />
          )}
          
          {/* User's minimum KM reference line */}
          {showReferenceLines && userKmMin && userKmMin > 0 && (
            <ReferenceLine 
              x={userKmMin} 
              stroke="#4CAF50" 
              strokeDasharray="3 3" 
              label={{ 
                value: `Min ${Math.round(userKmMin / 1000)}k km`, 
                position: minMaxTooClose ? 'bottom' : 'top', // Position at bottom if too close
                fill: '#4CAF50',
                fontSize: 11,
                offset: 10
              }} 
            />
          )}
          
          {/* User's maximum KM reference line */}
          {showReferenceLines && userKmMax && userKmMax > 0 && (
            <ReferenceLine 
              x={userKmMax} 
              stroke="#F44336" 
              strokeDasharray="3 3" 
              label={{ 
                value: `Max ${Math.round(userKmMax / 1000)}k km`, 
                position: 'top',
                fill: '#F44336',
                fontSize: 11,
                offset: 10
              }} 
            />
          )}
          
          {/* Average price reference line */}
          {showReferenceLines && stats && (
            <ReferenceLine 
              y={stats.avgPrice} 
              stroke="#888" 
              strokeDasharray="3 3" 
              label={{ 
                value: "Avg", 
                position: 'right',
                fill: '#888',
                fontSize: 11,
                offset: 5
              }} 
            />
          )}
        </ScatterChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

