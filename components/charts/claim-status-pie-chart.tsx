"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { <PERSON><PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { status: "Approved", value: 400 },
  { status: "Pending", value: 300 },
  { status: "Rejected", value: 200 },
  { status: "Under Review", value: 100 },
]

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']

export default function ClaimStatusPieChart() {
  return (
    <ChartContainer
      config={{
        value: {
          label: "Claims",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <ChartTooltip content={<ChartTooltipContent />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

