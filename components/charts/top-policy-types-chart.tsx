"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { type: "Auto - Comprehensive", value: 2500000 },
  { type: "Home - Homeowners", value: 2000000 },
  { type: "Life - Term", value: 1500000 },
  { type: "Auto - Liability", value: 1200000 },
  { type: "Health - Family", value: 1000000 },
]

export function TopPolicyTypesChart() {
  return (
    <ChartContainer
      config={{
        value: {
          label: "Value ($)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} layout="vertical" margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis type="number" />
          <YAxis dataKey="type" type="category" width={150} />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="value" fill="var(--color-value)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

