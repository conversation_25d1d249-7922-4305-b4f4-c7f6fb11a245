"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { type: "Auto", premium: 4500000 },
  { type: "Home", premium: 3800000 },
  { type: "Life", premium: 2500000 },
  { type: "Health", premium: 1500000 },
]

export function PremiumDistributionChart() {
  return (
    <ChartContainer
      config={{
        premium: {
          label: "Premium ($)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="type" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="premium" fill="var(--color-premium)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

