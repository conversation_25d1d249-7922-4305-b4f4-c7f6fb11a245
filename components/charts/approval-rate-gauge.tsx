"use client"

import GaugeChart from 'react-gauge-chart'
import { ComponentProps, FC } from 'react'

type GaugeChartProps = ComponentProps<any>

export default function ApprovalRateGauge() {
  const chartProps: GaugeChartProps = {
    id: "approval-rate-gauge",
    nrOfLevels: 3,
    colors: ["#FF5F6D", "#FFC371", "#2ECC71"],
    arcWidth: 0.3,
    percent: 0.785,
    textColor: "#333333",
    needleColor: "#464A4F",
    needleBaseColor: "#464A4F",
    animate: true,
    formatTextValue: ({ value }: { value: number }) => value + "%",
    cornerRadius: 0,
    marginInPercent: 0.05,
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <GaugeChart {...chartProps} />
    </div>
  )
}
