"use client"

import { <PERSON>, <PERSON><PERSON>hart, ResponsiveContainer, <PERSON>lt<PERSON>, XAxis, <PERSON>A<PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", leakages: 120000 },
  { month: "Feb", leakages: 150000 },
  { month: "Mar", leakages: 180000 },
  { month: "Apr", leakages: 220000 },
  { month: "May", leakages: 190000 },
  { month: "Jun", leakages: 230000 },
]

export default function LeakagesTrendChart() {
  return (
    <ChartContainer
      config={{
        leakages: {
          label: "Leakages ($)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="month" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="leakages" stroke="var(--color-leakages)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

