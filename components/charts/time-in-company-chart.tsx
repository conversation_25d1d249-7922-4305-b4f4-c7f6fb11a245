"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { years: "0-1", clients: 200 },
  { years: "1-3", clients: 350 },
  { years: "3-5", clients: 280 },
  { years: "5-10", clients: 220 },
  { years: "10+", clients: 150 },
]

export default function TimeInCompanyChart() {
  return (
    <ChartContainer
      config={{
        clients: {
          label: "Clients",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="years" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="clients" stroke="var(--color-clients)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

