"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", policies: 20000 },
  { month: "Feb", policies: 21000 },
  { month: "Mar", policies: 22000 },
  { month: "Apr", policies: 23000 },
  { month: "May", policies: 24000 },
  { month: "Jun", policies: 24565 },
]

export function PolicyGrowthChart() {
  return (
    <ChartContainer
      config={{
        policies: {
          label: "Policies",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="month" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="policies" stroke="var(--color-policies)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

