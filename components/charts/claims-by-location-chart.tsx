"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { location: "New York", value: 400 },
  { location: "Los Angeles", value: 300 },
  { location: "Chicago", value: 200 },
  { location: "Houston", value: 150 },
  { location: "Phoenix", value: 100 },
  { location: "Other", value: 250 },
]

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#ffc658']

export default function ClaimsByLocationChart() {
  return (
    <ChartContainer
      config={{
        value: {
          label: "Claims",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <ChartTooltip content={<ChartTooltipContent />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

