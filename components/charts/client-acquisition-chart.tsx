"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON>lt<PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", clients: 50 },
  { month: "Feb", clients: 80 },
  { month: "Mar", clients: 120 },
  { month: "Apr", clients: 170 },
  { month: "May", clients: 210 },
  { month: "Jun", clients: 250 },
]

export default function ClientAcquisitionChart() {
  return (
    <ChartContainer
      config={{
        clients: {
          label: "New Clients",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="month" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="clients" stroke="var(--color-clients)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

