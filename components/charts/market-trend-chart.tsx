"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON>lt<PERSON>, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { month: "Jan", growth: 2.5 },
  { month: "Feb", growth: 3.1 },
  { month: "Mar", growth: 3.8 },
  { month: "Apr", growth: 3.3 },
  { month: "May", growth: 3.6 },
  { month: "Jun", growth: 4.2 },
  { month: "Jul", growth: 4.5 },
  { month: "Aug", growth: 4.1 },
  { month: "Sep", growth: 4.4 },
  { month: "Oct", growth: 4.7 },
  { month: "Nov", growth: 4.9 },
  { month: "Dec", growth: 5.2 },
]

export function MarketTrendChart() {
  return (
    <ChartContainer
      config={{
        growth: {
          label: "Growth (%)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="month" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="growth" stroke="var(--color-growth)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

