"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON>ontainer, <PERSON>lt<PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { date: "2023-01", claims: 120 },
  { date: "2023-02", claims: 150 },
  { date: "2023-03", claims: 180 },
  { date: "2023-04", claims: 220 },
  { date: "2023-05", claims: 190 },
  { date: "2023-06", claims: 230 },
]

export default function ClaimsOverTimeChart() {
  return (
    <ChartContainer
      config={{
        claims: {
          label: "Claims",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="date" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Line type="monotone" dataKey="claims" stroke="var(--color-claims)" strokeWidth={2} />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

