"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { ageGroup: "18-25", count: 120 },
  { ageGroup: "26-35", count: 250 },
  { ageGroup: "36-45", count: 180 },
  { ageGroup: "46-55", count: 150 },
  { ageGroup: "56-65", count: 100 },
  { ageGroup: "65+", count: 50 },
]

export default function AgeDistributionChart() {
  return (
    <ChartContainer
      config={{
        count: {
          label: "Clients",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="ageGroup" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="count" fill="var(--color-count)" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

