"use client"

import { Area, AreaChart, ResponsiveContainer, <PERSON><PERSON>xi<PERSON>, <PERSON><PERSON>xi<PERSON> } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

const data = [
  { year: "2018", rate: 75 },
  { year: "2019", rate: 78 },
  { year: "2020", rate: 80 },
  { year: "2021", rate: 82 },
  { year: "2022", rate: 85 },
  { year: "2023", rate: 88 },
]

export default function ClientRetentionChart() {
  return (
    <ChartContainer
      config={{
        rate: {
          label: "Retention Rate (%)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <XAxis dataKey="year" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Area type="monotone" dataKey="rate" stroke="var(--color-rate)" fill="var(--color-rate)" fillOpacity={0.2} />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}

