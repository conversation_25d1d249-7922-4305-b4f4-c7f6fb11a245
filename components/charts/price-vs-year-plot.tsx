"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, ReferenceArea, Line } from 'recharts'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { useMemo } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface PriceVsYearPlotProps {
  data: Array<{
    Source_Url?: string
    Year: number
    Price: number
  }>
  referenceYears?: number[]
  showReferenceLines?: boolean
  userSelectedYear?: number  // User's selected year
  showRegressionLine?: boolean  // New prop to control regression line visibility
  highlightedVehicles?: string[] // Array of Source_Url for vehicles to highlight
}

// Linear regression function to calculate slope and intercept
function calculateLinearRegression(data: Array<{x: number, y: number}>) {
  const n = data.length;
  if (n < 2) return null; // Need at least 2 points for regression
  
  let sumX = 0;
  let sumY = 0;
  let sumXY = 0;
  let sumXX = 0;
  
  for (const point of data) {
    sumX += point.x;
    sumY += point.y;
    sumXY += point.x * point.y;
    sumXX += point.x * point.x;
  }
  
  // Calculate slope (m) and intercept (b) for y = mx + b
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;
  
  return { slope, intercept };
}

export function PriceVsYearPlot({ 
  data, 
  referenceYears = [], 
  showReferenceLines = true,
  userSelectedYear,
  showRegressionLine = true,
  highlightedVehicles = [] // Default to empty array
}: PriceVsYearPlotProps) {
  // Determine if data is in a loading or incomplete state
  const isDataReady = useMemo(() => {
    if (!data || data.length === 0) return false;
    return data.length >= 2;
  }, [data]);
  
  // Group data by highlight status
  const { regularData, highlightedData } = useMemo(() => {
    const filtered = (data || []).filter(item => 
      item.Year > 0 && item.Price > 0
    );
    
    const regular: { x: number; y: number }[] = [];
    const highlighted: { x: number; y: number }[] = [];
    
    filtered.forEach(item => {
      const point = { x: item.Year, y: item.Price };
      
      if (item.Source_Url && highlightedVehicles.includes(item.Source_Url)) {
        highlighted.push(point);
      } else {
        regular.push(point);
      }
    });
    
    return { 
      regularData: regular, 
      highlightedData: highlighted 
    };
  }, [data, highlightedVehicles]);
  
  // Use all data for calculations (both regular and highlighted)
  const chartData = useMemo(() => {
    return [...regularData, ...highlightedData];
  }, [regularData, highlightedData]);
  
  // Calculate linear regression coefficients
  const regression = useMemo(() => {
    if (chartData.length < 2) return null;
    
    const n = chartData.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    
    for (const point of chartData) {
      sumX += point.x;
      sumY += point.y;
      sumXY += point.x * point.y;
      sumXX += point.x * point.x;
    }
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    return { slope, intercept };
  }, [chartData]);
  
  // Calculate statistics for data-driven insights
  const stats = useMemo(() => {
    if (!chartData.length) return null;
    
    const prices = chartData.map(item => item.y);
    const years = chartData.map(item => item.x);
    
    return {
      maxPrice: Math.max(...prices),
      minPrice: Math.min(...prices),
      avgPrice: Math.round(prices.reduce((sum, val) => sum + val, 0) / prices.length),
      maxYear: Math.max(...years),
      minYear: Math.min(...years),
      currentYear: new Date().getFullYear()
    };
  }, [chartData]);

  // Calculate domain values for proper chart scaling
  const xDomain = useMemo(() => {
    if (!stats || !chartData.length) return [2010, 2025];
    
    const minX = Math.floor(stats.minYear);
    const maxX = Math.ceil(stats.maxYear);
    
    return [minX - 0.5, maxX + 0.5];
  }, [stats, chartData]);
  
  const yDomain = useMemo(() => {
    if (!stats) return [0, 35000];
    
    const minY = Math.floor(stats.minPrice * 0.9 / 5000) * 5000;
    const maxY = Math.ceil(stats.maxPrice * 1.1 / 5000) * 5000;
    
    return [minY, maxY];
  }, [stats]);

  // If data is not ready, show skeleton UI
  if (!isDataReady) {
    return (
      <div className="h-[300px] w-full flex flex-col space-y-2">
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>
        <Skeleton className="h-[260px] w-full" />
      </div>
    );
  }

  // Calculate regression line endpoints
  const getYForX = (x: number) => {
    if (!regression) return 0;
    return regression.slope * x + regression.intercept;
  };

  const minX = xDomain[0];
  const maxX = xDomain[1];
  const minY = getYForX(minX);
  const maxY = getYForX(maxX);

  return (
    <ChartContainer
      config={{
        price: {
          label: "Price (EUR)",
          color: "hsl(var(--chart-1))",
        },
      }}
      className="h-[300px] w-full"
    >
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart margin={{ top: 20, right: 40, bottom: 20, left: 20 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            type="number" 
            dataKey="x" 
            name="Year" 
            domain={xDomain} 
            tickFormatter={value => `${Math.round(value)}`}
          />
          <YAxis 
            type="number" 
            dataKey="y" 
            name="Price" 
            unit=" EUR" 
            domain={yDomain}
            tickFormatter={value => `${Math.round(value).toLocaleString()}`} 
          />
          <ChartTooltip 
            content={<ChartTooltipContent />} 
            cursor={{ strokeDasharray: '3 3' }}
          />
          
          {/* Regression trend line */}
          {showRegressionLine && regression && (
            <ReferenceLine
              segment={[
                { x: minX, y: minY || 0 },
                { x: maxX, y: maxY || 0 }
              ]}
              stroke="#0060df"
              strokeWidth={2.5}
              ifOverflow="visible"
              strokeOpacity={0.8}
            />
          )}
          
          {/* Regular vehicles in blue */}
          <Scatter 
            name="Vehicles" 
            data={regularData} 
            fill="var(--color-price)" 
            opacity={0.8}
          />
          
          {/* Highlighted vehicles in green */}
          {highlightedData.length > 0 && (
            <Scatter 
              name="Selected Vehicles" 
              data={highlightedData} 
              fill="#22c55e" 
              opacity={0.9}
              strokeWidth={1.5}
              stroke="#15803d"
            />
          )}
          
          {/* User's selected year reference line */}
          {showReferenceLines && userSelectedYear && userSelectedYear > 0 && (
            <ReferenceLine 
              x={userSelectedYear} 
              stroke="#FF9800" 
              strokeDasharray="3 3" 
              strokeWidth={2}
              label={{ 
                value: `Selected ${userSelectedYear}`, 
                position: 'top',
                fill: '#FF9800',
                fontSize: 11,
                fontWeight: 'bold',
                offset: 10
              }} 
            />
          )}
          
          {/* Average price reference line */}
          {showReferenceLines && stats && (
            <ReferenceLine 
              y={stats.avgPrice} 
              stroke="#888" 
              strokeDasharray="3 3" 
              label={{ 
                value: "Avg", 
                position: 'right',
                fill: '#888',
                fontSize: 11,
                offset: 5
              }} 
            />
          )}
          
          {/* Current year reference line - optional */}
          {showReferenceLines && stats && stats.currentYear && (
            <ReferenceLine 
              x={stats.currentYear} 
              stroke="#66a" 
              strokeDasharray="3 3" 
              label={{ 
                value: "Current", 
                position: 'top',
                fill: '#66a',
                fontSize: 11,
                offset: 10
              }} 
            />
          )}
        </ScatterChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
}
