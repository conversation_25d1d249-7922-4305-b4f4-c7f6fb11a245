"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TaskCard } from "@/components/task-card"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Search } from 'lucide-react'
import { CreateTaskDialog } from "@/components/create-task-dialog"

// Mock data for tasks
const initialTasks = [
  {
    id: "TASK-001",
    title: "Review Claim Documentation",
    description: "Review and validate all submitted documentation for claim #CLM-2023-456",
    category: "Claims",
    priority: "high",
    status: "in_progress",
    progress: 45,
    dueDate: "2023-12-15",
    assignedBy: "<PERSON>",
    labels: ["documentation", "review", "urgent"],
    attachments: 3,
    comments: 5
  },
  {
    id: "TASK-002",
    title: "Update Client Information",
    description: "Update contact and policy information for client <PERSON>",
    category: "Client Management",
    priority: "medium",
    status: "not_started",
    progress: 0,
    dueDate: "2023-12-20",
    assignedBy: "<PERSON>",
    labels: ["client", "update"],
    attachments: 1,
    comments: 2
  },
  {
    id: "TASK-003",
    title: "Process Premium Payment",
    description: "Process and verify incoming premium payment for policy #POL-789-123",
    category: "Finance",
    priority: "low",
    status: "completed",
    progress: 100,
    dueDate: "2023-12-10",
    assignedBy: "Emily Davis",
    labels: ["payment", "finance"],
    attachments: 2,
    comments: 3
  },
  {
    id: "TASK-004",
    title: "Investigate Fraud Alert",
    description: "Investigate potential fraud alert triggered for claim #CLM-2023-789",
    category: "Fraud",
    priority: "high",
    status: "in_progress",
    progress: 75,
    dueDate: "2023-12-12",
    assignedBy: "Robert Johnson",
    labels: ["fraud", "investigation", "urgent"],
    attachments: 5,
    comments: 8
  },
  {
    id: "TASK-005",
    title: "Prepare Monthly Report",
    description: "Compile and prepare monthly claims processing report",
    category: "Reporting",
    priority: "medium",
    status: "not_started",
    progress: 0,
    dueDate: "2023-12-31",
    assignedBy: "Lisa Anderson",
    labels: ["report", "monthly"],
    attachments: 0,
    comments: 1
  }
]

const categories = ["All", "Claims", "Client Management", "Finance", "Fraud", "Reporting"]
const priorities = ["All", "high", "medium", "low"]
const statuses = ["All", "not_started", "in_progress", "completed"]

export function MyTasksScreen() {
  const [tasks, setTasks] = useState(initialTasks)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedPriority, setSelectedPriority] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "All" || task.category === selectedCategory
    const matchesPriority = selectedPriority === "All" || task.priority === selectedPriority
    const matchesStatus = selectedStatus === "All" || task.status === selectedStatus

    return matchesSearch && matchesCategory && matchesPriority && matchesStatus
  })

  const handleCreateTask = (newTask: any) => {
    setTasks(prev => [{
      id: `TASK-${String(prev.length + 1).padStart(3, '0')}`,
      ...newTask,
      progress: 0,
      status: "not_started",
      attachments: 0,
      comments: 0
    }, ...prev])
    setIsCreateDialogOpen(false)
  }

  const handleUpdateTask = (taskId: string, updates: Partial<typeof tasks[0]>) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    ))
  }

  const handleDeleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Task List</CardTitle>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Task
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                {priorities.map((priority) => (
                  <SelectItem key={priority} value={priority}>
                    {priority.charAt(0).toUpperCase() + priority.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No tasks found matching your filters.
              </div>
            ) : (
              filteredTasks.map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onUpdate={handleUpdateTask}
                  onDelete={handleDeleteTask}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>

      <CreateTaskDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateTask}
      />
    </div>
  )
}

