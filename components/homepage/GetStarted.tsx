import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { AnimatedSpan, Terminal, TypingAnimation } from "@/components/ui/terminal";
import { AnimatedList} from "@/components/ui/animatedList";


const GetStarted = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // When the section is 20% visible, trigger the animation
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Once triggered, we can disconnect the observer
          observer.disconnect();
        }
      },
      { threshold: 0.2 } // Trigger when 20% of the element is visible
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  const features = [
    {
      id: 1,
      name: "Python SDK",
      description: "Import rekover and access all features with a few lines of code",
      icon: "code",
      color: "#0be5a9",
      time: "Step 1",
    },
    {
      id: 2,
      name: "RESTful API",
      description: "Integrate with any language or platform via our comprehensive API",
      icon: "package",
      color: "#0be5a9",
      time: "Step 2",
    },
    {
      id: 3,
      name: "One-Line Integration",
      description: "Access complex AI features with minimal code",
      icon: "zap",
      color: "#0be5a9",
      time: "Step 3",
    },
    {
      id: 4,
      name: "OCR & Document Analysis",
      description: "Extract and validate data from any document with a single function call",
      icon: "file-text",
      color: "#4A9DFF", // Blue
      time: "client.ocr()",
    },
    {
      id: 5,
      name: "Fraud Detection",
      description: "Analyze claims for fraud indicators with network analysis",
      icon: "shield",
      color: "#FF3D71", // Red
      time: "client.detect_fraud()",
    },
    {
      id: 6,
      name: "Complex Claims Analysis",
      description: "Get comprehensive legal case analysis with one API call",
      icon: "search",
      color: "#9B51E0", // Purple
      time: "client.analyze_claim()",
    },
    {
      id: 7,
      name: "External Data Access",
      description: "Seamlessly connect to our curated external data sources",
      icon: "database",
      color: "#00C9A7", // Green
      time: "client.get_data()",
    },
    {
      id: 8,
      name: "Batch Processing",
      description: "Process thousands of documents or claims in parallel",
      icon: "layers",
      color: "#FFB800", // Yellow
      time: "client.batch_process()",
    },
    {
      id: 9,
      name: "Custom Model Training",
      description: "Train models on your specific data with our SDK",
      icon: "cpu",
      color: "#1E86FF", // Light Blue
      time: "client.train_model()",
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="bg-[#141b2b] py-24 relative overflow-hidden"
    >
      {/* Simplified Background with solid fades */}
      <div className="absolute inset-0">
        {/* Base background */}
        <div className="absolute inset-0 bg-[#0be5a9]/5 mix-blend-overlay" />

        {/* Top solid fade - blends with Feature.tsx */}
        <div className="absolute top-0 inset-x-0 h-20 bg-gradient-to-b from-[#141b2b] to-[#141b2b]/0" />

        {/* Bottom solid fade - blends with next section */}
        <div className="absolute bottom-0 inset-x-0 h-20 bg-gradient-to-t from-[#141b2b] to-[#141b2b]/0" />

        {/* Repositioned green gradient circle - moved down from top-0 to top-1/4 */}
        <div className="absolute top-1/4 right-0 w-[500px] h-[500px] bg-gradient-to-b from-[#0be5a9]/20 to-transparent rounded-full blur-3xl" />

        {/* Orange glow remains at bottom */}
        <div className="absolute bottom-1/4 left-0 w-[500px] h-[500px] bg-gradient-to-t from-[#f69323]/20 to-transparent rounded-full blur-3xl" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative">
        {/* Get Started Steps */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Powerful SDK & API Integration
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Our Python SDK and API make it effortless to integrate all our AI features into your existing systems
          </p>
        </div>

        {/* Integration Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <Link href="/login" legacyBehavior>
            <a className="block cursor-pointer group">
              <div className="border-[#0be5a9]/20 rounded-lg group-hover:border-[#0be5a9]/60 transition-colors duration-300">
                <Terminal className="min-h-[400px]" isVisible={isVisible}>
                  <div className="h-full flex flex-col">
                    {/* Import statement - purple for import keyword */}
                    <div className="flex">
                      <TypingAnimation className="text-[#ff79c6]" isVisible={isVisible}>import</TypingAnimation>
                      <TypingAnimation className="text-white ml-1" isVisible={isVisible}>rekover</TypingAnimation>
                    </div>

                    <AnimatedSpan delay={1000} className="text-gray-500" isVisible={isVisible}>
                      # Initialize the client with your API key
                    </AnimatedSpan>

                    {/* Variable assignment with method call */}
                    <div className="flex">
                      <TypingAnimation delay={1500} className="text-white" isVisible={isVisible}>client</TypingAnimation>
                      <TypingAnimation delay={1500} className="text-white" isVisible={isVisible}> = </TypingAnimation>
                      <TypingAnimation delay={1500} className="text-[#8be9fd]" isVisible={isVisible}>rekover</TypingAnimation>
                      <TypingAnimation delay={1500} className="text-white" isVisible={isVisible}>.</TypingAnimation>
                      <TypingAnimation delay={1500} className="text-[#50fa7b]" isVisible={isVisible}>Client</TypingAnimation>
                      <TypingAnimation delay={1500} className="text-white" isVisible={isVisible}>(</TypingAnimation>
                    </div>

                    {/* Parameter with string value */}
                    <div className="flex pl-2">
                      <TypingAnimation delay={2000} className="text-[#f1fa8c]" isVisible={isVisible}>api_key</TypingAnimation>
                      <TypingAnimation delay={2000} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={2000} className="text-[#f1fa8c]" isVisible={isVisible}>&quot;YOUR_API_KEY&quot;</TypingAnimation>
                    </div>

                    <TypingAnimation delay={2500} className="text-white" isVisible={isVisible}>)</TypingAnimation>

                    <AnimatedSpan delay={3000} className="text-gray-500" isVisible={isVisible}>
                      # Extract data from a document with OCR
                    </AnimatedSpan>

                    {/* OCR method call */}
                    <div className="flex">
                      <TypingAnimation delay={3500} className="text-white" isVisible={isVisible}>invoice_data</TypingAnimation>
                      <TypingAnimation delay={3500} className="text-white" isVisible={isVisible}> = </TypingAnimation>
                      <TypingAnimation delay={3500} className="text-white" isVisible={isVisible}>client.</TypingAnimation>
                      <TypingAnimation delay={3500} className="text-[#8be9fd]" isVisible={isVisible}>ocr</TypingAnimation>
                      <TypingAnimation delay={3500} className="text-white" isVisible={isVisible}>.</TypingAnimation>
                      <TypingAnimation delay={3500} className="text-[#50fa7b]" isVisible={isVisible}>extract</TypingAnimation>
                      <TypingAnimation delay={3500} className="text-white" isVisible={isVisible}>(</TypingAnimation>
                    </div>

                    {/* OCR parameters */}
                    <div className="flex pl-2">
                      <TypingAnimation delay={4000} className="text-[#f1fa8c]" isVisible={isVisible}>document_path</TypingAnimation>
                      <TypingAnimation delay={4000} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={4000} className="text-[#ff79c6]" isVisible={isVisible}>&quot;invoice.pdf&quot;</TypingAnimation>
                      <TypingAnimation delay={4000} className="text-white" isVisible={isVisible}>,</TypingAnimation>
                    </div>

                    <div className="flex pl-2">
                      <TypingAnimation delay={4300} className="text-[#f1fa8c]" isVisible={isVisible}>validate</TypingAnimation>
                      <TypingAnimation delay={4300} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={4300} className="text-[#bd93f9]" isVisible={isVisible}>True</TypingAnimation>
                    </div>

                    <TypingAnimation delay={4500} className="text-white" isVisible={isVisible}>)</TypingAnimation>

                    <AnimatedSpan delay={5000} className="text-gray-500" isVisible={isVisible}>
                      # Analyze a complex claim with legal framework
                    </AnimatedSpan>

                    {/* Claims analysis method call */}
                    <div className="flex">
                      <TypingAnimation delay={5500} className="text-white" isVisible={isVisible}>analysis</TypingAnimation>
                      <TypingAnimation delay={5500} className="text-white" isVisible={isVisible}> = </TypingAnimation>
                      <TypingAnimation delay={5500} className="text-white" isVisible={isVisible}>client.</TypingAnimation>
                      <TypingAnimation delay={5500} className="text-[#8be9fd]" isVisible={isVisible}>claims</TypingAnimation>
                      <TypingAnimation delay={5500} className="text-white" isVisible={isVisible}>.</TypingAnimation>
                      <TypingAnimation delay={5500} className="text-[#50fa7b]" isVisible={isVisible}>analyze</TypingAnimation>
                      <TypingAnimation delay={5500} className="text-white" isVisible={isVisible}>(</TypingAnimation>
                    </div>

                    {/* Claims analysis parameters */}
                    <div className="flex pl-2">
                      <TypingAnimation delay={5800} className="text-[#f1fa8c]" isVisible={isVisible}>description</TypingAnimation>
                      <TypingAnimation delay={5800} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={5800} className="text-[#ff79c6]" isVisible={isVisible}>&quot;Auto accident with disputed liability&quot;</TypingAnimation>
                      <TypingAnimation delay={5800} className="text-white" isVisible={isVisible}>,</TypingAnimation>
                    </div>

                    <div className="flex pl-2">
                      <TypingAnimation delay={6100} className="text-[#f1fa8c]" isVisible={isVisible}>find_similar</TypingAnimation>
                      <TypingAnimation delay={6100} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={6100} className="text-[#bd93f9]" isVisible={isVisible}>True</TypingAnimation>
                      <TypingAnimation delay={6100} className="text-white" isVisible={isVisible}>,</TypingAnimation>
                    </div>

                    <div className="flex pl-2">
                      <TypingAnimation delay={6400} className="text-[#f1fa8c]" isVisible={isVisible}>include_legal_citations</TypingAnimation>
                      <TypingAnimation delay={6400} className="text-white" isVisible={isVisible}>=</TypingAnimation>
                      <TypingAnimation delay={6400} className="text-[#bd93f9]" isVisible={isVisible}>True</TypingAnimation>
                    </div>

                    <TypingAnimation delay={6700} className="text-white" isVisible={isVisible}>)</TypingAnimation>

                    <AnimatedSpan delay={7000} className="text-[#0be5a9]" isVisible={isVisible}>
                      ✓ Complete AI toolkit at your fingertips
                    </AnimatedSpan>

                  </div>
                </Terminal>
              </div>
            </a>
          </Link>

          <div>

            <div className="relative h-[400px] w-full flex flex-col overflow-hidden">
                            {/* Animated visual list */}
              <AnimatedList
                className="w-full"
                maxItems={6}
                delay={2500}
                isVisible={isVisible}
                items={features}
              />

            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GetStarted;