import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface TestimonialProps {
  name: string;
  role: string;
  company: string;
  testimonial: string;
  avatarSrc: string;
}

export function TestimonialCard({ name, role, company, testimonial, avatarSrc }: TestimonialProps) {
  return (
    <Card className="bg-white text-gray-900 shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <Avatar className="h-12 w-12 mr-4">
            <AvatarImage src={avatarSrc} alt={name} />
            <AvatarFallback>{name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold text-lg">{name}</h3>
            <p className="text-sm text-gray-600">{role}, {company}</p>
          </div>
        </div>
        <p className="text-gray-700 italic">"{testimonial}"</p>
      </CardContent>
    </Card>
  )
}

