import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Calendly<PERSON>utton } from "@/components/ui/calendly-button";
import {
  Coins,
  BarChart3,
  ClipboardCheck,
  Cloud,
  FileText,
  Building2,
  Users,
  ArrowRight,
  X,
  BarChart4,
  FileCheck,
  CloudSun,
  MessageCircle,
  Brain,
  Workflow,
  CheckCircle,
  ExternalLink,
  Clock,
  ShieldCheck,
  Banknote,
  AlertTriangle,
  Network,
  Server,
  Plus,
  Calendar
} from 'lucide-react';
import dynamic from 'next/dynamic';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-[#1a2436] rounded-xl border border-[#0be5a9]/20 p-6 md:p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={e => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white"
        >
          <X className="w-6 h-6" />
        </button>
        {children}
      </div>
    </div>
  );
};

// Import LineShadowText with no SSR since it uses client hooks
const ServicesHeading = dynamic(() => import('@/components/layout/ServicesHeading').then(mod => mod.ServicesHeading), {
  ssr: false,
});

// Reusable Pulsating Circle Component
const PulsatingCircle = ({
  className = '',
  isPulsating = true // Default to pulsating
}: {
  className?: string;
  isPulsating?: boolean;
}) => (
  <span className={`relative inline-flex justify-center items-center ${className}`}>
    {/* Outer ping animation - conditional based on isPulsating and hover */}
    <span
      className={`absolute inline-flex h-4 w-4 rounded-full bg-gray-500 group-hover:bg-[#f69323] opacity-75
                 ${isPulsating ? 'animate-ping group-hover:animate-none' : ''}`}
    ></span>
    {/* Inner static circle */}
    <span className="relative inline-flex rounded-full h-2 w-2 bg-gray-600 group-hover:bg-[#f69323]"></span>
  </span>
);

const Features = () => {
  const [selectedTenant, setSelectedTenant] = useState<'app' | 'test' | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [visitedCategories, setVisitedCategories] = useState<Set<string>>(new Set());
  const [visitedTenants, setVisitedTenants] = useState<Set<'app' | 'test'>>(new Set());

  const categories = [
    {
      id: 'analytics',
      name: 'Advanced Analytics',
      icon: BarChart3,
      description: 'Comprehensive data analysis and visualization tools powered by AI and external data sources.',
      tools: [
        { id: 'market', name: 'Market Evaluation', icon: BarChart3, description: 'Real-time market analysis with vendor reputation and risk geo indexes.' },
        { id: 'debtors', name: 'Debtors', icon: Coins, description: 'Financial analysis with social security and financial debtor data integration.' }
      ]
    },
    {
      id: 'ml',
      name: 'ML Models \n',
      icon: Brain,
      description: 'Powerful ML models trained with insurance expert knowledge for accurate predictions.',
      tools: [
        { id: 'ocr', name: 'OCR', icon: FileText, description: 'Advanced document scanning and data extraction from invoices and forms.' },
        { id: 'fraud', name: 'Fraud Detection', icon: ShieldCheck, description: 'AI-powered fraud detection with network analysis and external data.' }
      ]
    },
    {
      id: 'automation',
      name: 'Task Automation \n',
      icon: Workflow,
      description: 'Intelligent automation solutions to streamline insurance workflows and processes.',
      tools: [
        { id: 'claims', name: 'Claim Acceptance', icon: ClipboardCheck, description: 'Intelligent claim processing with litigation court case analysis.' }
      ]
    },
    {
      id: 'assistants',
      name: 'Complex Claims',
      icon: MessageCircle,
      description: 'Advanced assistants for complex claims analysis with comprehensive legal case insights.',
      tools: [
        { id: 'companion', name: 'Claims Assistant', icon: MessageCircle, description: 'AI assistant with legal framework intelligence and judge analysis.' }
      ]
    }
  ];

  const handleCloseCategoryModal = () => {
    if (selectedCategory) {
      setVisitedCategories(prev => new Set(prev).add(selectedCategory));
    }
    setSelectedCategory(null);
  };

  const handleCloseTenantModal = () => {
      if (selectedTenant) {
        setVisitedTenants(prev => new Set(prev).add(selectedTenant));
      }
      setSelectedTenant(null);
  };

  const renderTenantModal = () => {
    const content = {
      app: {
        title: 'Enterprise AI Suite',
        description: 'A comprehensive AI consultancy solution for insurers, with custom development and a marketplace of ready-to-use features delivered via SaaS and API.',
        features: [
          { icon: Server, text: 'End-to-end AI solution development' },
          { icon: BarChart4, text: 'Insurance-specific AI models' },
          { icon: FileCheck, text: 'Expert-built features marketplace' },
          { icon: CloudSun, text: 'SaaS platform and API integration' },
        ],
        image: 'https://images.unsplash.com/photo-**********-e076c223a692?auto=format&fit=crop&q=80'
      },
      test: {
        title: 'AI Risk & Compliance Lab',
        description: 'Our AI solutions leverage extensive external data sources for comprehensive insights, from vendor reputation to litigation court cases.',
        features: [
          { icon: ShieldCheck, text: 'Fraud detection with network analysis' },
          { icon: Banknote, text: 'Financial and social security data integration' },
          { icon: AlertTriangle, text: 'Risk geo indexes and litigation analysis' },
          { icon: Network, text: 'Comprehensive external data sources' },
        ],
        image: 'https://images.unsplash.com/photo-*************-009f0129c71c?auto=format&fit=crop&q=80'
      }
    };

    if (!selectedTenant) return null;
    const data = content[selectedTenant];

    return (
      <div className="space-y-6">
        <div className="relative h-48 rounded-lg overflow-hidden mb-8">
          <div className="absolute inset-0 bg-gradient-to-t from-[#1a2436] to-transparent z-10" />
          <img src={data.image} alt={data.title} className="w-full h-full object-cover" />
          <h3 className="absolute bottom-4 left-4 text-2xl font-bold text-white z-20">{data.title}</h3>
        </div>

        <p className="text-gray-300">{data.description}</p>

        <div className="grid grid-cols-2 gap-4 mt-6">
          {data.features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3 bg-[#141b2b] p-4 rounded-lg">
              <feature.icon className="w-5 h-5 text-[#0be5a9]" />
              <span className="text-white text-sm">{feature.text}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderCategoryModal = () => {
    if (!selectedCategory) return null;
    const category = categories.find(c => c.id === selectedCategory);
    if (!category) return null;

    // Content mapping for each category with optimized content
    const contentMap = {
      analytics: {
        title: "Advanced Analytics",
        subtitle: "Data-Driven Insights with External Sources",
        benefits: [
          "Leverage vendor reputation and risk geo indexes",
          "Integrate social security and financial data",
          "Gain comprehensive market intelligence"
        ],
        cta: "Explore our Advanced Analytics solutions"
      },
      ml: {
        title: "ML Models",
        subtitle: "Expert-Built AI for Insurance Workflows",
        benefits: [
          "Extract data from invoices and documents with OCR",
          "Detect fraud with network analysis and external data",
          "Models trained with insurance expert knowledge"
        ],
        cta: "Discover our insurance-specific ML models"
      },
      automation: {
        title: "Task Automation",
        subtitle: "Streamline Insurance Operations with AI",
        benefits: [
          "Process claims with litigation court case analysis",
          "Automate routine insurance workflows",
          "Integrate with existing insurance systems"
        ],
        cta: "See how our automation solutions transform operations"
      },
      assistants: {
        title: "Complex Claims",
        subtitle: "Comprehensive Legal Case Analysis",
        benefits: [
          "Legal framework intelligence and judge analysis",
          "Strategy assistance and argument generation",
          "Clickable legal citations and references"
        ],
        cta: "Explore our Complex Claims Assistant"
      }
    };

    const content = contentMap[category.id as keyof typeof contentMap];

    // Tool icon mapping using Lucide React icons instead of emojis
    const toolIconComponents = {
      market: BarChart3,
      debtors: Coins,
      ocr: FileText,
      fraud: ShieldCheck,
      claims: ClipboardCheck,
      companion: MessageCircle
    };

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-xl bg-[#f69323]/10 flex items-center justify-center">
            <category.icon className="w-8 h-8 text-[#f69323]" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{content.title}</h3>
            <p className="text-gray-400">{content.subtitle}</p>
          </div>
        </div>

        <div className="bg-[#141b2b] rounded-lg p-6 mt-6">
          <h4 className="text-white font-semibold mb-4">Key Benefits:</h4>
          <ul className="space-y-3">
            {content.benefits.map((benefit, index) => (
              <li key={index} className="flex items-start gap-3 text-gray-300">
                <CheckCircle className="w-5 h-5 text-[#f69323] min-w-5" />
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="mt-6">
          <h4 className="text-white font-semibold mb-4">Tools in this Category:</h4>
          <div className="bg-[#141b2b] rounded-lg p-6">
            {category.tools.length > 0 ? (
              <div className="space-y-4">
                {/* Show the first tool as an example */}
                <div className="flex items-start gap-3">
                  <div className="min-w-10 h-10 rounded-lg bg-[#f69323]/10 flex items-center justify-center">
                    {(() => {
                      const ToolIcon = toolIconComponents[category.tools[0].id as keyof typeof toolIconComponents] || FileCheck;
                      return <ToolIcon className="w-5 h-5 text-[#f69323]" />;
                    })()}
                  </div>
                  <div>
                    <h5 className="text-white font-medium">{category.tools[0].name}</h5>
                    <p className="text-gray-400 text-sm">{category.tools[0].description}</p>
                  </div>
                </div>

                {/* "And much more" indicator inside MODAL */}
                <div className="flex items-center gap-3 pt-3 border-t border-[#f69323]/10 group">
                  <div className="min-w-10 h-10 rounded-lg bg-[#f69323]/10 flex items-center justify-center group-hover:scale-110 transition-transform">
                    <Plus
                      className="w-5 h-5 text-[#f69323] transition-transform duration-300"
                      aria-hidden="true"
                    />
                  </div>
                  <div>
                    <h5 className="text-white font-medium">And Much More</h5>
                    <p className="text-gray-400 text-sm">Explore our full suite of advanced tools</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-start gap-3">
                <div className="min-w-10 h-10 rounded-lg bg-[#f69323]/10 flex items-center justify-center">
                  <Clock className="w-5 h-5 text-[#f69323]" />
                </div>
                <div>
                  <h5 className="text-white font-medium">Coming Soon</h5>
                  <p className="text-gray-400 text-sm">New AI assistant tools in development</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="mt-6">
          <CalendlyButton
            className="w-full bg-[#f69323] hover:bg-[#f69323]/90 text-white font-medium rounded-lg p-4 flex items-center gap-3 transition-all"
            calendlyUrl="https://calendly.com/francisco-rekover/30min?primary_color=e3af13"
          >
            <Calendar className="w-5 h-5 text-white min-w-5" />
            <p className="text-white">{content.cta}</p>
          </CalendlyButton>
        </motion.div>
      </div>
    );
  };

  const renderToolModal = () => {
    if (!selectedTool) return null;

    // Find the tool in any category
    let foundTool = null;
    for (const category of categories) {
      const tool = category.tools.find(t => t.id === selectedTool);
      if (tool) {
        foundTool = tool;
        break;
      }
    }

    if (!foundTool) return null;
    const tool = foundTool;

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 rounded-xl bg-[#0be5a9]/10 flex items-center justify-center">
            <tool.icon className="w-8 h-8 text-[#0be5a9]" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">{tool.name}</h3>
            <p className="text-gray-400">AI-Powered Solution</p>
          </div>
        </div>

        <p className="text-gray-300">{tool.description}</p>

        <div className="bg-[#141b2b] rounded-lg p-6 mt-6">
          <h4 className="text-white font-semibold mb-4">Key Features</h4>
          <ul className="space-y-3">
            <li className="flex items-center gap-2 text-gray-300">
              <div className="w-1.5 h-1.5 rounded-full bg-[#0be5a9]" />
              Real-time processing and analysis
            </li>
            <li className="flex items-center gap-2 text-gray-300">
              <div className="w-1.5 h-1.5 rounded-full bg-[#0be5a9]" />
              Advanced AI algorithms
            </li>
            <li className="flex items-center gap-2 text-gray-300">
              <div className="w-1.5 h-1.5 rounded-full bg-[#0be5a9]" />
              Customizable workflows
            </li>
            <li className="flex items-center gap-2 text-gray-300">
              <div className="w-1.5 h-1.5 rounded-full bg-[#0be5a9]" />
              Integration capabilities
            </li>
          </ul>
        </div>
      </div>
    );
  };

  return (
    <section className="bg-[#141b2b] py-24 relative overflow-hidden">
      <Modal isOpen={!!selectedTenant} onClose={handleCloseTenantModal}>
        {renderTenantModal()}
      </Modal>

      <Modal isOpen={!!selectedCategory} onClose={handleCloseCategoryModal}>
        {renderCategoryModal()}
      </Modal>

      <Modal isOpen={!!selectedTool} onClose={() => setSelectedTool(null)}>
        {renderToolModal()}
      </Modal>

      <div className="absolute inset-0">
        {/* Base overlay */}
        <div className="absolute inset-0 bg-[#0be5a9]/5 mix-blend-overlay" />

        {/* Top solid fade */}
        <div className="absolute top-0 inset-x-0 h-20 bg-gradient-to-b from-[#141b2b] to-[#141b2b]/0" />

        {/* Bottom solid fade - blends with GetStarted */}
        <div className="absolute bottom-0 inset-x-0 h-20 bg-gradient-to-t from-[#141b2b] to-[#141b2b]/0" />

        {/* Keep existing background effects */}
        <div className="absolute inset-0 [mask-image:radial-gradient(1000px_1000px_at_center,white,transparent)]">
          <div className="absolute inset-y-0 right-0 w-1/2 bg-gradient-to-l from-[#0be5a9]/10 to-transparent" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative">
        <div className="text-center mb-16">
          <ServicesHeading />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div
              className="relative bg-[#1a2436] rounded-xl p-8 border border-white/10 hover:border-[#0be5a9]/50 transition-all group cursor-pointer flex flex-col"
              onClick={() => setSelectedTenant('app')}
            >
              <div className="absolute top-4 right-4">
                <PulsatingCircle
                  className="text-[#0be5a9]"
                  isPulsating={!visitedTenants.has('app')}
                />
              </div>
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center group-hover:scale-110 transition-transform">
                  <Building2 className="w-6 h-6 text-[#0be5a9]" />
                </div>
                <div className="min-h-[60px] flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-white">Enterprise AI Suite</h3>
                  <p className="text-gray-400">Powering the <span className="text-white font-bold">Future</span> of <span className="text-white font-bold">Insurance</span></p>
                </div>
              </div>
              <div className="flex-grow"></div>
              <button className="flex items-center justify-start gap-2 text-[#0be5a9] mt-auto w-full group-hover:text-white transition-colors">
                <span className="group-hover:underline transition-all">Explore Enterprise Solutions</span>
              </button>
            </div>

            <div
              className="relative bg-[#1a2436] rounded-xl p-8 border border-white/10 hover:border-[#0be5a9]/50 transition-all group cursor-pointer flex flex-col"
              onClick={() => setSelectedTenant('test')}
            >
              <div className="absolute top-4 right-4">
                <PulsatingCircle
                  className="text-[#0be5a9]"
                  isPulsating={!visitedTenants.has('test')}
                />
              </div>
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center group-hover:scale-110 transition-transform">
                  <Users className="w-6 h-6 text-[#0be5a9]" />
                </div>
                <div className="min-h-[60px] flex flex-col justify-center">
                  <h3 className="text-xl font-semibold text-white">AI Risk & Compliance Lab</h3>
                  <p className="text-gray-400"><span className="text-white font-bold">AI-driven</span> Environment & Validation</p>
                </div>
              </div>
              <div className="flex-grow"></div>
              <button className="flex items-center justify-start gap-2 text-[#0be5a9] mt-auto w-full group-hover:text-white transition-colors">
                <span className="group-hover:underline transition-all">Learn More</span>
              </button>
            </div>
          </div>

          <div className="lg:row-span-2 bg-[#1a2436] rounded-xl p-8 border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-6">Available Tools</h3>
            <div className="grid grid-cols-2 gap-4">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className="p-4 rounded-lg bg-[#141b2b] hover:bg-[#f69323]/10 hover:shadow-[0_0_15px_rgba(246,147,35,0.3)] transition-all group cursor-pointer relative flex flex-col items-center h-[140px]"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <div className="absolute top-4 right-4">
                    <PulsatingCircle
                      className="text-[#f69323]"
                      isPulsating={!visitedCategories.has(category.id)}
                    />
                  </div>
                  <div className="flex-1 flex flex-col items-center justify-center text-center w-full pt-4">
                    <category.icon className="w-6 h-6 text-[#f69323] mb-2 group-hover:scale-110 transition-transform" />
                    <p className="text-sm text-white font-medium whitespace-pre-line">{category.name}</p>
                  </div>
                </div>
              ))}
              <div
                className="p-4 rounded-lg bg-[#141b2b] border-2 border-dashed border-[#f69323]/20 flex flex-col items-center justify-center text-center group hover:bg-[#f69323]/5 transition-all cursor-pointer h-[140px]"
              >
                <div className="flex-1 flex flex-col items-center justify-center">
                    <Plus className="w-6 h-6 text-[#f69323] mb-2 group-hover:scale-110 transition-transform" />
                    <p className="text-sm text-[#f69323]">And More</p>
                </div>
              </div>
            </div>
          </div>

          <div className="lg:col-span-2 bg-[#1a2436] rounded-xl p-8 border border-white/10 relative overflow-hidden group flex flex-col">
            <div className="relative z-10 flex flex-col flex-grow">
              <h3 className="text-2xl font-bold text-white mb-2">
                AI-Powered Efficiency – Work Smarter, Not Harder
              </h3>
              <p className="text-gray-400 mb-6 flex-grow">
                Revolutionizing <span className="text-white font-bold">insurance</span> operations with AI. <span className="text-white font-bold">Reduce inefficiencies</span>, automate workflows, and unlock smarter <span className="text-white font-bold">decision-making—without</span> replacing people.
              </p>
              <Link href="/login" legacyBehavior>
                <a className="flex items-center gap-2 text-[#0be5a9] hover:gap-4 transition-all mt-auto w-fit">
                  Discover How AI Empowers Your Team <ArrowRight className="w-4 h-4" />
                </a>
              </Link>
            </div>
            <div className="absolute right-0 bottom-0 w-64 h-64 bg-gradient-to-tl from-[#0be5a9]/20 to-transparent rounded-full transform translate-x-1/2 translate-y-1/2 group-hover:scale-110 transition-transform pointer-events-none" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;