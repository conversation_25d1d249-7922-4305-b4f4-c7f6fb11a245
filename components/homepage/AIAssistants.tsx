'use client'

import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap, Calendar } from 'lucide-react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>on } from "@/components/ui/calendly-button"
import React from 'react';

const assistants = [
  {
    name: "Complex Claims Analysis",
    headline: "Comprehensive Legal Case Intelligence",
    description: "Our Complex Claims Analysis tool provides comprehensive legal case analysis based on claim descriptions. It leverages litigation court cases, legal frameworks, and judge intelligence to generate strategic assistance and arguments, with clickable legal citations that open source documents in a new tab.",
    features: [
      "Search historic cases by URL and find similar claims by description",
      "Generate legal arguments with context-aware suggestions",
      "Access judge intelligence and legal framework analysis"
    ],
    color: "bg-red-500",
    image: "/landingpage/fraud.png"
  },
  {
    name: "OCR & Document Analysis",
    headline: "Extract Insights from Any Document",
    description: "Our advanced OCR solution extracts critical information from invoices, forms, and documents with high accuracy. It integrates with external data sources to validate information and detect inconsistencies, providing a comprehensive view of document authenticity and content.",
    features: [
      "Extract structured data from unstructured documents",
      "Validate information against external databases",
      "Detect document manipulation and inconsistencies"
    ],
    color: "bg-blue-500",
    image: "/landingpage/repairman.png"
  },
  {
    name: "Fraud Detection Network",
    headline: "Uncover Hidden Fraud Patterns",
    description: "Our Fraud Detection Network leverages extensive external data sources including vendor reputation, social security information, financial debtors lists, and network analysis to identify potential fraud. It connects seemingly unrelated data points to reveal hidden relationships and fraud patterns.",
    features: [
      "Analyze network connections between entities and individuals",
      "Leverage geo risk indexes and litigation history",
      "Integrate with social security and financial databases"
    ],
    color: "bg-green-500",
    image: "/landingpage/support.png"
  }
]

export default function AIAssistants() {
  return (
    <section className="py-16 bg-gradient-to-b from-gray-800 to-gray-700 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            Expert-Built AI Solutions
          </h2>
          <p className="mt-4 text-xl text-gray-200 max-w-3xl mx-auto">
            Rekover's AI solutions are developed by experts in AI and insurance, trained with a curated knowledge base built by insurance professionals. Our marketplace features are available via SaaS and API, leveraging extensive external data sources for comprehensive insights.
          </p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-3">
          {assistants.map((assistant, index) => (
            <Card
              key={assistant.name}
              className={`bg-gray-800 border-gray-700 overflow-hidden`}
            >
              <CardHeader>
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <Image
                    src={assistant.image}
                    alt={assistant.name}
                    width={80}
                    height={80}
                    className={`rounded-full ${assistant.color}`}
                  />
                </div>
                <CardTitle className="text-2xl font-bold text-white">{assistant.name}</CardTitle>
                <CardDescription className="text-gray-300">{assistant.headline}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-200 mb-4">{assistant.description}</p>
                <ul className="space-y-2">
                  {assistant.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <Zap className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-1" />
                      <span className="text-gray-200">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-white mb-4">
            Marketplace of Pre-Built Solutions for Insurance
          </h3>
          <p className="text-gray-200 mb-8 max-w-3xl mx-auto">
            Our marketplace features range from OCR tools to extract information from invoices to advanced fraud detection tools and complex claims assistants. All solutions leverage extensive external data sources including vendor reputation, litigation court cases, fraud and risk geo indexes, social security data, and network analysis.
          </p>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="inline-block">
            <CalendlyButton
              className="bg-[#f69323] text-white px-6 py-3 rounded-full font-medium hover:bg-[#f69323]/90 transition-colors"
              calendlyUrl="https://calendly.com/francisco-rekover/30min?primary_color=e3af13"
            >
              <Calendar className="w-4 h-4 mr-2" /> Book a Demo
            </CalendlyButton>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

