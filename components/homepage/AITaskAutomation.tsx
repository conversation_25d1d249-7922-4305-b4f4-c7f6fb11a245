import React from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Check<PERSON>ir<PERSON>, <PERSON>, Brain } from 'lucide-react'

const automatedTasks = [
  "Market Evaluation",
  "Check Debtor",
  "Claim Acceptance",
  "Car Appraisal",
  "Network Analysis",
  "Check Insurer",
  "Vehicle Specs",
  "OSINT",
  "Home Appraisal",
  "Image Analyser",
  "Weather",
  "Identity Check",
  "FNOL Speech-to-Text",
  "Commercial Due Diligence",
  "Appraisal Audit",
  "Satellite Imagery",
  "OCR",
  "Double Insurance"
]

export default function AITaskAutomation() {
  return (
    <section className="py-24 bg-gradient-to-b from-gray-100 to-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Empower Your Claim Handlers with AI Automation
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Let AI handle repetitive tasks, freeing your team to focus on strategic decision-making and complex cases
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <Card className="bg-white shadow-xl">
              <CardHeader>
                <CardTitle className="text-2xl font-bold">AI-Powered Task Automation</CardTitle>
                <CardDescription>Streamline your claims process with these automated tools</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="grid grid-cols-2 gap-4">
                  {automatedTasks.map((task, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-sm text-gray-700">{task}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-8">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <Clock className="h-12 w-12 text-blue-500" />
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Save Time on Repetitive Tasks</h3>
                <p className="mt-2 text-gray-600">
                  AI automation handles time-consuming, repetitive tasks, reducing processing time by up to 80% and allowing your team to manage a higher volume of claims efficiently.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <Brain className="h-12 w-12 text-purple-500" />
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Focus on Strategic Decision Making</h3>
                <p className="mt-2 text-gray-600">
                  With routine tasks automated, your claim handlers can dedicate more time to complex cases, risk assessment, and customer interaction, improving overall claim quality and customer satisfaction.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <CheckCircle className="h-12 w-12 text-green-500" />
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">Enhance Accuracy and Consistency</h3>
                <p className="mt-2 text-gray-600">
                  AI-driven processes ensure consistent application of rules and criteria across all claims, reducing human error and improving the overall accuracy of claim assessments.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

