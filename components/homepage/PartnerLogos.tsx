import Image from 'next/image'

const partners = [
  { name: 'Partner 1', logo: '/placeholder.svg?height=60&width=120&text=Partner+1' },
  { name: 'Partner 2', logo: '/placeholder.svg?height=60&width=120&text=Partner+2' },
  { name: 'Partner 3', logo: '/placeholder.svg?height=60&width=120&text=Partner+3' },
  { name: 'Partner 4', logo: '/placeholder.svg?height=60&width=120&text=Partner+4' },
  { name: 'Partner 5', logo: '/placeholder.svg?height=60&width=120&text=Partner+5' },
  { name: 'Partner 6', logo: '/placeholder.svg?height=60&width=120&text=Partner+6' },
  { name: 'Partner 7', logo: '/placeholder.svg?height=60&width=120&text=Partner+7' },
  { name: 'Partner 8', logo: '/placeholder.svg?height=60&width=120&text=Partner+8' },
]

export function PartnerLogos() {
  return (
    <div className="w-full overflow-hidden bg-white py-12">
      <div className="relative w-full">
        <div className="animate-slide flex">
          {[...partners, ...partners].map((partner, index) => (
            <div key={index} className="flex-shrink-0 w-[200px] mx-8">
              <Image
                src={partner.logo}
                alt={partner.name}
                width={120}
                height={60}
                className="object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

