"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { FileUploader } from "@/components/file-uploader"
import { Loader2, FileAudio, AlertTriangle, Clock, VolumeX, Volume2 } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ClaimDetails {
  date: string
  location: string
  vehiclesInvolved: number
  injuries: string
  policyNumber: string
  claimantName: string
}

interface DetectedFlag {
  flag: string
  confidence: number
}

interface CallAnalysis {
  languageType: string
  sentiment: string
  clarity: string
  totalCallTime: string
  silencePeriods: number
  averageWordSpeed: number
}

interface FNOLResult {
  transcription: string
  claimDetails: ClaimDetails
  detectedFlags: DetectedFlag[]
  callAnalysis: CallAnalysis
}

export function FNOLSpeechToTextTool() {
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<FNOLResult | null>(null)
  const [progress, setProgress] = useState(0)

  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      setAudioFile(files[0])
    }
  }

  const handleProcess = async () => {
    if (!audioFile) return

    setIsProcessing(true)
    setResult(null)
    setProgress(0)

    try {
      // Simulate processing steps
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      // Mock result
      const mockResult: FNOLResult = {
        transcription: "Hello, this is John Smith. I'm calling to report a car accident that happened on July 15th at the intersection of Main St and 5th Ave in New York. There were two vehicles involved, and I have some minor injuries. My policy number is POL-12345. I'm really frustrated with how this has been handled so far, and I'm considering speaking to a lawyer about this incident.",
        claimDetails: {
          date: "2023-07-15",
          location: "Intersection of Main St and 5th Ave, New York, NY",
          vehiclesInvolved: 2,
          injuries: "Minor",
          policyNumber: "POL-12345",
          claimantName: "John Smith"
        },
        detectedFlags: [
          { flag: "Potential Fraud", confidence: 0.15 },
          { flag: "Potential Litigation", confidence: 0.65 },
          { flag: "Unhappy Customer", confidence: 0.85 }
        ],
        callAnalysis: {
          languageType: "Direct",
          sentiment: "Negative",
          clarity: "High",
          totalCallTime: "3:45",
          silencePeriods: 2,
          averageWordSpeed: 130
        }
      }

      setResult(mockResult)
      toast({
        title: "FNOL Processing Complete",
        description: "The audio has been transcribed and analyzed successfully.",
      })
    } catch (error) {
      toast({
        title: "Processing Failed",
        description: "There was an error processing the audio file. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>FNOL Speech-to-Text Tool</CardTitle>
        <CardDescription>Upload an audio recording of the FNOL call for transcription and analysis</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <FileUploader
            id="audio-upload"
            accept="audio/*"
            onFilesSelected={handleFileUpload}
          />
          <Button onClick={handleProcess} disabled={isProcessing || !audioFile}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <FileAudio className="mr-2 h-4 w-4" />
                Process Audio
              </>
            )}
          </Button>

          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground">Processing audio... {progress}%</p>
            </div>
          )}

          {result && (
            <div className="mt-6 space-y-4">
              <Tabs defaultValue="transcription" className="w-full">
                <TabsList>
                  <TabsTrigger value="transcription">Transcription</TabsTrigger>
                  <TabsTrigger value="claim-details">Claim Details</TabsTrigger>
                  <TabsTrigger value="flags">Detected Flags</TabsTrigger>
                  <TabsTrigger value="call-analysis">Call Analysis</TabsTrigger>
                </TabsList>
                <TabsContent value="transcription">
                  <Card>
                    <CardHeader>
                      <CardTitle>Transcription</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm bg-muted p-4 rounded-md">{result.transcription}</p>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="claim-details">
                  <Card>
                    <CardHeader>
                      <CardTitle>Extracted Claim Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableBody>
                          {Object.entries(result.claimDetails).map(([key, value]) => (
                            <TableRow key={key}>
                              <TableCell className="font-medium">{key.charAt(0).toUpperCase() + key.slice(1)}</TableCell>
                              <TableCell>{value}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="flags">
                  <Card>
                    <CardHeader>
                      <CardTitle>Detected Flags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Flag</TableHead>
                            <TableHead>Confidence</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {result.detectedFlags.map((flag, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <div className="flex items-center">
                                  <AlertTriangle className="mr-2 h-4 w-4 text-yellow-500" />
                                  {flag.flag}
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={flag.confidence > 0.5 ? "destructive" : "default"}>
                                  {(flag.confidence * 100).toFixed(2)}%
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="call-analysis">
                  <Card>
                    <CardHeader>
                      <CardTitle>Call Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableBody>
                          <TableRow>
                            <TableCell className="font-medium">Language Type</TableCell>
                            <TableCell>{result.callAnalysis.languageType}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Sentiment</TableCell>
                            <TableCell>
                              <Badge variant={result.callAnalysis.sentiment === "Negative" ? "destructive" : "default"}>
                                {result.callAnalysis.sentiment}
                              </Badge>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Clarity</TableCell>
                            <TableCell>{result.callAnalysis.clarity}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Total Call Time</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Clock className="mr-2 h-4 w-4" />
                                {result.callAnalysis.totalCallTime}
                              </div>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Silence Periods</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <VolumeX className="mr-2 h-4 w-4" />
                                {result.callAnalysis.silencePeriods}
                              </div>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">Average Word Speed</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Volume2 className="mr-2 h-4 w-4" />
                                {result.callAnalysis.averageWordSpeed} words/minute
                              </div>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

