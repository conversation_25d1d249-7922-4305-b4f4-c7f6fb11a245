import { Card, CardContent } from "@/components/ui/card"

interface MockPDFViewerProps {
  highlightedText: string;
}

export function MockPDFViewer({ highlightedText }: MockPDFViewerProps) {
  return (
    <Card className="w-full mt-4">
      <CardContent className="p-4">
        <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
          <p className="mb-2">Policy Document</p>
          <p className="mb-2">
            1. Introduction
            <br />
            2. Coverage Details
            <br />
            3. Claims Process
          </p>
          <p>
            3.1 Filing a Claim
            <br />
            <span className="bg-yellow-200">
              3.2 All claims submitted within 30 days of the incident are eligible for expedited processing.
            </span>
            <br />
            3.3 Required Documentation
          </p>
          <p className="mt-2">
            4. Exclusions
            <br />
            5. Terms and Conditions
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

