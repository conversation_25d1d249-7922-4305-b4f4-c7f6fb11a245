import { Lightbulb, HeartHandshake, Award, Users } from 'lucide-react';
import Image from 'next/image';

export default function ValuesSection() {
  const valuesList = [
    {
      icon: <Lightbulb className="w-8 h-8 text-[#0be5a9]" />,
      title: "Innovation First",
      description: "We constantly push boundaries to create AI solutions that transform insurance operations."
    },
    {
      icon: <HeartHandshake className="w-8 h-8 text-[#0be5a9]" />,
      title: "Human-Centered AI",
      description: "We build technology that empowers insurance professionals, not replaces them."
    },
    {
      icon: <Award className="w-8 h-8 text-[#0be5a9]" />,
      title: "Excellence in Execution",
      description: "We hold ourselves to the highest standards in product development and customer success."
    },
    {
      icon: <Users className="w-8 h-8 text-[#0be5a9]" />,
      title: "Collaborative Spirit",
      description: "We work closely with our clients to solve real challenges and drive meaningful outcomes."
    }
  ];

  return (
    <section className="py-16 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="animate-fadeIn">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Our Core Values</h2>
            <p className="text-gray-400 max-w-3xl mx-auto">
              These principles guide everything we do—from product development to customer relationships.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {valuesList.map((value, index) => (
              <div key={index} className="bg-[#1a2436] p-8 rounded-xl border border-white/10 hover:border-[#0be5a9]/50 transition-all">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-16 h-16 rounded-lg bg-[#0be5a9]/10 flex items-center justify-center">
                    {value.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-white">{value.title}</h3>
                </div>
                <p className="text-gray-300">{value.description}</p>
              </div>
            ))}
          </div>

          <div className="bg-[#1a2436] p-10 rounded-2xl border border-white/10 mt-16">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold text-white mb-4">Our Mission</h3>
                <p className="text-gray-300 mb-6">
                  To transform the insurance industry by providing AI solutions that detect fraud, streamline operations, and empower human decision-makers with data-driven insights.
                </p>
                <h3 className="text-2xl font-bold text-white mb-4">Our Vision</h3>
                <p className="text-gray-300">
                  A future where insurance companies can operate with unprecedented efficiency and accuracy, leading to faster claims processing, fewer fraudulent payouts, and better customer experiences.
                </p>
              </div>
              <div className="relative h-80 rounded-xl overflow-hidden">
                <Image 
                  src="/landingpage/work.jpg" 
                  alt="Company culture" 
                  fill 
                  style={{objectFit: "cover"}}
                  className="rounded-xl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 