"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { FileUploader } from "@/components/file-uploader"
import { Loader2, Home } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface Damage {
  description: string
  severity: 'Low' | 'Medium' | 'High'
  room: string
}

interface RepairItem {
  item: string
  action: 'Replace' | 'Repair'
  hours: number
  materialCost: number
}

interface AppraisalResult {
  damages: Damage[]
  repairItems: RepairItem[]
  laborCost: number
  materialCost: number
  totalCost: number
}

export function HomeAppraisalTool() {
  const [homeDetails, setHomeDetails] = useState({
    address: '',
    yearBuilt: '',
    constructionType: '',
  })
  const [mediaFiles, setMediaFiles] = useState<File[]>([])
  const [isAppraising, setIsAppraising] = useState(false)
  const [appraisalResult, setAppraisalResult] = useState<AppraisalResult | null>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHomeDetails({ ...homeDetails, [e.target.name]: e.target.value })
  }

  const handleFileUpload = (files: File[]) => {
    setMediaFiles(files)
  }

  const handleAppraisal = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsAppraising(true)
    setAppraisalResult(null)

    try {
      await new Promise(resolve => setTimeout(resolve, 2000))

      const mockResult: AppraisalResult = {
        damages: [
          { description: "Water damage on ceiling", severity: "High", room: "Kitchen" },
          { description: "Cracked wall", severity: "Medium", room: "Living Room" },
          { description: "Broken window", severity: "Low", room: "Bedroom" }
        ],
        repairItems: [
          { item: "Ceiling repair", action: "Repair", hours: 4, materialCost: 200 },
          { item: "Wall repair", action: "Repair", hours: 2, materialCost: 100 },
          { item: "Window replacement", action: "Replace", hours: 1, materialCost: 300 }
        ],
        laborCost: 75 * (4 + 2 + 1),
        materialCost: 200 + 100 + 300,
        totalCost: 75 * (4 + 2 + 1) + (200 + 100 + 300)
      }

      setAppraisalResult(mockResult)
      toast({
        title: "Appraisal Complete",
        description: "Your home has been successfully appraised.",
      })
    } catch (error) {
      toast({
        title: "Appraisal Failed",
        description: "There was an error appraising your home. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsAppraising(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Home Appraisal Tool</CardTitle>
        <CardDescription>
          Upload photos or video of your home for a detailed damage assessment and repair estimate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleAppraisal} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                value={homeDetails.address}
                onChange={handleInputChange}
                placeholder="e.g., 123 Main St"
                required
              />
            </div>
            <div>
              <Label htmlFor="yearBuilt">Year Built</Label>
              <Input
                id="yearBuilt"
                name="yearBuilt"
                type="number"
                value={homeDetails.yearBuilt}
                onChange={handleInputChange}
                placeholder="e.g., 2000"
                required
              />
            </div>
            <div>
              <Label htmlFor="constructionType">Construction Type</Label>
              <Input
                id="constructionType"
                name="constructionType"
                value={homeDetails.constructionType}
                onChange={handleInputChange}
                placeholder="e.g., Brick"
                required
              />
            </div>
          </div>
          <div>
            <Label htmlFor="media">Upload Photos or Video</Label>
            <FileUploader
              id="media"
              accept="image/*,video/*"
              onFilesSelected={handleFileUpload}
              multiple
            />
          </div>
          <Button type="submit" disabled={isAppraising || mediaFiles.length === 0}>
            {isAppraising ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Appraising...
              </>
            ) : (
              <>
                <Home className="mr-2 h-4 w-4" />
                Appraise Home
              </>
            )}
          </Button>
        </form>

        {appraisalResult && (
          <div className="mt-6 space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Damage Assessment</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Room</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Severity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {appraisalResult.damages.map((damage, index) => (
                    <TableRow key={index}>
                      <TableCell>{damage.room}</TableCell>
                      <TableCell>{damage.description}</TableCell>
                      <TableCell>{damage.severity}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Repair Recommendations</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Labor Hours</TableHead>
                    <TableHead>Material Cost</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {appraisalResult.repairItems.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.item}</TableCell>
                      <TableCell>{item.action}</TableCell>
                      <TableCell>{item.hours}</TableCell>
                      <TableCell>${item.materialCost.toFixed(2)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Cost Breakdown</h3>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell>Labor Cost</TableCell>
                    <TableCell>${appraisalResult.laborCost.toFixed(2)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Materials Cost</TableCell>
                    <TableCell>${appraisalResult.materialCost.toFixed(2)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-semibold">Total Estimated Cost</TableCell>
                    <TableCell className="font-semibold">${appraisalResult.totalCost.toFixed(2)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

