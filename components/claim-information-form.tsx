"use client"

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ClaimInformation {
  claimNumber: string;
  claimantName: string;
  claimDate: string;
  claimType: string;
  claimAmount: string;
  policyType: string;
  incidentDescription: string;
}

interface ClaimInformationFormProps {
  onSubmit: (claimInfo: ClaimInformation) => void;
}

export function ClaimInformationForm({ onSubmit }: ClaimInformationFormProps) {
  const [claimInfo, setClaimInfo] = useState<ClaimInformation>({
    claimNumber: '',
    claimantName: '',
    claimDate: '',
    claimType: '',
    claimAmount: '',
    policyType: '',
    incidentDescription: '',
  });

  const handleChange = (field: keyof ClaimInformation, value: string) => {
    setClaimInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(claimInfo);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="claimNumber">Claim Number</Label>
          <Input
            id="claimNumber"
            value={claimInfo.claimNumber}
            onChange={(e) => handleChange('claimNumber', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="claimantName">Claimant Name</Label>
          <Input
            id="claimantName"
            value={claimInfo.claimantName}
            onChange={(e) => handleChange('claimantName', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="claimDate">Claim Date</Label>
          <Input
            id="claimDate"
            type="date"
            value={claimInfo.claimDate}
            onChange={(e) => handleChange('claimDate', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="claimType">Claim Type</Label>
          <Select value={claimInfo.claimType} onValueChange={(value) => handleChange('claimType', value)}>
            <SelectTrigger id="claimType">
              <SelectValue placeholder="Select claim type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="auto">Auto</SelectItem>
              <SelectItem value="property">Property</SelectItem>
              <SelectItem value="liability">Liability</SelectItem>
              <SelectItem value="health">Health</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="claimAmount">Claim Amount</Label>
          <Input
            id="claimAmount"
            type="number"
            value={claimInfo.claimAmount}
            onChange={(e) => handleChange('claimAmount', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="policyType">Policy Type</Label>
          <Select value={claimInfo.policyType} onValueChange={(value) => handleChange('policyType', value)}>
            <SelectTrigger id="policyType">
              <SelectValue placeholder="Select policy type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="individual">Individual</SelectItem>
              <SelectItem value="group">Group</SelectItem>
              <SelectItem value="commercial">Commercial</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div>
        <Label htmlFor="incidentDescription">Incident Description</Label>
        <Input
          id="incidentDescription"
          value={claimInfo.incidentDescription}
          onChange={(e) => handleChange('incidentDescription', e.target.value)}
          required
        />
      </div>
      <Button type="submit">Submit Claim Information</Button>
    </form>
  );
}

