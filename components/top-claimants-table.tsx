import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const topClaimants = [
  { name: "<PERSON>", claimCount: 5, totalAmount: 25000 },
  { name: "<PERSON>", claimCount: 4, totalAmount: 20000 },
  { name: "<PERSON>", claimCount: 3, totalAmount: 15000 },
  { name: "<PERSON>", claimCount: 3, totalAmount: 12000 },
  { name: "<PERSON>", claimCount: 2, totalAmount: 10000 },
]

export default function TopClaimantsTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Claim Count</TableHead>
          <TableHead>Total Amount</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {topClaimants.map((claimant) => (
          <TableRow key={claimant.name}>
            <TableCell>{claimant.name}</TableCell>
            <TableCell>{claimant.claimCount}</TableCell>
            <TableCell>${claimant.totalAmount.toLocaleString()}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

