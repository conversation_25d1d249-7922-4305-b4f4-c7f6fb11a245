"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Search, FileText, AlertCircle } from 'lucide-react'
import { format } from "date-fns"
import { DateRange } from "react-day-picker"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import Link from 'next/link'

// Define the structure of a claim according to the API
interface Claim {
  id: string
  claim_no: string
  claim_amount: number
  claim_type: string
  claim_status: "Open" | "Closed" | "In Review" | "Pending"
  claim_date: string
  modelLabels?: { model: string, label: string }[] // Optional model labels
}
 
// API URL
const API_URL = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/claims`

export default function ClaimsBrowseScreen() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>()
  const [selectedType, setSelectedType] = useState<string | undefined>()
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [amountRange, setAmountRange] = useState<[number, number]>([0, 50000])
  const [claims, setClaims] = useState<Claim[]>([])
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>(claims)
  const [selectedModelLabel, setSelectedModelLabel] = useState<string | undefined>() // Model label filter
  const [loading, setLoading] = useState(true)

  // Fetch claims from the API
  useEffect(() => {
    const fetchClaims = async () => {
      try {
        const response = await fetch(API_URL)
        const data = await response.json()

        // Transform API data to match the expected structure
        const transformedClaims = data.map((claim: any) => ({
          id: claim.id,
          claim_no: claim.claim_no,
          claim_amount: claim.claim_amount,
          claim_type: claim.claim_type,
          claim_status: claim.claim_status as "Open" | "Closed" | "In Review" | "Pending",
          claim_date: new Date(claim.claim_date),
          modelLabels: claim.internal_tags ? [{ model: "Internal", label: claim.internal_tags }] : [],
        }))

        setClaims(transformedClaims)
        setFilteredClaims(transformedClaims)
        setLoading(false)
      } catch (error) {
        console.error("Error fetching claims:", error)
        setLoading(false)
      }
    }

    fetchClaims()
  }, [])

  const handleSearch = () => {
    const filtered = claims.filter((claim) => {
      const matchesSearch =
        claim.claim_no.toLowerCase().includes(searchQuery.toLowerCase()) ||
        claim.claim_type.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = !selectedStatus || claim.claim_status === selectedStatus;
      const matchesType = !selectedType || claim.claim_type === selectedType;
      
      const matchesDateRange =
        !dateRange ||
        !dateRange.from ||
        !dateRange.to ||
        (new Date(claim.claim_date) >= new Date(dateRange.from) &&
          new Date(claim.claim_date) <= new Date(dateRange.to));
  
      const matchesAmountRange =
        claim.claim_amount >= amountRange[0] && claim.claim_amount <= amountRange[1];
      const matchesModelLabel = !selectedModelLabel || claim.modelLabels?.some(({ label }) => label === selectedModelLabel);
  
      return matchesSearch && matchesStatus && matchesType && matchesDateRange && matchesAmountRange && matchesModelLabel;
    });
  
    setFilteredClaims(filtered);
  };
  

  // Table columns
  const columns: ColumnDef<Claim>[] = [
    {
      accessorKey: "claim_no",
      header: "Claim Number",
      cell: ({ row }) => {
        const claim = row.original
        return (
          <Link href={`/dashboard/claim-details/${claim.id}`} className="text-blue-500 hover:underline">
            {claim.claim_no}
          </Link>
        )
      },
    },
    {
      accessorKey: "claim_type",
      header: "Claim Type",
    },
    {
      accessorKey: "claim_status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("claim_status") as string
        return (
          <Badge variant={status === "Open" ? "default" : status === "Closed" ? "secondary" : status === "Pending" ? "outline" : "warning"}>
            {status}
          </Badge>
        )
      },
    },
    {
      accessorKey: "claim_amount",
      header: "Amount",
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("claim_amount"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)
        return <div className="font-medium">{formatted}</div>
      },
    },
    {
      accessorKey: "claim_date",
      header: "Date Submitted",
      cell: ({ row }) => {
        const date = row.getValue("claim_date") as Date
        return <div>{format(date, "PP")}</div>
      },
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Claims</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search claims..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Open">Open</SelectItem>
                <SelectItem value="Closed">Closed</SelectItem>
                <SelectItem value="In Review">In Review</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Claim Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Auto">Auto</SelectItem>
                <SelectItem value="Home">Home</SelectItem>
                <SelectItem value="Life">Life</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedModelLabel} onValueChange={setSelectedModelLabel}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Model Label" />
              </SelectTrigger>
              <SelectContent>
                {/* Add model label options */}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-4">
            <div>
              <Label htmlFor="amount-range">Amount Range</Label>
              <Slider
                id="amount-range"
                min={0}
                max={50000}
                step={1000}
                value={amountRange}
                onValueChange={(value) =>
                  setAmountRange([value[0], value[1]] as [number, number])
                }
                className="mt-2"
              />
              <div className="flex justify-between mt-2 text-sm text-muted-foreground">
                <span>${amountRange[0].toLocaleString()}</span>
                <span>${amountRange[1].toLocaleString()}</span>
              </div>
            </div>
            <div>
              <Label>Date Submitted Range</Label>
              <DatePickerWithRange
                dateRange={dateRange}
                setDateRange={setDateRange}
              />
            </div>
          </div>
          <Button onClick={handleSearch} className="w-full sm:w-auto">
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
        </div>
        {loading ? (
          <div>Loading...</div>
        ) : filteredClaims.length > 0 ? (
          <div className="mt-6">
            <DataTable columns={columns} data={filteredClaims} />
          </div>
        ) : (
          <Alert className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No results</AlertTitle>
            <AlertDescription>
              No claims match your search criteria. Try adjusting your filters.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
