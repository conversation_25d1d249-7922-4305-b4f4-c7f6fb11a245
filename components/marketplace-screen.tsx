"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MarketplaceFeatureCard } from "./marketplace-feature-card"
import { CategoryOverview } from "./marketplace/category-overview"
import { Search, PenToolIcon as Tool, Brain, GitBranch, BarChart } from 'lucide-react'

type MarketplaceItem = {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  price: number;
  billingCycle: string;
  features: string[];
  metrics: {
    users: number;
    rating: number;
    reviews: number;
  };
  status: "available" | "purchased";
};
// Mock data for marketplace items
const marketplaceItems = {
  tools: [
    {
      id: "tool-1",
      name: "Market Evaluation Tool",
      description: "Analyze market trends and competitor pricing",
      category: "Tools",
      subcategory: "Market Analysis",
      price: 49.99,
      billingCycle: "monthly",
      features: [
        "Real-time market data",
        "Competitor analysis",
        "Price trend tracking",
        "Custom reports"
      ],
      metrics: {
        users: 1250,
        rating: 4.8,
        reviews: 89
      },
      status: "available"
    },
    {
      id: "tool-2",
      name: "Network Analysis Tool",
      description: "Identify connections and patterns in claims data",
      category: "Tools",
      subcategory: "Analysis",
      price: 79.99,
      billingCycle: "monthly",
      features: [
        "Interactive network graphs",
        "Pattern detection",
        "Risk scoring",
        "Export capabilities"
      ],
      metrics: {
        users: 850,
        rating: 4.6,
        reviews: 62
      },
      status: "purchased"
    }
  ],
  models: [
    {
      id: "model-1",
      name: "Claims Segmentation Model",
      description: "AI-powered claims classification and routing",
      category: "Models",
      subcategory: "Classification",
      price: 199.99,
      billingCycle: "monthly",
      features: [
        "Automatic classification",
        "Priority scoring",
        "Route optimization",
        "Performance analytics"
      ],
      metrics: {
        users: 2100,
        rating: 4.9,
        reviews: 156
      },
      status: "available"
    },
    {
      id: "model-2",
      name: "Fraud Detection Model",
      description: "Advanced fraud detection using machine learning",
      category: "Models",
      subcategory: "Detection",
      price: 299.99,
      billingCycle: "monthly",
      features: [
        "Real-time detection",
        "Pattern learning",
        "Risk scoring",
        "Alert system"
      ],
      metrics: {
        users: 1800,
        rating: 4.7,
        reviews: 134
      },
      status: "purchased"
    }
  ],
  workflows: [
    {
      id: "workflow-1",
      name: "Claims Processing Workflow",
      description: "Automated end-to-end claims processing",
      category: "Workflows",
      subcategory: "Processing",
      price: 149.99,
      billingCycle: "monthly",
      features: [
        "Document processing",
        "Status tracking",
        "Automated notifications",
        "Integration capabilities"
      ],
      metrics: {
        users: 1500,
        rating: 4.8,
        reviews: 112
      },
      status: "available"
    },
    {
      id: "workflow-2",
      name: "Policy Renewal Workflow",
      description: "Streamlined policy renewal process",
      category: "Workflows",
      subcategory: "Policy Management",
      price: 129.99,
      billingCycle: "monthly",
      features: [
        "Automatic notifications",
        "Document generation",
        "Payment processing",
        "Status tracking"
      ],
      metrics: {
        users: 1200,
        rating: 4.6,
        reviews: 98
      },
      status: "purchased"
    }
  ],
  "project-management": [
    {
      id: "pm-1",
      name: "Fraud Investigation Suite",
      description: "Comprehensive fraud investigation tools",
      category: "Project Management",
      subcategory: "Fraud",
      price: 399.99,
      billingCycle: "monthly",
      features: [
        "Case management",
        "Evidence tracking",
        "Collaboration tools",
        "Report generation"
      ],
      metrics: {
        users: 900,
        rating: 4.7,
        reviews: 76
      },
      status: "available"
    },
    {
      id: "pm-2",
      name: "Leakage Management System",
      description: "Track and prevent insurance leakage",
      category: "Project Management",
      subcategory: "Leakage",
      price: 349.99,
      billingCycle: "monthly",
      features: [
        "Leakage detection",
        "Cost tracking",
        "Prevention tools",
        "Analytics dashboard"
      ],
      metrics: {
        users: 750,
        rating: 4.5,
        reviews: 64
      },
      status: "purchased"
    }
  ]
}

const sortOptions = ["Popular", "Price: Low to High", "Price: High to Low", "Rating"]

export default function MarketplaceScreen() {
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("Popular")

  const filterAndSortItems = (items: MarketplaceItem[] = []): MarketplaceItem[] => {
    if (!items.length) return [];
    
    // Filter based on search query
    const filtered = items.filter((item) =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  
    // Sort items based on selected option
    return [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "Price: Low to High":
          return a.price - b.price;
        case "Price: High to Low":
          return b.price - a.price;
        case "Rating":
          return b.metrics.rating - a.metrics.rating;
        default:
          return b.metrics.users - a.metrics.users;
      }
    });
  };
  

  const getCategoryMetrics = (category: keyof typeof marketplaceItems) => {
    const items = marketplaceItems[category] || []
    return {
      totalItems: items.length,
      purchasedItems: items.filter(i => i.status === "purchased").length,
      averagePrice: items.length ? items.reduce((acc, curr) => acc + curr.price, 0) / items.length : 0
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Browse Features</CardTitle>
          <CardDescription>
            Discover and activate powerful features for your insurance operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search features..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="tools" className="gap-2">
            <Tool className="h-4 w-4" />
            Tools
          </TabsTrigger>
          <TabsTrigger value="models" className="gap-2">
            <Brain className="h-4 w-4" />
            Models
          </TabsTrigger>
          <TabsTrigger value="workflows" className="gap-2">
            <GitBranch className="h-4 w-4" />
            Workflows
          </TabsTrigger>
          <TabsTrigger value="project-management" className="gap-2">
            <BarChart className="h-4 w-4" />
            Projects
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <CategoryOverview
              title="Tools"
              description="Specialized tools for market analysis, investigation, and data processing"
              icon={Tool}
              items={marketplaceItems.tools as MarketplaceItem[] || []}
              metrics={getCategoryMetrics("tools")}
            />
            <CategoryOverview
              title="Models"
              description="AI-powered models for prediction, classification, and decision support"
              icon={Brain}
              items={marketplaceItems.models as MarketplaceItem[] || []}
              metrics={getCategoryMetrics("models")}
            />
            <CategoryOverview
              title="Workflows"
              description="Automated workflows for streamlined insurance operations"
              icon={GitBranch}
              items={marketplaceItems.workflows as MarketplaceItem[] || []}
              metrics={getCategoryMetrics("workflows")}
            />
            <CategoryOverview
              title="Project Management"
              description="Comprehensive solutions for fraud detection and leakage management"
              icon={BarChart}
              items={marketplaceItems["project-management"] as MarketplaceItem[] || []}
              metrics={getCategoryMetrics("project-management")}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="tools">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filterAndSortItems(marketplaceItems.tools as MarketplaceItem[]).map((item) => (
              <MarketplaceFeatureCard
                key={item.id}
                feature={item}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="models">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filterAndSortItems(marketplaceItems.models as MarketplaceItem[]).map((item) => (
              <MarketplaceFeatureCard
                key={item.id}
                feature={item}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="workflows">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filterAndSortItems(marketplaceItems.workflows as MarketplaceItem[]).map((item) => (
              <MarketplaceFeatureCard
                key={item.id}
                feature={item}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="project-management">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filterAndSortItems(marketplaceItems["project-management"] as MarketplaceItem[]).map((item) => (
              <MarketplaceFeatureCard
                key={item.id}
                feature={item}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

