"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult
} from '@hello-pangea/dnd'
import { GripVertical, Plus, X, ArrowRight } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
// Available tasks that can be added to the workflow
const availableTasks = [
  {
    id: "task-1",
    name: "Document Analysis",
    description: "Extract and validate information from submitted documents",
    category: "Document Processing",
    type: "automated",
    estimatedTime: "2-3 minutes",
    inputs: ["PDF documents", "Image files"],
    outputs: ["Extracted text", "Key-value pairs", "Confidence scores"]
  },
  {
    id: "task-2",
    name: "Risk Assessment",
    description: "Evaluate risk factors and calculate risk scores",
    category: "Risk Management",
    type: "automated",
    estimatedTime: "2-3 minutes",
    inputs: ["Client data", "Historical claims"],
    outputs: ["Risk score", "Risk factors", "Recommendations"]
  },
  {
    id: "task-3",
    name: "Manual Review",
    description: "Human review of processed documents and risk assessment",
    category: "Review",
    type: "manual",
    estimatedTime: "10-15 minutes",
    inputs: ["Processed documents", "Risk assessment report"],
    outputs: ["Review decision", "Notes", "Next actions"]
  },
  {
    id: "task-4",
    name: "Client Communication",
    description: "Generate and send automated updates to client",
    category: "Communication",
    type: "automated",
    estimatedTime: "1-2 minutes",
    inputs: ["Client details", "Status update"],
    outputs: ["Email notification", "SMS notification"]
  },
  {
    id: "task-5",
    name: "Fraud Detection",
    description: "Analyze for potential fraud indicators",
    category: "Fraud",
    type: "automated",
    estimatedTime: "3-4 minutes",
    inputs: ["Claim details", "Document analysis results"],
    outputs: ["Fraud score", "Suspicious patterns", "Recommendations"]
  }
]

interface WorkflowTask {
  id: string
  taskId: string
  name: string
  description: string
  category: string
  type: string
  estimatedTime: string
  inputs: string[]
  outputs: string[]
  config?: Record<string, string>
  linkedAgent?: string | null // Updated: added | null
}

export function WorkflowBuilder() {
  const [workflowName, setWorkflowName] = useState("")
  const [workflowDescription, setWorkflowDescription] = useState("")
  const [selectedTasks, setSelectedTasks] = useState<WorkflowTask[]>([])
  const [isTaskSelectorOpen, setIsTaskSelectorOpen] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const [availableAgents] = useState([
    { id: "agent1", name: "Claims Adjuster Agent" },
    { id: "agent2", name: "Fraud Detection Agent" },
    { id: "agent3", name: "Underwriting Agent" },
    { id: "agent4", name: "Customer Service Agent" },
  ])
  const { toast } = useToast()

  const handleAddTask = (task: typeof availableTasks[0]) => {
    const workflowTask: WorkflowTask = {
      // id: `workflow-task-${selectedTasks.length + 1}`,
      taskId: task.id,
      ...task,
      linkedAgent: null // Initialize with null
    }
    setSelectedTasks(prev => [...prev, workflowTask])
    setIsTaskSelectorOpen(false)
  }

  const handleRemoveTask = (taskIndex: number) => {
    setSelectedTasks(prev => prev.filter((_, index) => index !== taskIndex))
  }

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return

    const items = Array.from(selectedTasks)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    setSelectedTasks(items)
  }

  const handleLinkAgent = (taskIndex: number, agentId: string | null) => { // Updated: agentId is now string | null
    setSelectedTasks(prev => prev.map((task, index) =>
      index === taskIndex ? { ...task, linkedAgent: agentId } : task
    ))
  }

  const validateWorkflow = () => {
    if (!workflowName.trim()) {
      setValidationError("Workflow name is required")
      return false
    }
    if (selectedTasks.length < 2) {
      setValidationError("Workflow must contain at least 2 tasks")
      return false
    }
    setValidationError(null)
    return true
  }

  const handleSaveWorkflow = async () => {
    if (!validateWorkflow()) return

    // Here you would typically save the workflow
    console.log("Saving workflow:", {
      name: workflowName,
      description: workflowDescription,
      tasks: selectedTasks.map(task => ({
        ...task,
        linkedAgent: task.linkedAgent || null // Ensure linkedAgent is included
      }))
    })

    toast({
      title: "Workflow saved",
      description: "Your workflow has been saved successfully.",
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Workflow Details</CardTitle>
          <CardDescription>
            Enter the basic information about your workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="workflowName">Workflow Name</Label>
            <Input
              id="workflowName"
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
              placeholder="Enter workflow name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="workflowDescription">Description</Label>
            <Textarea
              id="workflowDescription"
              value={workflowDescription}
              onChange={(e) => setWorkflowDescription(e.target.value)}
              placeholder="Describe the purpose of this workflow"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Tasks</CardTitle>
          <CardDescription>
            Add and arrange tasks for your workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            onClick={() => setIsTaskSelectorOpen(true)}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Task
          </Button>

          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="workflow-tasks">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-2"
                >
                  {selectedTasks.map((task, index) => (
                    <Draggable
                      key={task.id}
                      draggableId={task.id}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="relative bg-muted rounded-lg p-4"
                        >
                          <div className="flex items-start gap-4">
                            <div
                              {...provided.dragHandleProps}
                              className="mt-1.5 text-muted-foreground"
                            >
                              <GripVertical className="h-4 w-4" />
                            </div>
                            <div className="flex-1 space-y-2">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h4 className="font-medium">{task.name}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {task.description}
                                  </p>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleRemoveTask(index)}
                                  className="h-8 w-8"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="flex flex-wrap gap-2">
                                <Badge variant="secondary">
                                  {task.category}
                                </Badge>
                                <Badge variant="outline">
                                  {task.type}
                                </Badge>
                                <Badge variant="outline">
                                  {task.estimatedTime}
                                </Badge>
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor={`agent-${task.id}`}>Linked Agent</Label>
                                <Select
                                  value={task.linkedAgent || undefined} // Updated: set initial value to null
                                  onValueChange={(value) => handleLinkAgent(index, value)}
                                >
                                  <SelectTrigger id={`agent-${task.id}`}>
                                    <SelectValue placeholder="Select an agent" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={""}>None</SelectItem> {/* Added "None" option */}
                                    {availableAgents.map((agent) => (
                                      <SelectItem key={agent.id} value={agent.id}>
                                        {agent.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              {index < selectedTasks.length - 1 && (
                                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          {validationError && (
            <Alert variant="destructive">
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end pt-4">
            <Button onClick={handleSaveWorkflow}>
              Save Workflow
            </Button>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isTaskSelectorOpen} onOpenChange={setIsTaskSelectorOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Task to Workflow</DialogTitle>
            <DialogDescription>
              Select a task to add to your workflow
            </DialogDescription>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 p-2">
              {availableTasks.map((task) => (
                <div
                  key={task.id}
                  className="rounded-lg border p-4 hover:bg-muted transition-colors cursor-pointer"
                  onClick={() => handleAddTask(task)}
                >
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium">{task.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {task.description}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="secondary">
                          {task.category}
                        </Badge>
                        <Badge variant="outline">
                          {task.type}
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Inputs:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {task.inputs.map((input, index) => (
                            <li key={index}>{input}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong>Outputs:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {task.outputs.map((output, index) => (
                            <li key={index}>{output}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTaskSelectorOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

