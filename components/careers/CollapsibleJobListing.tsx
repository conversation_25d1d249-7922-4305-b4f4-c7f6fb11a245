'use client'

import { useState } from 'react'
import { Card, CardDescription, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  ChevronDown,
  ChevronUp,
  Code,
  Database,
  Brain,
  Cloud,
  BarChart4,
  Linkedin,
  Facebook
} from 'lucide-react'
import { ApplicationForm } from '@/components/careers/ApplicationForm'

interface JobPosition {
  title: string
  description: string
  requirements: string[]
  responsibilities: string[]
  location: string
  type: string
}

export function CollapsibleJobListing({ job }: { job: JobPosition }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Card className="bg-[#1a2436] border border-white/10 hover:border-[#0be5a9]/50 transition-all overflow-hidden mb-6">
      <div className="flex flex-col md:flex-row">
        <div className="md:w-1/4 p-6 flex flex-col items-center">
          {/* Position Icon */}
          <div className="w-full h-48 relative rounded-lg overflow-hidden border border-white/10 bg-gradient-to-b from-[#141b2b] to-[#0f172a]">
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              {job.title.includes("Founding Engineer") && (
                <Code className="w-24 h-24 text-[#0be5a9]" strokeWidth={1.25} />
              )}
              {job.title.includes("Data Engineer") && (
                <Database className="w-24 h-24 text-[#0be5a9]" strokeWidth={1.25} />
              )}
              {job.title.includes("AI Engineer") && (
                <Brain className="w-24 h-24 text-[#0be5a9]" strokeWidth={1.25} />
              )}
              {job.title.includes("DevOps") && (
                <Cloud className="w-24 h-24 text-[#0be5a9]" strokeWidth={1.25} />
              )}
              {job.title.includes("Marketing") && (
                <BarChart4 className="w-24 h-24 text-[#0be5a9]" strokeWidth={1.25} />
              )}
              <div className="mt-4 text-[#0be5a9] font-mono text-xs">
                {job.title.includes("Founding Engineer") && "< code />"}
                {job.title.includes("Data Engineer") && "< data />"}
                {job.title.includes("AI Engineer") && "< ai />"}
                {job.title.includes("DevOps") && "< cloud />"}
                {job.title.includes("Marketing") && "< growth />"}
              </div>
            </div>
          </div>
        </div>
        <div className="md:w-3/4 p-6 pt-0 md:pt-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex flex-wrap items-center gap-2 mb-2">
                <CardTitle className="text-2xl font-bold text-white">{job.title}</CardTitle>
                <Badge className="ml-2 bg-[#0be5a9]/20 text-[#0be5a9] hover:bg-[#0be5a9]/30">{job.type}</Badge>
                <Badge variant="outline" className="border-white/20 text-white/70">{job.location}</Badge>
              </div>
              <CardDescription className="text-gray-400 text-sm whitespace-pre-line p-2 mt-2">{job.description}</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center justify-center h-8 w-8 rounded-full bg-[#141b2b] hover:bg-[#1d2739] transition-colors"
                aria-label={isOpen ? "Collapse job details" : "Expand job details"}
              >
                {isOpen ? (
                  <ChevronUp className="h-4 w-4 text-[#0be5a9]" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-[#0be5a9]" />
                )}
              </button>

              <div className="flex items-center gap-2 ml-1">
                <span className="text-xs text-gray-400 mr-1">Share:</span>
                <button
                  onClick={() => {
                    const shareUrl = window.location.origin + '/careers';
                    const shareText = `Check out this ${job.title} position at Rekover! They're looking for talented professionals to join their team. #JobOpportunity #Rekover`;

                    // LinkedIn doesn't support custom text in the URL parameters as easily as Facebook,
                    // but we can include it in the URL as a comment parameter
                    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}&summary=${encodeURIComponent(shareText)}`, '_blank', 'width=600,height=600');
                  }}
                  className="flex items-center justify-center h-7 w-7 rounded-full bg-[#141b2b] hover:bg-[#0077b5] transition-colors"
                  aria-label="Share on LinkedIn"
                  title="Share on LinkedIn"
                >
                  <Linkedin className="h-3.5 w-3.5 text-[#0be5a9]" />
                </button>

                <button
                  onClick={() => {
                    const shareUrl = window.location.origin + '/careers';
                    const shareText = `Check out this ${job.title} position at Rekover! They're looking for talented professionals to join their team. #JobOpportunity #Rekover`;

                    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`, '_blank', 'width=600,height=600');
                  }}
                  className="flex items-center justify-center h-7 w-7 rounded-full bg-[#141b2b] hover:bg-[#1877f2] transition-colors"
                  aria-label="Share on Facebook"
                  title="Share on Facebook"
                >
                  <Facebook className="h-3.5 w-3.5 text-[#0be5a9]" />
                </button>
              </div>
            </div>
          </div>

          {isOpen && (
            <div className="mt-6 space-y-6">
              {/* Requirements Section */}
              <div className="space-y-2">
                <h4 className="font-semibold text-white text-base border-b border-[#0be5a9]/30 pb-2">
                  Requirements
                </h4>
                <div className="p-3 bg-[#141b2b]/50 rounded-md">
                  <ul className="list-disc pl-5 space-y-2 text-gray-300">
                    {job.requirements.map((req, i) => (
                      <li key={i}>{req}</li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Responsibilities Section */}
              <div className="space-y-2">
                <h4 className="font-semibold text-white text-base border-b border-[#0be5a9]/30 pb-2">
                  Responsibilities
                </h4>
                <div className="p-3 bg-[#141b2b]/50 rounded-md">
                  <ul className="list-disc pl-5 space-y-2 text-gray-300">
                    {job.responsibilities.map((resp, i) => (
                      <li key={i}>{resp}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          <div className="mt-4">
            <ApplicationForm jobTitle={job.title}>
              <Button className="bg-[#0be5a9] hover:bg-[#0be5a9]/80 text-black font-medium">
                Apply Now
              </Button>
            </ApplicationForm>
          </div>
        </div>
      </div>
    </Card>
  );
}
