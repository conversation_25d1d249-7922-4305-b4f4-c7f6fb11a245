'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"

interface ApplicationFormProps {
  jobTitle: string
  children: React.ReactNode
}

export function ApplicationForm({ jobTitle, children }: ApplicationFormProps) {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [position, setPosition] = useState(jobTitle)
  const [salary, setSalary] = useState('')
  const [coverLetter, setCoverLetter] = useState('')
  const [cvFile, setCvFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // In a real implementation, you would send this data to your backend
      // For now, we'll just simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 1500))

      toast({
        title: "Application submitted!",
        description: "Thank you for applying. We'll be in touch soon.",
      })

      // Reset form
      setName('')
      setEmail('')
      setPosition(jobTitle)
      setSalary('')
      setCoverLetter('')
      setCvFile(null)

      // Close dialog - this would need to be handled by the parent component
      // or by using a more complex state management approach
    } catch (error) {
      toast({
        title: "Something went wrong",
        description: "Your application couldn't be submitted. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setCvFile(e.target.files[0])
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] bg-[#1a2436] border-[#0be5a9]/30 text-white">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-white">Apply for {jobTitle}</DialogTitle>
          <DialogDescription className="text-gray-400">
            Fill out the form below to apply for this position. We'll review your application and get back to you soon.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-white">Full Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="bg-[#141b2b] border-[#0be5a9]/30 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-[#141b2b] border-[#0be5a9]/30 text-white"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="position" className="text-white">Position</Label>
              <Input
                id="position"
                value={position}
                onChange={(e) => setPosition(e.target.value)}
                required
                className="bg-[#141b2b] border-[#0be5a9]/30 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="salary" className="text-white">Salary Expectations (Optional)</Label>
              <Input
                id="salary"
                value={salary}
                onChange={(e) => setSalary(e.target.value)}
                placeholder="e.g. $80,000 - $100,000"
                className="bg-[#141b2b] border-[#0be5a9]/30 text-white"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cv" className="text-white">Resume/CV</Label>
            <Input
              id="cv"
              type="file"
              onChange={handleFileChange}
              required
              accept=".pdf,.doc,.docx"
              className="bg-[#141b2b] border-[#0be5a9]/30 text-white"
            />
            <p className="text-xs text-gray-400">Accepted formats: PDF, DOC, DOCX</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="coverLetter" className="text-white">Cover Letter (Optional)</Label>
            <Textarea
              id="coverLetter"
              value={coverLetter}
              onChange={(e) => setCoverLetter(e.target.value)}
              placeholder="Tell us why you're interested in this position and what you can bring to the team."
              className="min-h-[120px] bg-[#141b2b] border-[#0be5a9]/30 text-white"
            />
          </div>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[#0be5a9] hover:bg-[#0be5a9]/80 text-black font-medium"
          >
            {isSubmitting ? "Submitting..." : "Submit Application"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  )
}
