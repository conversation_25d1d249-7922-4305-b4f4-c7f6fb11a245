"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Calendar, DollarSign, BarChart2 } from "lucide-react";

const leakageTypes = ["Overpayment", "Underpayment", "Process Inefficiency", "Fraud", "Policy Misinterpretation", "System Error"];
const statuses = ["Open", "Closed", "Pending", "Confirmed", "Rejected"];
const urgencies = ["Low", "Medium", "High"];

interface Leakage {
  leakage_id: string;
  leakage_type: string;
  claim_id: string;
  status: string;
  estimate_savings: number;
  observations: string;
  urgency: string;
  date: string;
  modelScore: number;
}

export default function LeakagesBrowseScreen() {
  const [leakages, setLeakages] = useState<Leakage[]>([]);
  const [selectedLeakageType, setSelectedLeakageType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedUrgency, setSelectedUrgency] = useState("");
  const [searchId, setSearchId] = useState("");
  const { data: session } = useSession();
  const userId = session?.user?.id;

  useEffect(() => {
    if (!userId) return; // Prevent fetch if user ID is unavailable

    fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/leakages/user/${userId}`)
      .then((response) => response.json())
      .then((data: Leakage[]) => setLeakages(data))
      .catch((error) => console.error("Error fetching leakages:", error));
  }, [userId]);

  const filteredLeakages = leakages.filter((leakage) =>
    (!selectedLeakageType || leakage.leakage_type.toLowerCase() === selectedLeakageType.toLowerCase()) &&
    (!selectedStatus || leakage.status.toLowerCase() === selectedStatus.toLowerCase()) &&
    (!selectedUrgency || leakage.urgency.toLowerCase() === selectedUrgency.toLowerCase()) &&
    (!searchId || leakage.leakage_id.includes(searchId))
  );
  

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select value={selectedLeakageType} onValueChange={setSelectedLeakageType}>
              <SelectTrigger>
                <SelectValue placeholder="Leakage Type" />
              </SelectTrigger>
              <SelectContent>
                {leakageTypes.map((type) => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedUrgency} onValueChange={setSelectedUrgency}>
              <SelectTrigger>
                <SelectValue placeholder="Urgency" />
              </SelectTrigger>
              <SelectContent>
                {urgencies.map((urgency) => (
                  <SelectItem key={urgency} value={urgency}>{urgency}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>{status}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input placeholder="Search by Leakage ID" value={searchId} onChange={(e) => setSearchId(e.target.value)} />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredLeakages.map((leakage) => (
          <Card key={leakage.leakage_id} className="overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div className={`h-2 bg-${leakage.urgency.toLowerCase()}`} />
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold mb-1">{leakage.leakage_id}</h3>
                  <p className="text-sm text-gray-500">Claim ID: {leakage.claim_id}</p>
                </div>
                <Badge variant={leakage.status === "Open" ? "default" : "secondary"}>{leakage.status}</Badge>
              </div>

              <div className="flex items-center mb-4">
                <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                <span className="text-sm">{new Date(leakage.date).toLocaleDateString()}</span>
              </div>

              <div className="bg-muted p-4 rounded-lg mb-4">
                <h4 className="text-sm font-semibold">{leakage.leakage_type}</h4>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <BarChart2 className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="text-sm">Score: {leakage.modelScore}</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="text-sm">${leakage.estimate_savings.toLocaleString()}</span>
                  </div>
                </div>
                <Progress value={leakage.modelScore * 100} className="h-2 mb-2" />
                <p className="text-sm mb-2">{leakage.observations}</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
