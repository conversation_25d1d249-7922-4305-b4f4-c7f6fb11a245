import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface SegmentationCategory {
  name: string;
  score: number;
}

interface SegmentationResultsProps {
  results: SegmentationCategory[];
}

export function SegmentationResults({ results }: SegmentationResultsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Segmentation Results</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {results.map((category, index) => (
            <div key={index}>
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">{category.name}</span>
                <span className="text-sm font-medium">{category.score.toFixed(2)}</span>
              </div>
              <Progress value={category.score * 100} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

