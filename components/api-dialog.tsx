"use client"

import {
<PERSON><PERSON>,
<PERSON><PERSON><PERSON>onte<PERSON>,
<PERSON>alogDescription,
<PERSON>alog<PERSON>eader,
DialogTitle,
DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Code2, Key } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface APIDialogProps {
modelName?: string
toolName?: string
endpoint: string
requestExample: Record<string, any>
responseExample: Record<string, any>
}

export function APIDialog({ modelName, toolName, endpoint, requestExample, responseExample }: APIDialogProps) {
const pythonAuthExample = `
import os
from rekover.auth import configure_auth
from rekover.models import ${modelName ? modelName.replace(/\s+/g, '') : 'Tool'}

# Method 1: Set API key via environment variable
os.environ["REKOVER_API_KEY"] = "your_api_key_here"

# Method 2: Configure API key programmatically
configure_auth(api_key="your_api_key_here")

// Initialize the model (automatically uses the configured API key)
model = ${modelName ? modelName.replace(/\s+/g, '') : 'Tool'}("${toolName || ''}")

# Make predictions
result = model.predict({
${Object.entries(requestExample).map(([key, value]) => 
  `"${key}": ${typeof value === 'string' ? `"${value}"` : value}`
).join(',\n    ')}
})

# Access the results
print(result)
`.trim()

const pythonEnvExample = `
# .env file
REKOVER_API_KEY=your_api_key_here
`.trim()

const restExample = `
curl -X POST https://api.rekover.ai/v1/${endpoint} \\
-H "Authorization: Bearer your_api_key_here" \\
-H "Content-Type: application/json" \\
-d '{
${JSON.stringify(requestExample, null, 2).split('\n').join('\n  ')}
}'
`.trim()

return (
<Dialog>
  <DialogTrigger asChild>
    <Button variant="outline" size="sm" className="gap-2">
      <Code2 className="h-4 w-4" />
      API
    </Button>
  </DialogTrigger>
  <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
    <DialogHeader>
      <DialogTitle>API Integration for {modelName || toolName}</DialogTitle>
      <DialogDescription>
        Access this feature through our REST API or Python package
      </DialogDescription>
    </DialogHeader>
    
    <Alert>
      <Key className="h-4 w-4" />
      <AlertTitle>Authentication Required</AlertTitle>
      <AlertDescription>
        Get your API key from the <a href="https://dashboard.rekover.ai/api-keys" className="font-medium underline">Rekover Dashboard</a>. 
        Keep your API key secure and never expose it in client-side code.
      </AlertDescription>
    </Alert>
    
    <Tabs defaultValue="python" className="w-full mt-6">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="python">Python Package</TabsTrigger>
        <TabsTrigger value="rest">REST API</TabsTrigger>
      </TabsList>
      
      <TabsContent value="python">
        <Card>
          <CardHeader>
            <CardTitle>Python Package Usage</CardTitle>
            <CardDescription>Install via pip: <code>pip install rekover</code></CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Authentication Setup</h4>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Option 1: Environment Variables</p>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    <code>{pythonEnvExample}</code>
                  </pre>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Option 2: Python Code</p>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                    <code>{pythonAuthExample}</code>
                  </pre>
                </div>
              </div>
            </div>

            <Alert className="text-sm bg-yellow-100 border-yellow-400 text-yellow-800">
              <AlertDescription>
                For production deployments, we recommend using environment variables to manage your API key securely.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="rest">
        <Card>
          <CardHeader>
            <CardTitle>REST API Endpoint</CardTitle>
            <CardDescription>Send authenticated POST requests to access the model</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">Authentication</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Include your API key in the Authorization header as a Bearer token with every request.
              </p>
              <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                <code>{restExample}</code>
              </pre>
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Example Response:</h4>
                <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                  <code>{JSON.stringify(responseExample, null, 2)}</code>
                </pre>
              </div>
            </div>

            <Alert className="text-sm bg-yellow-100 border-yellow-400 text-yellow-800">
              <AlertDescription>
                Never expose your API key in client-side JavaScript code. Always make API calls from your backend server.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </DialogContent>
</Dialog>
)
}

