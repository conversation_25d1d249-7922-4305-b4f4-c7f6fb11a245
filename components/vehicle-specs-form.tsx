"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export function VehicleSpecsForm({ onSubmit }: { onSubmit: (data: { licensePlate: string }) => void }) {
  const [licensePlate, setLicensePlate] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ licensePlate })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="licensePlate">License Plate</Label>
        <Input
          id="licensePlate"
          value={licensePlate}
          onChange={(e) => setLicensePlate(e.target.value)}
          required
          placeholder="Enter license plate"
        />
      </div>
      <Button type="submit" className="w-full">
        Retrieve Specs
      </Button>
    </form>
  )
}

