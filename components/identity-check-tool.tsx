"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileUploader } from "@/components/file-uploader";
import { Loader2, FileCheck } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface IdentityCheckResult {
  documentType: string;
  extractedInfo: {
    [key: string]: string;
  };
  isAuthentic: boolean;
  confidenceScore: number;
}

export function IdentityCheckTool() {
  const [document, setDocument] = useState<File | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [checkResult, setCheckResult] = useState<IdentityCheckResult | null>(
    null
  );

  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      setDocument(files[0]);
    }
  };

  const handleCheck = async () => {
    if (!document) return;

    setIsChecking(true);
    setCheckResult(null);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock result
      const mockResult: IdentityCheckResult = {
        documentType: "Driver's License",
        extractedInfo: {
          name: "John Doe",
          address: "123 Main St, Anytown, USA",
          idNumber: "*********",
          dateOfBirth: "1990-01-01",
          expirationDate: "2025-01-01",
        },
        isAuthentic: Math.random() > 0.2, // 80% chance of being authentic
        confidenceScore: Math.random() * 0.3 + 0.7, // Random score between 0.7 and 1.0
      };

      setCheckResult(mockResult);
      toast({
        title: "Identity Check Complete",
        description: "The document has been analyzed successfully.",
      });
    } catch (error) {
      toast({
        title: "Identity Check Failed",
        description:
          "There was an error analyzing the document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Identity Check Tool</CardTitle>
        <CardDescription>
          Upload an identification document for analysis and verification
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <FileUploader
            id="document-upload"
            accept=".pdf,.png,.jpg,.jpeg"
            onFilesSelected={handleFileUpload}
          />
          <Button onClick={handleCheck} disabled={isChecking || !document}>
            {isChecking ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <FileCheck className="mr-2 h-4 w-4" />
                Check Document
              </>
            )}
          </Button>

          {checkResult && (
            <div className="mt-6 space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Document Analysis Result
                </h3>
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">
                        Document Type
                      </TableCell>
                      <TableCell>{checkResult.documentType}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">
                        Authenticity
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            checkResult.isAuthentic ? "default" : "destructive"
                          }
                          className={
                            checkResult.isAuthentic
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }
                        >
                          {checkResult.isAuthentic
                            ? "Authentic"
                            : "Not Authentic"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">
                        Confidence Score
                      </TableCell>
                      <TableCell>
                        {(checkResult.confidenceScore * 100).toFixed(2)}%
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Extracted Information
                </h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Field</TableHead>
                      <TableHead>Value</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(checkResult.extractedInfo).map(
                      ([key, value]) => (
                        <TableRow key={key}>
                          <TableCell className="font-medium">{key}</TableCell>
                          <TableCell>{value}</TableCell>
                        </TableRow>
                      )
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
