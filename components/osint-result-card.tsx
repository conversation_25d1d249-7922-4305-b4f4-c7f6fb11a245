import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ExternalLink } from 'lucide-react'
import { LucideIcon } from 'lucide-react'

interface OSINTResultCardProps {
  platform: string
  nickname: string
  url: string
  icon: LucideIcon
}

export function OSINTResultCard({ platform, nickname, url, icon: Icon }: OSINTResultCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon className="h-5 w-5" />
          {platform}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-2"><strong>Nickname:</strong> {nickname}</p>
        <a 
          href={url} 
          target="_blank" 
          rel="noopener noreferrer" 
          className="flex items-center text-blue-500 hover:underline"
        >
          View Profile <ExternalLink className="ml-1 h-4 w-4" />
        </a>
      </CardContent>
    </Card>
  )
}

