import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const topClients = [
  { name: "Acme Corporation", type: "Corporate", totalPolicies: 15, totalValue: 500000 },
  { name: "<PERSON>", type: "Individual", totalPolicies: 5, totalValue: 150000 },
  { name: "TechStart Inc.", type: "Small Business", totalPolicies: 8, totalValue: 300000 },
  { name: "City Government", type: "Government", totalPolicies: 12, totalValue: 450000 },
  { name: "Global Enterprises", type: "Corporate", totalPolicies: 20, totalValue: 750000 },
]

export default function TopClientsTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Total Policies</TableHead>
          <TableHead>Total Value</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {topClients.map((client) => (
          <TableRow key={client.name}>
            <TableCell className="font-medium">{client.name}</TableCell>
            <TableCell>{client.type}</TableCell>
            <TableCell>{client.totalPolicies}</TableCell>
            <TableCell>${client.totalValue.toLocaleString()}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

