"use client"

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileUploader } from "@/components/file-uploader"

interface TotalLossInfo {
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: string;
  mileage: string;
  damageDescription: string;
  accidentSeverity: string;
  damageFiles: File[];
}

interface TotalLossFormProps {
  onSubmit: (totalLossInfo: TotalLossInfo) => void;
}

export function TotalLossForm({ onSubmit }: TotalLossFormProps) {
  const [totalLossInfo, setTotalLossInfo] = useState<TotalLossInfo>({
    vehicleMake: '',
    vehicleModel: '',
    vehicleYear: '',
    mileage: '',
    damageDescription: '',
    accidentSeverity: '',
    damageFiles: [],
  });

  const handleChange = (field: keyof TotalLossInfo, value: string | File[]) => {
    setTotalLossInfo(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(totalLossInfo);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="vehicleMake">Vehicle Make</Label>
          <Input
            id="vehicleMake"
            value={totalLossInfo.vehicleMake}
            onChange={(e) => handleChange('vehicleMake', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="vehicleModel">Vehicle Model</Label>
          <Input
            id="vehicleModel"
            value={totalLossInfo.vehicleModel}
            onChange={(e) => handleChange('vehicleModel', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="vehicleYear">Vehicle Year</Label>
          <Input
            id="vehicleYear"
            type="number"
            min="1900"
            max={new Date().getFullYear()}
            value={totalLossInfo.vehicleYear}
            onChange={(e) => handleChange('vehicleYear', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="mileage">Mileage</Label>
          <Input
            id="mileage"
            type="number"
            min="0"
            value={totalLossInfo.mileage}
            onChange={(e) => handleChange('mileage', e.target.value)}
            required
          />
        </div>
      </div>
      <div>
        <Label htmlFor="damageDescription">Damage Description</Label>
        <Input
          id="damageDescription"
          value={totalLossInfo.damageDescription}
          onChange={(e) => handleChange('damageDescription', e.target.value)}
          required
        />
      </div>
      <div>
        <Label htmlFor="accidentSeverity">Accident Severity</Label>
        <Select 
          value={totalLossInfo.accidentSeverity} 
          onValueChange={(value) => handleChange('accidentSeverity', value)}
        >
          <SelectTrigger id="accidentSeverity">
            <SelectValue placeholder="Select accident severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="minor">Minor</SelectItem>
            <SelectItem value="moderate">Moderate</SelectItem>
            <SelectItem value="severe">Severe</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="damageFiles">Upload Images or Videos of Damage</Label>
        <FileUploader
          id="damageFiles"
          onFilesSelected={(files) => handleChange('damageFiles', files)}
          accept="image/*,video/*"
          multiple
        />
      </div>
      <Button type="submit">Submit Total Loss Information</Button>
    </form>
  );
}

