"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileUploader } from "@/components/file-uploader";
import { Loader2, CheckCircle, AlertTriangle, DollarSign } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface AuditIssue {
  issue: string;
  severity: "Low" | "Medium" | "High";
  details: string;
  potentialSavings?: number;
}

interface AppraisalAuditResult {
  issuesDetected: AuditIssue[];
  overallScore: number;
  recommendations: string[];
  totalPotentialSavings: number;
}

export function AppraisalAuditTool() {
  const [appraisalId, setAppraisalId] = useState("");
  const [appraisalType, setAppraisalType] = useState<"auto" | "home">("auto");
  const [additionalFiles, setAdditionalFiles] = useState<File[]>([]);
  const [isAuditing, setIsAuditing] = useState(false);
  const [auditResult, setAuditResult] = useState<AppraisalAuditResult | null>(
    null
  );

  const handleAppraisalTypeChange = (value: "auto" | "home") => {
    setAppraisalType(value);
  };

  const handleFileUpload = (files: File[]) => {
    setAdditionalFiles(files);
  };

  const handleAudit = async () => {
    if (!appraisalId) {
      toast({
        title: "Appraisal ID Required",
        description: "Please enter an appraisal ID to proceed with the audit.",
        variant: "destructive",
      });
      return;
    }

    setIsAuditing(true);
    setAuditResult(null);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Mock audit result with potential savings
      const mockResult: AppraisalAuditResult = {
        issuesDetected: [
          {
            issue: "Overcharging for labor",
            severity: "High",
            details: "Labor cost exceeds industry average by 20%",
            potentialSavings: 200,
          },
          {
            issue: "Unnecessary part replacement",
            severity: "Medium",
            details: "The bumper could have been repaired instead of replaced",
            potentialSavings: 500,
          },
          {
            issue: "Missing documentation",
            severity: "Low",
            details: "Photos of the damaged parts are missing",
          },
        ],
        overallScore: 75,
        recommendations: [
          "Request revised estimate from repair shop",
          "Negotiate lower labor costs",
        ],
        totalPotentialSavings: 700, // Sum of potential savings from all issues
      }; 

      setAuditResult(mockResult);
      toast({
        title: "Audit Complete",
        description: "The appraisal has been audited successfully.",
      });
    } catch (error) {
      toast({
        title: "Audit Failed",
        description:
          "There was an error auditing the appraisal. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAuditing(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Appraisal Audit Tool</CardTitle>
        <CardDescription>
          Enter the appraisal ID and upload additional documents for audit
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="appraisal-id">Appraisal ID</Label>
            <Input
              id="appraisal-id"
              placeholder="Enter appraisal ID"
              value={appraisalId}
              onChange={(e) => setAppraisalId(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="appraisal-type">Appraisal Type</Label>
            <Select
              value={appraisalType}
              onValueChange={handleAppraisalTypeChange}
            >
              <SelectTrigger id="appraisal-type">
                <SelectValue placeholder="Select appraisal type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto</SelectItem>
                <SelectItem value="home">Home</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Additional Documents (Optional)</Label>
            <FileUploader
              id="additional-files"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
              onFilesSelected={handleFileUpload}
              multiple
            />
          </div>
          <Button onClick={handleAudit} disabled={isAuditing || !appraisalId}>
            {isAuditing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Auditing...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Audit Appraisal
              </>
            )}
          </Button>
        </form>

        {auditResult && (
          <div className="mt-6 space-y-4">
            <h3 className="text-lg font-semibold mb-2">Audit Results</h3>
            <div className="flex items-center space-x-2 mb-4">
              <span className="font-medium">Overall Score:</span>
              <span className="font-bold text-2xl">
                {auditResult.overallScore}/100
              </span>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Detected Issues</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Issue</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Potential Savings</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditResult.issuesDetected.map((issue, index) => (
                    <TableRow key={index}>
                      <TableCell>{issue.issue}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            issue.severity === "High"
                              ? "destructive"
                              : issue.severity === "Medium"
                              ? "default"
                              : "secondary"
                          }
                        >
                          {issue.severity}
                        </Badge>
                      </TableCell>
                      <TableCell>{issue.details}</TableCell>
                      <TableCell>
                        {issue.potentialSavings ? (
                          <div className="flex items-center gap-2 font-medium text-green-600">
                            <DollarSign className="h-4 w-4" />$
                            {issue.potentialSavings.toLocaleString()}
                          </div>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <Alert
              variant="default"
              className="mt-4 border-green-500 bg-green-50 text-green-800"
            >
              <AlertTitle>
                <CheckCircle className="h-4 w-4 mr-2" />
                Total Potential Savings
              </AlertTitle>
              <AlertDescription>
                <div className="flex items-center gap-2 font-bold text-green-600">
                  <DollarSign className="h-4 w-4" />$
                  {auditResult.totalPotentialSavings.toLocaleString()}
                </div>
              </AlertDescription>
            </Alert>

            <h3 className="text-lg font-semibold mt-4 mb-2">Recommendations</h3>
            <ul className="list-disc list-inside space-y-1">
              {auditResult.recommendations.map((recommendation, index) => (
                <li key={index} className="text-sm">
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
