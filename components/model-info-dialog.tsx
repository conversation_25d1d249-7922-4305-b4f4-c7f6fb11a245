"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogDescription,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { InfoIcon } from 'lucide-react'
import { ModelInfoCard } from "@/components/model-info-card"

interface ModelInfoDialogProps {
  title: string
  description: string
  purpose: string[]
  methodology: string[]
  outputs: string[]
  accuracy?: number
  type: "classification" | "regression" | "clustering" | "nlp" | "tool" | "lookup"
}

export function ModelInfoDialog(props: ModelInfoDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <InfoIcon className="h-4 w-4" />
          Model Information
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Model Information</DialogTitle>
          <DialogDescription>
            Detailed information about this model&apos;s capabilities and usage
          </DialogDescription>
        </DialogHeader>
        <ModelInfoCard {...props} />
      </DialogContent>
    </Dialog>
  )
}

