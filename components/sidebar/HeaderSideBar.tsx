"use client";

import { Search, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";

interface HeaderSideBarProps {
  isSearchOpen: boolean;
  toggleSearch: () => void;
  selectedCategory: string | null;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (value: boolean) => void;
  handleCategorySelect: (category: string) => void;
  MenuItems: Array<{ label: string }>;
}

export function HeaderSideBar({
  isSearchOpen,
  toggleSearch,
  selectedCategory,
  isDropdownOpen,
  setIsDropdownOpen,
  handleCategorySelect,
  MenuItems
}: HeaderSideBarProps) {
  // Helper function to display abbreviated text for long category names
  const getDisplayText = (text: string) => {
    if (text === "Project Management") return "PM";
    return text;
  };

  return (
    <div className="flex h-16 items-center justify-between border-b border-gray-700 bg-gray-900 px-4">
      <div className="flex items-center gap-3">
        <h1 className="text-xl font-bold tracking-tight">Rekover</h1>
        <Search 
          className="h-4 w-4 text-gray-400 hover:text-gray-300 cursor-pointer transition-colors" 
          onClick={toggleSearch} 
        />
      </div>
      <div className="relative">
        <Button
          variant="ghost"
          className="flex items-center gap-1 rounded-md px-3 py-1.5 text-sm font-medium text-gray-200 hover:bg-gray-700 transition-colors"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <span>{selectedCategory ? getDisplayText(selectedCategory) : "All"}</span>
          <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isDropdownOpen ? "rotate-180" : ""}`} />
        </Button>
        <AnimatePresence>
          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              transition={{ duration: 0.15 }}
              className="absolute right-0 top-full mt-1 w-48 rounded-md bg-gray-800 border border-gray-700 p-1.5 shadow-lg z-10"
            >
              <ul className="space-y-0.5">
                <li key="all">
                  <Button
                    variant="ghost"
                    className={`flex w-full items-center justify-start rounded-md px-3 py-1.5 text-sm ${
                      selectedCategory === null ? "bg-gray-700 text-white" : "text-gray-300 hover:bg-gray-700"
                    }`}
                    onClick={() => handleCategorySelect("All")}
                  >
                    All
                  </Button>
                </li>
                {MenuItems.map((section) => (
                  <li key={section.label}>
                    <Button
                      variant="ghost"
                      className={`flex w-full items-center justify-start rounded-md px-3 py-1.5 text-sm ${
                        selectedCategory === section.label ? "bg-gray-700 text-white" : "text-gray-300 hover:bg-gray-700"
                      }`}
                      onClick={() => handleCategorySelect(section.label)}
                    >
                      {section.label}
                    </Button>
                  </li>
                ))}
              </ul>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
} 