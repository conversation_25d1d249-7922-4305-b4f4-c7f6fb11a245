"use client";

import Link from "next/link";
import { 
  LayoutDashboard, 
  ShoppingBag, 
  Settings, 
  LogOut, 
  ThumbsUp, 
  Coins 
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { signOut } from "next-auth/react";
import type { UserRole } from "@/types/user";

interface FooterSideBarProps {
  userRole: UserRole;
  tokens: number;
  userImage: string;
  userName?: string;
  userEmail?: string;
  addToRecent: (item: any, category: string) => void;
}

const adminSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
  {
    name: "Settings",
    icon: Settings,
    href: "/dashboard/settings",
  },
];

const userSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
];

export function FooterSideBar({
  userRole,
  tokens,
  userImage,
  userName,
  userEmail,
  addToRecent
}: FooterSideBarProps) {
  return (
    <div className="border-t border-gray-700 bg-gray-900">
      <div className="px-3 py-3">
        <div className="flex items-center justify-between mb-3">
          <Button
            variant="ghost"
            className="flex items-center bg-gray-800/80 rounded-full px-2.5 py-1 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
          >
            <Coins className="h-3.5 w-3.5 text-yellow-500 mr-1.5" />
            <span className="font-medium">{tokens}</span>
          </Button>
        </div>
        
        <ul className="space-y-2">
          {/* Conditionally render admin or user section items based on role */}
          {userRole === "admin"
            ? adminSectionItems.map((item) => (
                <li key={item.name}>
                  <Link href={item.href} onClick={() => addToRecent(item, "Admin")}>
                    <Button
                      variant="ghost"
                      className="flex w-full items-center justify-start rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.name}
                    </Button>
                  </Link>
                </li>
              ))
            : userSectionItems.map((item) => (
                <li key={item.name}>
                  <Link href={item.href} onClick={() => addToRecent(item, "User")}>
                    <Button
                      variant="ghost"
                      className="flex w-full items-center justify-start rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.name}
                    </Button>
                  </Link>
                </li>
              ))
          }

          {/* User profile box */}
          <li className="mt-3">
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="relative rounded-lg bg-gray-700/60 backdrop-blur-sm p-3 cursor-pointer group"
            >
              <Link href="/dashboard/user-settings" className="absolute inset-0 z-10" aria-label="User settings">
                <span className="sr-only">Go to user settings</span>
              </Link>
              
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 border-2 border-gray-600">
                  <AvatarImage src={userImage} alt={userName || ""} />
                  <AvatarFallback>
                    {userName?.charAt(0) || "U"}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <p className="truncate text-sm font-medium text-white">{userName}</p>
                  <p className="truncate text-xs text-gray-400">{userEmail}</p>
                </div>

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    signOut();
                  }}
                  className="relative z-20 h-8 w-8 rounded-full text-gray-400 hover:bg-gray-600 hover:text-white transition-colors"
                  aria-label="Sign out"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="absolute inset-0 rounded-lg bg-gradient-to-tr from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>
          </li>
        </ul>
      </div>
    </div>
  );
} 