"use client";

import Link from "next/link";
import { <PERSON>, <PERSON>, Clock, ChevronDown, <PERSON>, Set<PERSON>s } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useRef, MouseEvent, useState, useEffect } from "react";

interface SubItem {
  name: string;
  href: string;
  disabled?: boolean;
}

interface MenuItem {
  name: string;
  icon: React.ComponentType<any>;
  href?: string;
  subItems?: SubItem[];
}

interface MenuSection {
  label: string;
  items: MenuItem[];
}

interface FavoriteItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  categoryLabel: string;
}

interface RecentItem extends FavoriteItem {
  timestamp: number;
}

interface ContentSideBarProps {
  isSearchOpen: boolean;
  searchText: string;
  setSearchText: (text: string) => void;
  searchResults: any[];
  handleSearchResultClick: (result: any) => void;
  isScrolling: boolean;
  handleScroll: () => void;
  isFavoritesOpen: boolean;
  setIsFavoritesOpen: (open: boolean) => void;
  isRecentOpen: boolean;
  setIsRecentOpen: (open: boolean) => void;
  favorites: FavoriteItem[];
  recentItems: RecentItem[];
  filteredMenuItems: MenuSection[];
  openItems: string[];
  toggleItem: (item: string, sectionLabel: string) => void;
  isInFavorites: (href: string) => boolean;
  toggleFavorite: (e: MouseEvent, item: any, categoryLabel: string) => void;
  addToRecent: (item: any, categoryLabel: string) => void;
}

// Define which categories belong to which tab
const buildCategories = ["ML Models", "Tools"];
const manageCategories = ["AI Assistants", "AI Workflows"]; //["AI Assistants", "Analytics", "Project Management"];

export function ContentSideBar({
  isSearchOpen,
  searchText,
  setSearchText,
  searchResults,
  handleSearchResultClick,
  isScrolling,
  handleScroll,
  isFavoritesOpen,
  setIsFavoritesOpen,
  isRecentOpen,
  setIsRecentOpen,
  favorites,
  recentItems,
  filteredMenuItems,
  openItems,
  toggleItem,
  isInFavorites,
  toggleFavorite,
  addToRecent
}: ContentSideBarProps) {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<'build' | 'manage'>('build');

  // Initialize favorites and recent sections to closed by default
  useEffect(() => {
    setIsFavoritesOpen(false);
    setIsRecentOpen(false);
  }, []);

  // Filter menu items based on active tab
  const tabFilteredMenuItems = filteredMenuItems.filter(section => 
    activeTab === 'build' 
      ? buildCategories.includes(section.label)
      : manageCategories.includes(section.label)
  );

  // Save active tab to localStorage
  useEffect(() => {
    const savedTab = localStorage.getItem('sidebarActiveTab');
    if (savedTab === 'build' || savedTab === 'manage') {
      setActiveTab(savedTab);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('sidebarActiveTab', activeTab);
  }, [activeTab]);

  return (
    <>
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div 
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-b border-gray-700 overflow-hidden"
          >
            <div className="p-3">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search tools..."
                  className="w-full rounded-md bg-gray-700 border border-gray-600 px-3 py-2 pl-8 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
              </div>
              
              {/* Search Results */}
              <AnimatePresence>
                {searchResults.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.15 }}
                    className="mt-2 rounded-md border border-gray-700 bg-gray-800 shadow-lg overflow-hidden max-h-80 overflow-y-auto"
                  >
                    <ul className="py-1">
                      {searchResults.map((result, idx) => (
                        <motion.li 
                          key={idx}
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.1, delay: idx * 0.03 }}
                        >
                          <Link 
                            href={result.href}
                            onClick={() => handleSearchResultClick(result)}
                            className="flex items-center px-3 py-2 text-sm text-gray-300 hover:bg-gray-700"
                          >
                            <result.icon className="mr-2 h-4 w-4 text-gray-400" />
                            <div className="flex flex-col">
                              <span className="font-medium">{result.subItemName || result.itemName}</span>
                              <span className="text-xs text-gray-500">
                                {result.subItemName ? `${result.itemName} · ` : ""}
                                {result.categoryLabel}
                              </span>
                            </div>
                          </Link>
                        </motion.li>
                      ))}
                    </ul>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div 
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className={`
          flex-1 overflow-y-auto 
          scrollbar-none hover:scrollbar-thin
          transition-all duration-300 ease-in-out
          ${isScrolling ? 'scrolling' : ''}
        `}
        style={{
          '--scrollbar-track': 'transparent',
          '--scrollbar-thumb': 'rgba(255, 255, 255, 0.1)',
          '--scrollbar-width': '6px',
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(255, 255, 255, 0.1) transparent',
        } as React.CSSProperties}
      >
        <motion.nav 
          className="px-2 py-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {/* Pinned Sections - Now at the top */}
          <div className="space-y-2 mb-6">
            {/* Favorites Section */}
            <Collapsible
              open={isFavoritesOpen}
              onOpenChange={setIsFavoritesOpen}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                >
                  <span className="flex items-center">
                    <Star className="mr-2 h-4 w-4 text-yellow-500" />
                    <span className="text-xs font-semibold uppercase tracking-wider">Favorites</span>
                  </span>
                  <div className="flex items-center">
                    {favorites.length > 0 && (
                      <span className="mr-2 text-xs text-gray-400">
                        {favorites.length}
                      </span>
                    )}
                    <ChevronDown
                      className={`h-4 w-4 transition-transform duration-200 ${
                        isFavoritesOpen ? "rotate-180" : ""
                      }`}
                    />
                  </div>
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                {favorites.length === 0 ? (
                  <div className="px-3 py-2 text-xs text-gray-500 italic">
                    No favorites yet. Hover over a tool and click the star to add it.
                  </div>
                ) : (
                  <motion.ul 
                    className="mt-1 space-y-1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {favorites.map((item) => (
                      <motion.li 
                        key={item.href}
                        initial={{ opacity: 0, x: -5 }}
                        animate={{ opacity: 1, x: 0 }}
                      >
                        <Link href={item.href}>
                          <Button
                            variant="ghost"
                            className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors group"
                          >
                            <span className="flex items-center">
                              <item.icon className="mr-2 h-4 w-4" />
                              <span>{item.name}</span>
                            </span>
                            <div className="flex items-center">
                              <span className="text-xs text-gray-500 group-hover:text-gray-400 mr-2">
                                {item.categoryLabel}
                              </span>
                              <Star 
                                className={`h-3.5 w-3.5 cursor-pointer transition-opacity duration-200 ${
                                  isInFavorites(item.href) 
                                    ? "text-yellow-500" 
                                    : "text-gray-500 opacity-0 group-hover:opacity-100"
                                }`}
                                onClick={(e) => toggleFavorite(e, item, item.categoryLabel)}
                              />
                            </div>
                          </Button>
                        </Link>
                      </motion.li>
                    ))}
                  </motion.ul>
                )}
              </CollapsibleContent>
            </Collapsible>

            {/* Recent Section */}
            <Collapsible
              open={isRecentOpen}
              onOpenChange={setIsRecentOpen}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                >
                  <span className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-blue-400" />
                    <span className="text-xs font-semibold uppercase tracking-wider">Recent</span>
                  </span>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${
                      isRecentOpen ? "rotate-180" : ""
                    }`}
                  />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                {recentItems.length === 0 ? (
                  <div className="px-3 py-2 text-xs text-gray-500 italic">
                    No recent activity. The 3 most recently used tools will appear here.
                  </div>
                ) : (
                  <motion.ul 
                    className="mt-1 space-y-1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {recentItems.map((item) => (
                      <motion.li 
                        key={item.href}
                        initial={{ opacity: 0, x: -5 }}
                        animate={{ opacity: 1, x: 0 }}
                      >
                        <Link href={item.href} onClick={() => addToRecent(item, item.categoryLabel)}>
                          <Button
                            variant="ghost"
                            className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors group"
                          >
                            <span className="flex items-center">
                              <item.icon className="mr-2 h-4 w-4" />
                              <span>{item.name}</span>
                            </span>
                            <div className="flex items-center">
                              <span className="text-xs text-gray-500 group-hover:text-gray-400">
                                {new Date(item.timestamp).toLocaleTimeString(undefined, {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </span>
                            </div>
                          </Button>
                        </Link>
                      </motion.li>
                    ))}
                  </motion.ul>
                )}
              </CollapsibleContent>
            </Collapsible>
          </div>

          {/* Tab Navigation - Now below the favorites and recent sections */}
          <div className="flex mb-4 px-1 border-b border-gray-700">
            <button
              className={`flex items-center justify-center flex-1 py-2 px-4 text-xs font-medium transition-colors relative ${
                activeTab === 'build' 
                  ? 'text-blue-400' 
                  : 'text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('build')}
            >
              <Hammer className="h-4 w-4 mr-2" />
              Build
              {activeTab === 'build' && (
                <motion.div 
                  layoutId="tab-indicator"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                />
              )}
            </button>
            <button
              className={`flex items-center justify-center flex-1 py-2 px-4 text-xs font-medium transition-colors relative ${
                activeTab === 'manage' 
                  ? 'text-blue-400' 
                  : 'text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('manage')}
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage
              {activeTab === 'manage' && (
                <motion.div 
                  layoutId="tab-indicator"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                />
              )}
            </button>
          </div>

          {/* Tab Content with Animation */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {/* Main Menu Items - Filtered by active tab */}
              <ul className="space-y-4">
                {tabFilteredMenuItems.map((section) => (
                  <motion.li 
                    key={section.label}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <h2 className="mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-gray-400">
                      {section.label}
                    </h2>
                    <ul className="space-y-1">
                      {section.items.map((item) => (
                        <li key={item.name}>
                          {item.subItems ? (
                            <Collapsible
                              key={`${section.label}-${item.name}`}
                              open={openItems.includes(`${section.label}-${item.name}`)}
                              onOpenChange={() => toggleItem(item.name, section.label)}
                            >
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors group"
                                >
                                  <span className="flex items-center">
                                    <item.icon className="mr-2 h-4 w-4" />
                                    {item.name}
                                  </span>
                                  <ChevronDown
                                    className={`h-4 w-4 transition-transform duration-200 ${
                                      openItems.includes(`${section.label}-${item.name}`)
                                        ? "rotate-180"
                                        : ""
                                    }`}
                                  />
                                </Button>
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <motion.ul 
                                  className="ml-4 mt-1 space-y-1"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  transition={{ duration: 0.2 }}
                                >
                                  {item.subItems.map((subItem) => (
                                    <motion.li 
                                      key={subItem.name}
                                      initial={{ opacity: 0, x: -5 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.15 }}
                                      className="group relative"
                                    >
                                      <Link
                                        href={subItem.href}
                                        onClick={() => addToRecent({
                                          ...subItem,
                                          icon: item.icon,
                                          subItemName: subItem.name
                                        }, section.label)}
                                        className={`flex justify-between items-center rounded-md px-3 py-1.5 text-sm text-gray-400 hover:bg-gray-700 hover:text-white transition-colors ${
                                          subItem.disabled ? "opacity-50 cursor-not-allowed" : ""
                                        }`}
                                      >
                                        <span>{subItem.name}</span>
                                        <Star 
                                          className={`h-3.5 w-3.5 cursor-pointer transition-opacity duration-200 ${
                                            isInFavorites(subItem.href) 
                                              ? "text-yellow-500" 
                                              : "text-gray-500 opacity-0 group-hover:opacity-100"
                                          }`}
                                          onClick={(e) => toggleFavorite(e, {
                                            ...subItem,
                                            icon: item.icon,
                                            subItemName: subItem.name
                                          }, section.label)}
                                        />
                                      </Link>
                                    </motion.li>
                                  ))}
                                </motion.ul>
                              </CollapsibleContent>
                            </Collapsible>
                          ) : (
                            <div className="relative group">
                              <Link 
                                href={item.href || '#'}
                                onClick={() => addToRecent(item, section.label)}
                              >
                                <Button
                                  variant="ghost"
                                  className="flex w-full items-center justify-between rounded-md px-3 py-1.5 text-sm text-gray-300 hover:bg-gray-700 transition-colors"
                                >
                                  <span className="flex items-center">
                                    <item.icon className="mr-2 h-4 w-4" />
                                    {item.name}
                                  </span>
                                  <Star 
                                    className={`h-3.5 w-3.5 cursor-pointer transition-opacity duration-200 ${
                                      isInFavorites(item.href || '') 
                                        ? "text-yellow-500" 
                                        : "text-gray-500 opacity-0 group-hover:opacity-100"
                                    }`}
                                    onClick={(e) => toggleFavorite(e, item, section.label)}
                                  />
                                </Button>
                              </Link>
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </AnimatePresence>
        </motion.nav>
      </div>
    </>
  );
} 