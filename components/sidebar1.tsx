"use client";

import {
  FileText,
  <PERSON>riefcase,
  Users,
  PenToolIcon as Wrench,
  Search,
  AlertTriangle,
  Settings,
  TrendingDown,
  GitBranch,
  PlayCircle,
  BarChart,
  LayoutDashboard,
  ShoppingBag,
  FileCheck,
  Bot,
  Car,
  Home,
  ListIcon as List,
} from "lucide-react";
import React, { useState, useEffect, useRef, MouseEvent } from "react";
import { useSession } from "next-auth/react";
import type { Session } from "next-auth";
import type { UserRole } from "@/types/user";
import axios from "axios";
import { HeaderSideBar } from "@/components/sidebar/HeaderSideBar";
import { FooterSideBar } from "@/components/sidebar/FooterSideBar";
import { ContentSideBar } from "@/components/sidebar/ContentSideBar";

const MenuItems = [
  {
    label: "ML Models",
    items: [
      {
        name: "Auto",
        icon: Car,
        subItems: [
          { name: "Claims Segmentation", href: "/dashboard/models/claims-segmentation" },
          { name: "Total Loss", href: "/dashboard/models/total-loss" },
          { name: "Car Fraud Detection", href: "/dashboard/models/custom-fraud-detection" },
        ],
      },
      {
        name: "Property",
        icon: Home,
        subItems: [
          { name: "Property Risk Assessment", href: "/dashboard/models/property-risk" },
          { name: "Natural Disaster Risk", href: "/dashboard/models/disaster-risk" },
        ],
      },
      {
        name: "Underwriting",
        icon: FileCheck,
        subItems: [
          { name: "CLV", href: "/dashboard/models/clv" },
          { name: "Churn", href: "/dashboard/models/churn" },
          { name: "Risk Scoring", href: "/dashboard/models/risk-scoring" },
        ],
      },
      {
        name: "General",
        icon: Briefcase,
        subItems: [
          { name: "Custom Models", href: "/dashboard/models/custom" },
          { name: "Market Analysis", href: "/dashboard/models/market-analysis" },
        ],
      },
    ],
  },
  {
    label: "Tools",
    items: [
      {
        name: "Auto",
        icon: Car,
        subItems: [
          { name: "Car Appraisal", href: "/dashboard/tools/car-appraisal"},
          { name: "Vehicle Specs", href: "/dashboard/tools/vehicle-specs" },
          { name: "Market Evaluation", href: "/dashboard/tools/market-evaluation" },
          { name: "Check Insurer", href: "/dashboard/tools/check-insurer" },
        ],
      },
      {
        name: "Property",
        icon: Home,
        subItems: [
          { name: "Home Appraisal", href: "/dashboard/tools/home-appraisal" },
          { name: "Satellite Imagery", href: "/dashboard/tools/satellite-imagery" },
          { name: "Weather", href: "/dashboard/tools/weather" },
          { name: "Earthquake Prediction", href: "/dashboard/tools/earthquake-prediction" },
        ],
      },
      {
        name: "Document Processing",
        icon: FileText,
        subItems: [
          { name: "OCR", href: "/dashboard/tools/ocr" },
          { name: "FNOL Speech-to-Text", href: "/dashboard/tools/fnol-speech-to-text" },
          { name: "Claim Acceptance", href: "/dashboard/tools/claim-acceptance" },
        ],
      },
      {
        name: "Investigation",
        icon: Search,
        subItems: [
          { name: "Check Debtor", href: "/dashboard/tools/check-debtor" },
          { name: "Image Analyser", href: "/dashboard/tools/image-analyser" },
          { name: "Network Analysis", href: "/dashboard/tools/network-analysis" },
          { name: "OSINT", href: "/dashboard/tools/osint" },
          { name: "Identity Check", href: "/dashboard/tools/identity-check" },
          { name: "Complex Claims Analysis", href: "/dashboard/tools/complex-claims-analysis" },
        ],
      },
      {
        name: "Underwritting",
        icon: Briefcase,
        subItems: [
          { name: "Commercial Due Diligence", href: "/dashboard/tools/commercial-due-diligence" },
        ],
      },
    ],
  },
  {
    label: "AI Workflows",
    items: [
      {
        name: "All Workflows",
        icon: GitBranch,
        href: "/dashboard/workflows",
      },
      {
        name: "My Tasks",
        icon: List,
        href: "/dashboard/workflows/my-tasks",
      },
      {
        name: "Create Workflow",
        icon: PlayCircle,
        href: "/dashboard/workflows/create",
      },
      {
        name: "Tools",
        icon: Wrench,
        href: "/dashboard/workflows/tools",
      },
      {
        name: "AI Agents",
        icon: Bot,
        href: "/dashboard/workflows/agents",
      },
    ],
  },
  {
    label: "AI Assistants",
    items: [
      {
        name: "Fraud Assistant",
        icon: AlertTriangle,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
      {
        name: "Underwritting Assistant",
        icon: FileCheck,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
      {
        name: "Appraiser Assistant",
        icon: BarChart,
        href: "/dashboard/ai-assistants/fraud-assistant",
      },
    ],
  },
  {
    label: "Analytics",
    items: [
      {
        name: "Claims",
        icon: FileText,
        subItems: [
          { name: "Dashboard", href: "/dashboard/claims" },
          { name: "Browse", href: "/dashboard/claims/browse" },
        ],
      },
      {
        name: "Appraisals",
        icon: FileCheck,
        subItems: [{ name: "Dashboard", href: "/dashboard/appraisals" }],
      },
      {
        name: "Policies",
        icon: Briefcase,
        subItems: [
          { name: "Dashboard", href: "/dashboard/policies" },
          { name: "Browse", href: "/dashboard/policies/browse" },
        ],
      },
      {
        name: "Clients",
        icon: Users,
        subItems: [
          { name: "Dashboard", href: "/dashboard/clients" },
          { name: "Browse", href: "/dashboard/clients/browse" },
        ],
      },
    ],
  },
  {
    label: "Project Management",
    items: [
      {
        name: "Fraud",
        icon: AlertTriangle,
        subItems: [
          { name: "Dashboard", href: "/dashboard/fraud" },
          { name: "Browse", href: "/dashboard/fraud/browse" },
          { name: "Settings", href: "/dashboard/fraud/settings" },
        ],
      },
      {
        name: "Leakages",
        icon: TrendingDown,
        subItems: [
          { name: "Dashboard", href: "/dashboard/leakages" },
          { name: "Browse", href: "/dashboard/leakages/browse" },
        ],
      },
      {
        name: "Settings",
        icon: Settings,
        href: "/dashboard/project-management/settings",
      },
    ],
  },
];

const adminSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
  {
    name: "Settings",
    icon: Settings,
    href: "/dashboard/settings",
  },
];

const userSectionItems = [
  {
    name: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
  },
  {
    name: "Marketplace",
    icon: ShoppingBag,
    href: "/dashboard/marketplace",
  },
];

interface SidebarProps {
  session: Session | null;
}

interface SearchResult {
  itemName: string;
  subItemName?: string;
  categoryLabel: string;
  href: string;
  icon: any;
}

interface FavoriteItem {
  name: string;
  href: string;
  icon: any;
  categoryLabel: string;
}

interface RecentItem {
  name: string;
  href: string;
  icon: any;
  timestamp: number;
  categoryLabel: string;
}

export function Sidebar({ session }: SidebarProps) {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const userRole = session?.user?.role as UserRole;
  const [isClient, setIsClient] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchText, setSearchText] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // User data and tokens management (from top-bar.tsx)
  const { data: sessionData, update: updateSession } = useSession();
  const [userImage, setUserImage] = useState(session?.user?.image || "/avatars/01.png");
  const [tokens, setTokens] = useState(96); // Default to 96 as shown in the image
  const user = session?.user;

  const [isScrolling, setIsScrolling] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);

  const [recentItems, setRecentItems] = useState<RecentItem[]>([]);

  const [isFavoritesOpen, setIsFavoritesOpen] = useState(false);
  const [isRecentOpen, setIsRecentOpen] = useState(false);

  useEffect(() => {
    const fetchUserImage = async () => {
      if (!user) return;
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${user.id}`);
        if (!response.ok) throw new Error('Failed to fetch user image');
        const data = await response.json();
        setUserImage(data.profile_pic || "/avatars/01.png");
      } catch (error) {
        console.error('Error fetching user image:', error);
      }
    };

    // Fetch user image on component mount
    fetchUserImage();

    // Handle profile picture updates
    const handleProfilePicUpdated = () => {
      fetchUserImage();
    };

    window.addEventListener('profile-pic-updated', handleProfilePicUpdated);
    return () => {
      window.removeEventListener('profile-pic-updated', handleProfilePicUpdated);
    };
  }, [user]);

  const fetchUserData = async (id: string) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${id}`);
      setTokens(response.data.user_token);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchUserData(user.id);
    }
  }, [user]);

  useEffect(() => {
    setIsLoading(false);
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearchOpen]);

  // Search function to find matches across all categories and items
  useEffect(() => {
    if (!searchText.trim()) {
      setSearchResults([]);
      return;
    }

    const results: SearchResult[] = [];
    const searchTerms = searchText.toLowerCase().split(" ");

    MenuItems.forEach(category => {
      category.items.forEach(item => {
        // Check if item name matches
        const itemNameMatches = searchTerms.every(term =>
          item.name.toLowerCase().includes(term)
        );

        if (item.subItems) {
          // Check subitems for matches
          item.subItems.forEach(subItem => {
            const subItemNameMatches = searchTerms.every(term =>
              subItem.name.toLowerCase().includes(term)
            );

            if (subItemNameMatches || itemNameMatches) {
              results.push({
                itemName: item.name,
                subItemName: subItem.name,
                categoryLabel: category.label,
                href: subItem.href,
                icon: item.icon
              });
            }
          });
        } else if (itemNameMatches && item.href) {
          // Item without subitems matches
          results.push({
            itemName: item.name,
            categoryLabel: category.label,
            href: item.href,
            icon: item.icon
          });
        }
      });
    });

    setSearchResults(results);
  }, [searchText]);

  // Handle scrolling detection for the scrollbar visibility
  const handleScroll = () => {
    setIsScrolling(true);

    // Clear previous timer if exists
    if (scrollTimerRef.current) {
      clearTimeout(scrollTimerRef.current);
    }

    // Set a timer to hide scrollbar after scrolling stops
    scrollTimerRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 1000); // Hide scrollbar after 1 second of inactivity
  };

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
    };
  }, []);

  if (isLoading || !isClient) {
    return <div className="flex h-screen w-64 flex-col bg-gray-800" />;
  }

  const toggleItem = (item: string, sectionLabel: string) => {
    const itemKey = `${sectionLabel}-${item}`;
    setOpenItems((prev) =>
      prev.includes(itemKey) ? prev.filter((i) => i !== itemKey) : [...prev, itemKey]
    );
  };

  const handleCategorySelect = (categoryLabel: string) => {
    if (categoryLabel === "All") {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(categoryLabel === selectedCategory ? null : categoryLabel);
    }
    setIsDropdownOpen(false);
  };

  // Display abbreviated text for long category names
  const getDisplayText = (text: string) => {
    if (text === "Project Management") return "PM";
    return text;
  };

  // Filter menu items based on selected category
  const filteredMenuItems = selectedCategory
    ? MenuItems.filter(section => section.label === selectedCategory)
    : MenuItems;

  // Handle search result selection
  const handleSearchResultClick = (result: SearchResult) => {
    // Set the category to the result's category
    setSelectedCategory(result.categoryLabel);

    // If this result has a parent item, open its collapsible
    if (result.subItemName) {
      toggleItem(result.itemName, result.categoryLabel);
    }

    // Add to recent items
    addToRecent(result, result.categoryLabel);

    // Clear search
    setSearchText("");
    setIsSearchOpen(false);
  };

  // Toggle search open
  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
    if (!isSearchOpen) {
      setSearchText("");
      setSearchResults([]);
    }
  };

  // Add function to handle adding/removing favorites
  const toggleFavorite = (e: MouseEvent, item: any, categoryLabel: string) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if this item is already in favorites
    const existingIndex = favorites.findIndex(
      fav => fav.href === (item.href || (item.subItems ? null : item.href))
    );

    if (existingIndex >= 0) {
      // Remove from favorites
      setFavorites(favorites.filter((_, i) => i !== existingIndex));
    } else {
      // Add to favorites
      const newFavorite: FavoriteItem = {
        name: item.subItemName || item.name,
        href: item.href,
        icon: item.icon,
        categoryLabel
      };
      setFavorites([...favorites, newFavorite]);
    }
  };

  // Helper function to check if an item is in favorites
  const isInFavorites = (href: string): boolean => {
    return favorites.some(fav => fav.href === href);
  };

  // Add a function to track when tools are clicked and update recent items
  const addToRecent = (item: any, categoryLabel: string) => {
    const newRecentItem: RecentItem = {
      name: item.subItemName || item.name,
      href: item.href,
      icon: item.icon,
      timestamp: Date.now(),
      categoryLabel
    };

    // Check if this item is already in recent items
    const existingIndex = recentItems.findIndex(
      recent => recent.href === newRecentItem.href
    );

    // Create a new array with the updated recent items
    let updatedRecentItems: RecentItem[];

    if (existingIndex >= 0) {
      // Item exists, remove it from its current position
      updatedRecentItems = recentItems.filter((_, i) => i !== existingIndex);
    } else {
      // Item doesn't exist, use current list
      updatedRecentItems = [...recentItems];
    }

    // Add the new item at the beginning (most recent)
    updatedRecentItems.unshift(newRecentItem);

    // Keep only the 3 most recent items
    updatedRecentItems = updatedRecentItems.slice(0, 3);

    // Update state
    setRecentItems(updatedRecentItems);
  };

  return (
    <div className="flex h-screen w-64 flex-col bg-gray-800 text-white shadow-lg">
      <HeaderSideBar
        isSearchOpen={isSearchOpen}
        toggleSearch={toggleSearch}
        selectedCategory={selectedCategory}
        isDropdownOpen={isDropdownOpen}
        setIsDropdownOpen={setIsDropdownOpen}
        handleCategorySelect={handleCategorySelect}
        MenuItems={MenuItems}
      />

      <ContentSideBar
        isSearchOpen={isSearchOpen}
        searchText={searchText}
        setSearchText={setSearchText}
        searchResults={searchResults}
        handleSearchResultClick={handleSearchResultClick}
        isScrolling={isScrolling}
        handleScroll={handleScroll}
        isFavoritesOpen={isFavoritesOpen}
        setIsFavoritesOpen={setIsFavoritesOpen}
        isRecentOpen={isRecentOpen}
        setIsRecentOpen={setIsRecentOpen}
        favorites={favorites}
        recentItems={recentItems}
        filteredMenuItems={filteredMenuItems}
        openItems={openItems}
        toggleItem={toggleItem}
        isInFavorites={isInFavorites}
        toggleFavorite={toggleFavorite}
        addToRecent={addToRecent}
      />

      <FooterSideBar
        userRole={userRole}
        tokens={tokens}
        userImage={userImage}
        userName={user?.name ?? undefined}
        userEmail={user?.email ?? undefined}
        addToRecent={addToRecent}
      />
    </div>
  );
}
