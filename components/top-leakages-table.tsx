import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const topLeakages = [
  { id: "LEK-001", type: "Overpayment", cause: "Human Error", amount: 50000, department: "Auto Claims" },
  { id: "LEK-002", type: "Fraud", cause: "Identity Theft", amount: 45000, department: "Life Insurance" },
  { id: "LEK-003", type: "Underpayment", cause: "System Glitch", amount: 40000
, department: "Health Claims" },
  { id: "LEK-004", type: "Process Inefficiency", cause: "Outdated Procedures", amount: 35000, department: "Property Claims" },
  { id: "LEK-005", type: "Overpayment", cause: "Policy Misinterpretation", amount: 30000, department: "Liability Claims" },
]

export default function TopLeakagesTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>ID</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Cause</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Department</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {topLeakages.map((leakage) => (
          <TableRow key={leakage.id}>
            <TableCell className="font-medium">{leakage.id}</TableCell>
            <TableCell>{leakage.type}</TableCell>
            <TableCell>{leakage.cause}</TableCell>
            <TableCell>${leakage.amount.toLocaleString()}</TableCell>
            <TableCell>{leakage.department}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

