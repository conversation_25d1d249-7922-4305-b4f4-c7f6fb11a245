import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle } from 'lucide-react'

export function CheckInsurerResults({ results }: { results: any }) {
  const today = new Date();
  const endDate = new Date(results.endDate);
  const isActive = endDate > today;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Insurance Information
          {isActive ? (
            <CheckCircle className="text-green-500" size={24} />
          ) : (
            <XCircle className="text-red-500" size={24} />
          )}
        </CardTitle>
        <CardDescription>Results based on the provided vehicle details</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Insurance Company</p>
            <p className="text-2xl font-bold">{results.insuranceCompany}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Policy Number</p>
            <p className="text-2xl font-bold">{results.policyNumber}</p>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Policy Start Date</p>
            <p className="text-2xl font-bold">{results.startDate}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Policy End Date</p>
            <p className="text-2xl font-bold">{results.endDate}</p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium">Policy Status</p>
          <p className={`text-2xl font-bold ${isActive ? 'text-green-500' : 'text-red-500'}`}>
            {isActive ? 'Active' : 'Inactive'}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

