import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const demographicsData = [
  { category: "Location", subCategory: "Urban", percentage: 65 },
  { category: "Location", subCategory: "Suburban", percentage: 25 },
  { category: "Location", subCategory: "Rural", percentage: 10 },
  { category: "Education", subCategory: "High School", percentage: 30 },
  { category: "Education", subCategory: "Bachelor's", percentage: 45 },
  { category: "Education", subCategory: "Master's or higher", percentage: 25 },
  { category: "Income", subCategory: "Low", percentage: 20 },
  { category: "Income", subCategory: "Middle", percentage: 50 },
  { category: "Income", subCategory: "High", percentage: 30 },
]

export default function DemographicsBreakdown() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Category</TableHead>
          <TableHead>Sub-category</TableHead>
          <TableHead className="text-right">Percentage</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {demographicsData.map((item, index) => (
          <TableRow key={index}>
            <TableCell>{item.category}</TableCell>
            <TableCell>{item.subCategory}</TableCell>
            <TableCell className="text-right">{item.percentage}%</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

