"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { FileUploader } from "@/components/file-uploader"
import { Loader2, Car, CircleAlert, Wrench, BanknoteIcon, ImageIcon,  ChevronDown, ChevronUp, DollarSign, AlertTriangle, CheckCircle} from 'lucide-react'
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useSession } from 'next-auth/react'
import { log, sendLogToBackend } from '@/lib/logger'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Progress } from "@/components/ui/progress"

interface DamageAssessment {
  description: string
  severity: string
}

interface RepairRecommendation {
  part: string
  repair_or_replace: string
  labor_hours: number
  part_cost: number
}

interface CostBreakdown {
  cost_type: string
  cost_amount: number
}

interface CarValuation {
  pre_accident_value: number;
  post_accident_value: number;
  depreciation_percentage: number;
  total_loss_threshold: number;
  is_total_loss: boolean;
}

interface ApiAppraisalResult {
  damage_assessment: DamageAssessment[]
  repair_recommendations: RepairRecommendation[]
  cost_breakdown: CostBreakdown[]
  detected_parts: string[]
  damage_masks?: string[]
  insurance_risk_score?: number
  fraud_analysis?: any
  user_explanation?: string
  cv_results?: any[]
  specific_damage?: any[]
  car_valuation?: CarValuation;
}

export function CarAppraisalTool({ compact = false }: { compact?: boolean }) {
  const [carDetails, setCarDetails] = useState({
    make: '',
    model: '',
    year: '',
  })
  const [mediaFile, setMediaFile] = useState<File | null>(null)
  const [isAppraising, setIsAppraising] = useState(false)
  const [appraisalResult, setAppraisalResultRaw] = useState<any>(null)
  const [error, setError] = useState<string>("")
  const [showRawJson, setShowRawJson] = useState(false)
  const [formExpanded, setFormExpanded] = useState(true)
  const setAppraisalResult = (data: any) => {
    console.log("Setting appraisal result with:", data);
    setAppraisalResultRaw(data);
  };
  const { data: session } = useSession();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCarDetails({ ...carDetails, [e.target.name]: e.target.value })
  }

  const handleFileUpload = (files: File[]) => {
    setMediaFile(files.length > 0 ? files[0] : null)
  }

  const handleAppraisal = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsAppraising(true);
    setAppraisalResult(null);
    setError("");

    try {
      // Log appraisal initiation
      const initialLogData = {
        action: "car_appraisal_initiated",
        user_id: session?.user?.id,
        level: "info",
        component: "car-appraisal",
        details: {
          make: carDetails.make,
          model: carDetails.model,
          year: carDetails.year,
          mediaCount: mediaFile ? 1 : 0,
          timestamp: new Date().toISOString(),
        },
      };
      log.info(initialLogData);
      await sendLogToBackend(initialLogData);

      // Validate file
      if (!mediaFile) {
        throw new Error("Please upload an image of the vehicle.");
      }

      // Validate that file is an image
      if (!mediaFile.type.startsWith('image/')) {
        throw new Error(`Invalid file type: ${mediaFile.name}. Only image files are accepted.`);
      }

      // Fetch user data by ID
      const userId = session?.user?.id;
      if (!userId) {
        throw new Error("User ID is missing. Please log in.");
      }

      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/users/${userId}`, {
        method: "GET",
        headers: { accept: "application/json" },
      });

      if (!userResponse.ok) {
        throw new Error(`Failed to fetch user data: ${userResponse.status}`);
      }

      const userData = await userResponse.json();
      console.log("User data:", userData);

      // Check if user is admin or has access to the tool
      if (userData.role !== "admin") {
        const { tools } = userData;
        if (!tools.car_appraisal) {
          throw new Error("You do not have access to the car appraisal tool.");
        }
      }

      // Create FormData
      const formData = new FormData();
      formData.append('file', mediaFile);
      formData.append('make', carDetails.make);
      formData.append('model', carDetails.model);
      formData.append('year', carDetails.year.toString());
      formData.append('user_id', session?.user?.id || '');
      formData.append('external_api_id', '67b9a727383420fe8690dfc7');

      // Make the API call
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/external-api-call/car-appraisal/`,
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      console.log("API Response:", data);
      setAppraisalResult(data);

      // Log successful appraisal
      const successLogData = {
        action: "car_appraisal_completed",
        user_id: session?.user?.id,
        level: "info",
        component: "car-appraisal",
        details: {
          make: carDetails.make,
          model: carDetails.model,
          year: carDetails.year,
          mediaCount: mediaFile ? 1 : 0,
          timestamp: new Date().toISOString(),
        },
      };
      log.info(successLogData);
      await sendLogToBackend(successLogData);

      toast({
        title: "Appraisal Complete",
        description: "Your car has been successfully appraised.",
      });

      // When appraisal is successful, collapse the form
      setFormExpanded(false);

    } catch (error) {
      console.error("Error during appraisal:", error);

      // Set error state
      setError(error instanceof Error ? error.message : "Failed to appraise car. Please try again.");

      // Log error
      const errorLogData = {
        action: "car_appraisal_error",
        user_id: session?.user?.id,
        level: "error",
        component: "car-appraisal",
        details: {
          make: carDetails.make,
          model: carDetails.model,
          year: carDetails.year,
          error: error instanceof Error ? error.message : "Unknown error occurred",
          timestamp: new Date().toISOString(),
        },
      };
      log.error(errorLogData);
      await sendLogToBackend(errorLogData);

      toast({
        title: "Appraisal Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsAppraising(false);
    }
  };

  console.log("Current appraisal result state:", appraisalResult);

  const getCarValuation = () => {
    if (!appraisalResult) return null;

    const totalRepairCost = appraisalResult.cost_breakdown?.reduce((sum: number, item: CostBreakdown) => sum + item.cost_amount, 0) || 0;

    // Base value on car make, model and year
    let baseValue = 15000; // Default value
    if (carDetails.make.toLowerCase().includes('toyota')) {
      baseValue = 18000;
      if (carDetails.model.toLowerCase().includes('corolla')) {
        baseValue = 22000;
      }
    }

    // Adjust for year - depreciate 10% per year from current
    const currentYear = new Date().getFullYear();
    const yearDiff = currentYear - parseInt(carDetails.year);
    const yearDepreciation = Math.min(Math.max(0, yearDiff * 0.1), 0.8); // Cap at 80% depreciation

    const preAccidentValue = Math.round(baseValue * (1 - yearDepreciation));
    const postAccidentValue = Math.round(preAccidentValue - totalRepairCost);
    const damageRatio = totalRepairCost / preAccidentValue;

    return {
      pre_accident_value: preAccidentValue,
      post_accident_value: postAccidentValue,
      depreciation_percentage: Math.round(yearDepreciation * 100),
      total_loss_threshold: 0.75, // 75% damage to value ratio is typically considered total loss
      is_total_loss: damageRatio > 0.75
    };
  };

  const renderDamageImage = (imagePath: string, index: number) => {
    // Use mock images provided instead of trying to load from server
    const mockImages = [
      "https://inspektlabs.com/blog/content/images/2022/11/9-1.jpg",
      "https://inspektlabs.com/blog/content/images/2022/11/17.jpg",
      "https://upload.wikimedia.org/wikipedia/commons/a/a4/2019_Toyota_Corolla_Icon_Tech_VVT-i_Hybrid_1.8.jpg"
    ];

    // Cycle through mock images based on index
    const imageUrl = mockImages[index % mockImages.length];

    return (
      <div className="relative border rounded overflow-hidden h-52">
        <img
          src={imageUrl}
          alt="Damage detection"
          className="object-cover w-full h-full"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/placeholder-image.jpg"; // Fallback image
          }}
        />
      </div>
    );
  };

  // Get car valuation data
  const carValuation = appraisalResult ? getCarValuation() : null;

  // Calculate repair to value ratio
  const totalRepairCost = appraisalResult?.cost_breakdown?.reduce((sum: number, item: CostBreakdown) => sum + item.cost_amount, 0) || 0;
  const repairToValueRatio = carValuation ? (totalRepairCost / carValuation.pre_accident_value) * 100 : 0;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Car Appraisal Tool</CardTitle>
        <CardDescription>Analyze vehicle damage, estimate repair costs, and assess insurance implications</CardDescription>
      </CardHeader>
      <CardContent>
        <Collapsible open={formExpanded} onOpenChange={setFormExpanded} className="mb-6">
          <div className="flex items-center justify-between">
            <h3 className={`font-medium ${appraisalResult ? "text-base" : "text-lg"}`}>
              {appraisalResult ? "Car Details" : "Enter Car Details"}
            </h3>

            {appraisalResult && (
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {formExpanded ? (
                    <>
                      <ChevronUp className="h-4 w-4 mr-1" />
                      Hide Form
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-1" />
                      Show Form
                    </>
                  )}
                </Button>
              </CollapsibleTrigger>
            )}
          </div>

          <CollapsibleContent>
            <form onSubmit={handleAppraisal} className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="make">Make</Label>
                  <Input
                    id="make"
                    name="make"
                    value={carDetails.make}
                    onChange={handleInputChange}
                    placeholder="e.g., Toyota"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="model">Model</Label>
                  <Input
                    id="model"
                    name="model"
                    value={carDetails.model}
                    onChange={handleInputChange}
                    placeholder="e.g., Camry"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    name="year"
                    type="number"
                    value={carDetails.year}
                    onChange={handleInputChange}
                    placeholder="e.g., 2020"
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="media">Upload Photos or Video</Label>
                <FileUploader
                  id="media"
                  accept="image/*,video/*"
                  onFilesSelected={handleFileUpload}
                  multiple
                />
              </div>
              <Button type="submit" disabled={isAppraising || !mediaFile}>
                {isAppraising ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Appraising...
                  </>
                ) : (
                  <>
                    <Car className="mr-2 h-4 w-4" />
                    Appraise Car
                  </>
                )}
              </Button>
            </form>
          </CollapsibleContent>
        </Collapsible>

        {appraisalResult && (
          <div className={`${formExpanded ? "mt-4" : "mt-0"}`}>
            {/* If the form is collapsed, show a summary of the car details */}
            {!formExpanded && (
              <div className="mb-4 p-3 bg-gray-50 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Car className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium">
                        {carDetails.make} {carDetails.model} ({carDetails.year})
                      </p>
                      <p className="text-sm text-gray-500">
                        {mediaFile ? '1 file uploaded' : 'No file uploaded'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFormExpanded(true)}
                  >
                    <ChevronDown className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Appraisal Results</h3>
              <Button variant="outline" size="sm" onClick={() => setShowRawJson(!showRawJson)}>
                {showRawJson ? "Hide" : "Show"} Raw JSON
              </Button>
            </div>

            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid grid-cols-5 mb-4">
                <TabsTrigger value="summary" className="flex items-center gap-2">
                  <CircleAlert className="h-4 w-4" />
                  <span>Summary</span>
                </TabsTrigger>
                <TabsTrigger value="valuation" className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  <span>Valuation</span>
                </TabsTrigger>
                <TabsTrigger value="repairs" className="flex items-center gap-2">
                  <Wrench className="h-4 w-4" />
                  <span>Repairs</span>
                </TabsTrigger>
                <TabsTrigger value="costs" className="flex items-center gap-2">
                  <BanknoteIcon className="h-4 w-4" />
                  <span>Costs</span>
                </TabsTrigger>
                <TabsTrigger value="images" className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4" />
                  <span>Images</span>
                </TabsTrigger>
              </TabsList>

              {/* Summary Tab */}
              <TabsContent value="summary" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Insurance Risk Score */}
                  <div className="p-4 border rounded-lg bg-white">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">Insurance Risk Score</h4>
                      <span className="text-xl font-bold">{appraisalResult.insurance_risk_score?.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${appraisalResult.insurance_risk_score || 0}%` }}
                      ></div>
                    </div>
                    {appraisalResult.fraud_analysis && (
                      <div className="mt-2 text-sm text-gray-600">
                        <p>Fraud Likelihood: {(appraisalResult.fraud_analysis.fraud_likelihood * 100).toFixed(1)}%</p>
                        <p>Recommendation: {appraisalResult.fraud_analysis.recommendation}</p>
                      </div>
                    )}
                  </div>

                  {/* Estimated Repair Cost */}
                  <div className="p-4 border rounded-lg bg-white">
                    <div className="flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">Total Repair Cost</h4>
                        <span className="text-xl font-bold">€{totalRepairCost}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Parts: €{appraisalResult.cost_breakdown?.find((c: CostBreakdown) => c.cost_type === "Parts")?.cost_amount || 0}</span>
                        <span>Labor: €{appraisalResult.cost_breakdown?.find((c: CostBreakdown) => c.cost_type === "Labor")?.cost_amount || 0}</span>
                      </div>
                    </div>
                  </div>

                  {/* Total Loss Assessment */}
                  {carValuation && (
                    <div className={`p-4 border rounded-lg ${carValuation.is_total_loss ? 'bg-red-50' : 'bg-green-50'}`}>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex-shrink-0">
                          {carValuation.is_total_loss ? (
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                          ) : (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          )}
                        </div>
                        <h4 className="font-medium">{carValuation.is_total_loss ? 'Potential Total Loss' : 'Repairable'}</h4>
                      </div>
                      <div className="mt-2 text-sm space-y-1">
                        <p>Repair-to-Value Ratio: <span className="font-medium">{repairToValueRatio.toFixed(1)}%</span></p>
                        <p>Threshold: <span className="font-medium">{(carValuation.total_loss_threshold * 100).toFixed(0)}%</span></p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Damage Assessment */}
                <div className="p-4 border rounded-lg bg-white">
                  <h4 className="font-medium mb-3">Damage Assessment</h4>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead>Severity</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {appraisalResult.damage_assessment?.map((damage: DamageAssessment, i: number) => (
                        <TableRow key={i}>
                          <TableCell>{damage.description}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${damage.severity === "high" ? "bg-red-100 text-red-800" :
                              damage.severity === "medium" ? "bg-yellow-100 text-yellow-800" :
                                "bg-green-100 text-green-800"
                              }`}>
                              {damage.severity.charAt(0).toUpperCase() + damage.severity.slice(1)}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              {/* New Valuation Tab */}
              <TabsContent value="valuation" className="space-y-4">
                {carValuation && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border rounded-lg bg-white">
                        <h4 className="font-medium mb-4">Vehicle Valuation Summary</h4>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Pre-Accident Value</span>
                              <span className="text-sm font-bold">€{carValuation.pre_accident_value}</span>
                            </div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Repair Cost</span>
                              <span className="text-sm font-bold text-red-600">-€{totalRepairCost}</span>
                            </div>
                            <div className="flex justify-between pt-2 border-t">
                              <span className="text-sm font-medium">Post-Repair Value</span>
                              <span className="text-sm font-bold">€{carValuation.post_accident_value}</span>
                            </div>
                          </div>

                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Age-Based Depreciation</span>
                              <span className="text-sm">{carValuation.depreciation_percentage}%</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="p-4 border rounded-lg bg-white">
                        <h4 className="font-medium mb-4">Repair vs. Replacement Analysis</h4>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between mb-1 text-sm">
                              <span>Repair-to-Value Ratio</span>
                              <span className="font-medium">{repairToValueRatio.toFixed(1)}%</span>
                            </div>
                            <Progress value={repairToValueRatio} className="h-2" />
                            <div className="mt-1 flex justify-between text-xs text-gray-500">
                              <span>0%</span>
                              <span className="text-amber-500">50%</span>
                              <span className="text-red-500">100%</span>
                            </div>
                          </div>

                          <div className={`mt-4 p-3 rounded-md ${carValuation.is_total_loss ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`}>
                            <div className="flex items-start gap-2">
                              {carValuation.is_total_loss ? (
                                <>
                                  <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                                  <div>
                                    <p className="font-medium text-red-700">Potential Total Loss</p>
                                    <p className="text-sm text-red-600">Repair costs exceed {(carValuation.total_loss_threshold * 100).toFixed(0)}% of vehicle value.</p>
                                    <p className="text-sm text-red-600 mt-1">Recommend settlement at pre-accident value minus salvage.</p>
                                  </div>
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                                  <div>
                                    <p className="font-medium text-green-700">Economically Repairable</p>
                                    <p className="text-sm text-green-600">Repair costs are {repairToValueRatio.toFixed(1)}% of vehicle value, below the {(carValuation.total_loss_threshold * 100).toFixed(0)}% threshold.</p>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg bg-white">
                      <h4 className="font-medium mb-3">Comparable Vehicles</h4>
                      <p className="text-sm text-gray-500 mb-4">Market analysis of similar {carDetails.year} {carDetails.make} {carDetails.model} vehicles</p>

                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Condition</TableHead>
                            <TableHead>Mileage</TableHead>
                            <TableHead>Market Value</TableHead>
                            <TableHead>Source</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>Excellent</TableCell>
                            <TableCell>30,000 mi</TableCell>
                            <TableCell>€{Math.round(carValuation.pre_accident_value * 1.1).toLocaleString()}</TableCell>
                            <TableCell>KBB</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Good</TableCell>
                            <TableCell>45,000 mi</TableCell>
                            <TableCell>€{carValuation.pre_accident_value.toLocaleString()}</TableCell>
                            <TableCell>Edmunds</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Fair</TableCell>
                            <TableCell>60,000 mi</TableCell>
                            <TableCell>€{Math.round(carValuation.pre_accident_value * 0.9).toLocaleString()}</TableCell>
                            <TableCell>NADA</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </>
                )}
              </TabsContent>

              {/* Repairs Tab */}
              <TabsContent value="repairs" className="space-y-4">
                <div className="p-4 border rounded-lg bg-white">
                  <h4 className="font-medium mb-3">Repair Recommendations</h4>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Part</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Labor Hours</TableHead>
                        <TableHead>Part Cost</TableHead>
                        <TableHead>Labor Cost</TableHead>
                        <TableHead>Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {appraisalResult.repair_recommendations?.map((repair: RepairRecommendation, i: number) => {
                        // Assuming labor rate of €50/hr
                        const laborRate = 50;
                        const laborCost = repair.labor_hours * laborRate;
                        const totalCost = laborCost + repair.part_cost;

                        return (
                          <TableRow key={i}>
                            <TableCell className="font-medium">{repair.part}</TableCell>
                            <TableCell>{repair.repair_or_replace.charAt(0).toUpperCase() + repair.repair_or_replace.slice(1)}</TableCell>
                            <TableCell>{repair.labor_hours}</TableCell>
                            <TableCell>€{repair.part_cost}</TableCell>
                            <TableCell>€{laborCost}</TableCell>
                            <TableCell className="font-semibold">€{totalCost}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                {/* Detected Parts */}
                {appraisalResult.detected_parts && appraisalResult.detected_parts.length > 0 && (
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Detected Parts</h4>
                    <div className="flex flex-wrap gap-2">
                      {appraisalResult.detected_parts.map((part: string, i: number) => (
                        <span key={i} className="px-3 py-1 bg-gray-100 rounded-full text-sm">
                          {part}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </TabsContent>

              {/* Costs Tab */}
              <TabsContent value="costs" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Cost Breakdown</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Category</TableHead>
                          <TableHead>Amount</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {appraisalResult.cost_breakdown?.map((cost: CostBreakdown, i: number) => (
                          <TableRow key={i}>
                            <TableCell>{cost.cost_type}</TableCell>
                            <TableCell>€{cost.cost_amount}</TableCell>
                          </TableRow>
                        ))}
                        <TableRow>
                          <TableCell className="font-semibold">Total Estimated Cost</TableCell>
                          <TableCell className="font-semibold">
                            €{appraisalResult.cost_breakdown?.reduce((sum: number, item: CostBreakdown) => sum + item.cost_amount, 0) || 0}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>

                  <div>
                    {appraisalResult.cost_breakdown && appraisalResult.cost_breakdown.length > 0 && (
                      <div className="rounded-lg border p-4 h-full flex flex-col justify-center">
                        <h5 className="text-sm font-medium mb-2">Cost Distribution</h5>
                        <div className="space-y-2">
                          {appraisalResult.cost_breakdown.map((cost: CostBreakdown, i: number) => {
                            const total = appraisalResult.cost_breakdown.reduce((sum: number, item: CostBreakdown) => sum + item.cost_amount, 0);
                            const percentage = (cost.cost_amount / total * 100).toFixed(1);
                            return (
                              <div key={i}>
                                <div className="flex justify-between text-sm mb-1">
                                  <span>{cost.cost_type}</span>
                                  <span>{percentage}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`${i === 0 ? 'bg-blue-600' : 'bg-green-500'} h-2 rounded-full`}
                                    style={{ width: `${percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Images Tab */}
              <TabsContent value="images" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Vehicle Overview */}
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Vehicle Overview</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <p className="text-sm mb-2">Overview of {carDetails.make} {carDetails.model}</p>
                        {renderDamageImage("vehicle-overview", 2)}
                      </div>
                    </div>
                  </div>

                  {/* Damage Detection */}
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Damage Detection</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <p className="text-sm mb-2">AI-detected damage areas</p>
                        {renderDamageImage("damage-detection", 0)}
                      </div>
                    </div>
                  </div>

                  {/* Parts Analysis */}
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Parts Analysis</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <p className="text-sm mb-2">Parts identification</p>
                        {renderDamageImage("parts-analysis", 1)}
                      </div>
                    </div>
                  </div>

                  {/* Compare Before/After */}
                  <div className="p-4 border rounded-lg bg-white">
                    <h4 className="font-medium mb-3">Damage Details</h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <p className="text-sm mb-2">Close-up of damaged area</p>
                        {renderDamageImage("damage-details", 0)}
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Raw JSON Toggle */}
            {showRawJson && (
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-semibold">Raw API Response</h3>
                <pre className="p-4 bg-neutral-50 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(appraisalResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600">
            <p className="font-medium">Error</p>
            <p>{error}</p>
          </div>
        )}

        {isAppraising && (
          <div className="mt-4 flex items-center justify-center">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            <p>Analyzing vehicle damage... This may take 5-15 seconds.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
