import React from 'react';

export const VerticalRuler: React.FC = () => {
  // Array for dots (7 items for all positions)
  const dots = Array.from({ length: 7 }, (_, i) => i);
  // Array for numbers (6 items) to display between dots
  const numbers = Array.from({ length: 6 }, (_, i) => String(i + 1).padStart(2, '0'));

  return (
    <div className="absolute left-4 top-1/2 -translate-y-1/2 h-[95%] flex flex-col justify-between items-center">
      {/* Vertical base line */}
      <div className="absolute h-full w-[1px] bg-gray-600/30 left-1/2 -translate-x-1/2" />
      
      {/* White highlight line between red points */}
      <div 
        className="absolute w-[1px] bg-white/80 left-1/2 -translate-x-1/2"
        style={{ 
          top: `calc(1 * (100% / 6))`, 
          height: `calc(100% / 6)` 
        }}
      />
      
      {/* Dots */}
      {dots.map((index) => (
        <div key={`dot-${index}`} className="relative z-10 flex items-center">
          <div className={`w-2 h-2 rounded-full ${
            index === 1 || index === 2 ? 'bg-[#ff4d6d]' : 'bg-gray-600/80'
          } border border-gray-500/30`} />
        </div>
      ))}
      
      {/* Numbers between dots - positioned exactly between dots */}
      <div className="absolute h-full w-full left-0">
        {numbers.map((num, index) => (
          <div 
            key={`num-${num}`} 
            className="absolute z-10 left-6"
            style={{ 
              top: `calc(${index * (100 / 6)}% + ${100/12}%)`, 
              transform: 'translateY(-50%)'
            }}
          >
            <span className="text-white/40 text-sm font-medium">{num}</span>
          </div>
        ))}
      </div>
    </div>
  );
};