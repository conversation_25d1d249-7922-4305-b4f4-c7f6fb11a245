import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { CustomModelInfoDialog } from "@/components/custom-model-info-dialog"
import { Code2, ArrowUpCircle } from 'lucide-react'

interface CustomModel {
  id: string
  name: string
  type: 'classification' | 'regression'
  pythonVersion: string
  createdAt: string
  version: string
  trainingMetrics: {
    accuracy: number
    f1Score: number
    precision: number
    recall: number
  }
  realPerformance: {
    accuracy: number
  }
}

interface CustomModelCardProps {
  model: CustomModel
}

export function CustomModelCard({ model }: CustomModelCardProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{model.name}</CardTitle>
            <CardDescription>Version {model.version}</CardDescription>
          </div>
          <Badge variant={model.type === 'classification' ? 'default' : 'secondary'}>
            {model.type}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Training Accuracy</span>
            <span className="font-medium">{(model.trainingMetrics.accuracy * 100).toFixed(2)}%</span>
          </div>
          <Progress value={model.trainingMetrics.accuracy * 100} className="h-2" />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-muted-foreground">F1 Score</span>
            <p className="font-medium">{model.trainingMetrics.f1Score.toFixed(2)}</p>
          </div>
          <div>
            <span className="text-sm text-muted-foreground">Precision</span>
            <p className="font-medium">{model.trainingMetrics.precision.toFixed(2)}</p>
          </div>
          <div>
            <span className="text-sm text-muted-foreground">Recall</span>
            <p className="font-medium">{model.trainingMetrics.recall.toFixed(2)}</p>
          </div>
          <div>
            <span className="text-sm text-muted-foreground">Real Performance</span>
            <p className="font-medium">{(model.realPerformance.accuracy * 100).toFixed(2)}%</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Created: {model.createdAt}
        </div>
        <div className="space-x-2">
          <CustomModelInfoDialog model={model} />
          <Button variant="outline" size="sm">
            <ArrowUpCircle className="mr-2 h-4 w-4" />
            Update
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

