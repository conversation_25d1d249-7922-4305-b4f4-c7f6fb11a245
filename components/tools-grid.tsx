"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Search, Globe, Database, HardDrive, FileText, Code, Mail, Calculator, Camera, Mic, MapPin, BarChartIcon as ChartBar, Lock, Zap, Settings, Car, LucideIcon } from 'lucide-react'
import { ToolConfigDialog } from "./tool-config-dialog"

// Define the ConfigOption type with specific allowed types
type ConfigOption = 
  | { type: "input"; label: string; inputType?: string }
  | { type: "select"; label: string; options: string[] };

// Update the tools array to use the correct ConfigOption type
const tools: { name: string; description?: string; icon: LucideIcon; configOptions: ConfigOption[] }[] = [
  { 
    name: "Web Access", 
    description: "Access and retrieve information from the internet", 
    icon: Globe,
    configOptions: [
      { type: "select", label: "Search Engine", options: ["Google", "Bing", "DuckDuckGo"] },
      { type: "input", label: "API Key" }
    ]
  },
  { 
    name: "SQL Connection", 
    description: "Connect to and query SQL databases", 
    icon: Database,
    configOptions: [
      { type: "input", label: "Host" },
      { type: "input", label: "Port" },
      { type: "input", label: "Username" },
      { type: "input", label: "Password", inputType: "password" },
      { type: "input", label: "Database Name" }
    ]
  },
  { 
    name: "S3 Connection", 
    description: "Access and manage files in Amazon S3 buckets", 
    icon: HardDrive,
    configOptions: [
      { type: "input", label: "Access Key ID" },
      { type: "input", label: "Secret Access Key", inputType: "password" },
      { type: "input", label: "Region" },
      { type: "input", label: "Bucket Name" }
    ]
  },
  { name: "Document Parser", description: "Extract information from various document formats", icon: FileText, configOptions: [] },
  { name: "Code Executor", description: "Execute code snippets in multiple programming languages", icon: Code, configOptions: [] },
  { name: "Email Sender", description: "Send emails programmatically", icon: Mail, configOptions: [] },
  { name: "Calculator", description: "Perform complex mathematical calculations", icon: Calculator, configOptions: [] },
  { name: "Image Analysis", description: "Analyze and extract information from images", icon: Camera, configOptions: [] },
  { name: "Speech Recognition", description: "Convert spoken words to text", icon: Mic, configOptions: [] },
  { name: "Geolocation", description: "Get location information and perform geospatial analysis", icon: MapPin, configOptions: [] },
  { name: "Data Visualization", description: "Create charts and graphs from data", icon: ChartBar, configOptions: [] },
  { name: "Encryption", description: "Encrypt and decrypt sensitive information", icon: Lock, configOptions: [] },
  { name: "API Connector", description: "Connect to various third-party APIs", icon: Zap, configOptions: [] },
  {
    name: "Car Appraisal",
    description: "Appraise a car's value based on photos or video",
    icon: Car,
    configOptions: []
  },
]

export function ToolsGrid() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTool, setSelectedTool] = useState<typeof tools[0] | null>(null)

  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tool.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Available Tools</h2>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredTools.map((tool, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <tool.icon className="h-5 w-5" />
                  {tool.name}
                </CardTitle>
                <Badge variant="secondary">AI Tool</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription>{tool.description}</CardDescription>
              <div className="mt-4 flex justify-between">
                <Button variant="outline">Use Tool</Button>
                <Button variant="outline" onClick={() => setSelectedTool(tool)}>
                  <Settings className="mr-2 h-4 w-4" />
                  Configure
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedTool && (
        <ToolConfigDialog
          tool={selectedTool}
          open={!!selectedTool}
          onOpenChange={() => setSelectedTool(null)}
        />
      )}
    </div>
  )
}

