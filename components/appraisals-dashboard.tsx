"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "@/components/ui/badge"
import { Search, FileText, AlertCircle } from 'lucide-react'
import { format } from "date-fns"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Label } from "@/components/ui/label"
import Link from 'next/link'

interface Appraisal {
  id: string
  claimId: string
  claimNumber: string
  appraiser: string
  date: Date
  estimatedValue: number
  status: "Pending" | "Completed" | "Disputed"
}

const appraisals: Appraisal[] = [
  {
    id: "APR-2023-001",
    claimId: "1",
    claimNumber: "CLM-2023-001",
    appraiser: "<PERSON>",
    date: new Date("2023-06-15"),
    estimatedValue: 5000,
    status: "Completed"
  },
  {
    id: "APR-2023-002",
    claimId: "2",
    claimNumber: "CLM-2023-002",
    appraiser: "Jane Doe",
    date: new Date("2023-06-18"),
    estimatedValue: 7500,
    status: "Pending"
  },
  {
    id: "APR-2023-003",
    claimId: "3",
    claimNumber: "CLM-2023-003",
    appraiser: "Bob Johnson",
    date: new Date("2023-06-20"),
    estimatedValue: 3000,
    status: "Disputed"
  },
  {
    id: "APR-2023-004",
    claimId: "4",
    claimNumber: "CLM-2023-004",
    appraiser: "Alice Brown",
    date: new Date("2023-06-22"),
    estimatedValue: 10000,
    status: "Completed"
  },
  {
    id: "APR-2023-005",
    claimId: "5",
    claimNumber: "CLM-2023-005",
    appraiser: "Charlie Davis",
    date: new Date("2023-06-25"),
    estimatedValue: 6000,
    status: "Pending"
  }
]

const columns: ColumnDef<Appraisal>[] = [
  {
    accessorKey: "id",
    header: "Appraisal ID",
    cell: ({ row }) => {
      const appraisal = row.original
      return (
        <Link href={`/appraisals/${appraisal.id}`} className="text-blue-500 hover:underline">
          {appraisal.id}
        </Link>
      )
    },
  },
  {
    accessorKey: "claimNumber",
    header: "Claim Number",
    cell: ({ row }) => {
      const claim = row.original
      return (
        <Link href={`/claims/${claim.claimId}`} className="text-blue-500 hover:underline">
          {claim.claimNumber}
        </Link>
      )
    },
  },
  {
    accessorKey: "appraiser",
    header: "Appraiser",
  },
  {
    accessorKey: "date",
    header: "Date",
    cell: ({ row }) => {
      return format(row.getValue("date"), "PP")
    },
  },
  {
    accessorKey: "estimatedValue",
    header: "Estimated Value",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("estimatedValue"))
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount)
      return <div className="font-medium">{formatted}</div>
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <Badge variant={status === "Completed" ? "default" : status === "Pending" ? "secondary" : "destructive"}>
          {status}
        </Badge>
      )
    },
  },
]

export function AppraisalsDashboard() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>()
  const [filteredAppraisals, setFilteredAppraisals] = useState(appraisals)

  const handleSearch = () => {
    const filtered = appraisals.filter((appraisal) => {
      const matchesSearch =
        appraisal.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        appraisal.claimNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        appraisal.appraiser.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = !selectedStatus || appraisal.status === selectedStatus
      return matchesSearch && matchesStatus
    })
    setFilteredAppraisals(filtered)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Appraisals</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search appraisals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Disputed">Disputed</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleSearch} className="w-full sm:w-auto">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
          </div>
        </div>
        {filteredAppraisals.length > 0 ? (
          <div className="mt-6">
            <DataTable columns={columns} data={filteredAppraisals} />
          </div>
        ) : (
          <Alert className="mt-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No results</AlertTitle>
            <AlertDescription>
              No appraisals match your search criteria. Try adjusting your filters.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}

