import React from 'react';
import { motion } from 'framer-motion';

interface Stage {
  date: string;
  period: string;
  title: string;
  description: string;
  isCurrent?: boolean;
  isCompleted?: boolean;
  isAnimated?: boolean;
}

interface RoadmapStageProps {
  stage: Stage;
}

export const RoadmapStage: React.FC<RoadmapStageProps> = ({ stage }) => {
  return (
    <div className="flex flex-col items-center w-72">
      {/* Period text above the dot */}
      <div className="mb-4">
        <span 
          className={`text-lg font-medium ${
            stage.isCurrent 
              ? 'text-[#ff4d6d]' 
              : stage.isCompleted 
                ? 'text-white' 
                : 'text-white/60'
          }`}
        >
          {stage.period}
        </span>
      </div>

      {/* Dot indicator with animation - centered on timeline */}
      <div className="relative flex items-center justify-center h-3">
        {/* Larger solid background circle that completely blocks the line - match exact background color */}
        <div className="absolute w-8 h-8 rounded-full bg-[#111827] z-40"></div>
        
        {stage.isAnimated ? (
          <motion.div
            className="w-4 h-4 rounded-full bg-gray-600 absolute z-50"
            animate={{
              backgroundColor: ['#1e2738', '#ff4d6d'],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 2.5,
              ease: "easeInOut",
              times: [0, 0.8, 1]
            }}
          />
        ) : (
          <div
            className={`w-4 h-4 rounded-full absolute z-50 ${
              stage.isCurrent
                ? 'bg-[#ff4d6d] shadow-[0_0_15px_rgba(255,77,109,0.5)]'
                : stage.isCompleted
                  ? 'bg-[#ff4d6d]'
                  : 'bg-gray-600'
            }`}
            style={{ 
              backgroundColor: stage.isCurrent || stage.isCompleted ? '#ff4d6d' : '#4b5563' 
            }}
          />
        )}
        
        {stage.isCurrent && (
          <div className="absolute -inset-1.5 rounded-full border-2 border-[#ff4d6d]/30 animate-ping z-45" />
        )}
      </div>

      {/* Content below the dot */}
      <div className="mt-7 text-center">
        <p className={`text-base mb-3 ${stage.isCompleted || stage.isCurrent ? 'text-white' : 'text-white/60'}`}>
          {stage.date}
        </p>
        <p className="text-white/60 text-xs leading-relaxed max-w-[250px]">
          {stage.description}
        </p>
      </div>
    </div>
  );
};