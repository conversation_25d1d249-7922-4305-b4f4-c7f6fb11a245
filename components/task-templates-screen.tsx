"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TaskTemplateCard } from "@/components/task-template-card"
import { Search } from 'lucide-react'

// Mock data for task templates
const taskTemplates = [
{
  id: "TASK-001",
  name: "Document Analysis",
  description: "Analyze and extract information from submitted documents using OCR and ML",
  category: "Document Processing",
  type: "automated",
  estimatedTime: "2-3 minutes",
  inputRequired: ["PDF documents", "Image files"],
  outputProvided: ["Extracted text", "Key-value pairs", "Confidence scores"],
  accuracy: 95,
  usageCount: 1250,
  aiModels: ["OCR Model", "Text Classification", "Named Entity Recognition"],
  integrations: ["Document Storage", "Claims Database"]
},
{
  id: "TASK-002",
  name: "Image Inspection",
  description: "Analyze images for damage assessment and fraud detection",
  category: "Visual Analysis",
  type: "automated",
  estimatedTime: "1-2 minutes",
  inputRequired: ["Image files", "Reference images"],
  outputProvided: ["Damage assessment", "Fraud probability", "Detailed report"],
  accuracy: 92,
  usageCount: 850,
  aiModels: ["Computer Vision", "Damage Detection", "Image Comparison"],
  integrations: ["Image Database", "Fraud Detection System"]
},
{
  id: "TASK-003",
  name: "Cost Estimation",
  description: "Calculate repair or replacement costs based on damage assessment",
  category: "Financial",
  type: "hybrid",
  estimatedTime: "3-4 minutes",
  inputRequired: ["Damage report", "Market prices", "Labor costs"],
  outputProvided: ["Cost breakdown", "Total estimate", "Confidence range"],
  accuracy: 88,
  usageCount: 720,
  aiModels: ["Price Prediction", "Cost Optimization"],
  integrations: ["Market Price API", "Vendor Database"]
},
{
  id: "TASK-004",
  name: "Risk Assessment",
  description: "Evaluate risk factors and calculate risk scores",
  category: "Risk Management",
  type: "automated",
  estimatedTime: "2-3 minutes",
  inputRequired: ["Client data", "Historical claims", "Market data"],
  outputProvided: ["Risk score", "Risk factors", "Recommendations"],
  accuracy: 91,
  usageCount: 950,
  aiModels: ["Risk Prediction", "Anomaly Detection"],
  integrations: ["Claims History", "Risk Database"]
},
{
  id: "TASK-005",
  name: "Network Analysis",
  description: "Analyze relationships between entities for fraud detection",
  category: "Fraud Detection",
  type: "automated",
  estimatedTime: "4-5 minutes",
  inputRequired: ["Entity data", "Transaction history", "Relationship data"],
  outputProvided: ["Network graph", "Suspicious patterns", "Risk indicators"],
  accuracy: 94,
  usageCount: 480,
  aiModels: ["Graph Analysis", "Pattern Recognition"],
  integrations: ["Entity Database", "Transaction System"]
},
{
  id: "TASK-006",
  name: "Policy Verification",
  description: "Verify policy coverage and conditions",
  category: "Policy Management",
  type: "automated",
  estimatedTime: "1-2 minutes",
  inputRequired: ["Policy details", "Claim information"],
  outputProvided: ["Coverage verification", "Exclusions list", "Recommendations"],
  accuracy: 97,
  usageCount: 1100,
  aiModels: ["Policy Analysis", "Coverage Matching"],
  integrations: ["Policy Database", "Rules Engine"]
},
{
  id: "TASK-007",
  name: "Market Value Assessment",
  description: "Determine current market value of assets",
  category: "Financial",
  type: "hybrid",
  estimatedTime: "3-4 minutes",
  inputRequired: ["Asset details", "Market data", "Condition report"],
  outputProvided: ["Market value", "Comparable sales", "Value range"],
  accuracy: 89,
  usageCount: 630,
  aiModels: ["Price Prediction", "Market Analysis"],
  integrations: ["Market Data API", "Asset Database"]
},
{
  id: "TASK-008",
  name: "Custom Fraud Detection",
  description: "A custom task for fraud detection",
  category: "Fraud Detection",
  type: "hybrid",
  estimatedTime: "5-10 minutes",
  inputRequired: ["Custom Data"],
  outputProvided: ["Fraud Detection Result"],
  accuracy: 90,
  usageCount: 10,
  aiModels: ["Custom Model"],
  integrations: ["Custom System"]
}
]

interface TaskTemplate {
id: string;
name: string;
description: string;
category: string;
type: string;
estimatedTime: string;
inputRequired: string[];
outputProvided: string[];
accuracy: number;
usageCount: number;
aiModels: string[];
integrations: string[];
}

const categories = ["All", "Document Processing", "Visual Analysis", "Financial", "Risk Management", "Fraud Detection", "Policy Management"]
const types = ["All", "automated", "hybrid", "manual"]

export function TaskTemplatesScreen() {
const [searchQuery, setSearchQuery] = useState("")
const [selectedCategory, setSelectedCategory] = useState("All")
const [selectedType, setSelectedType] = useState("All")
const [selectedTask, setSelectedTask] = useState<TaskTemplate | null>(null);
const [tasks, setTasks] = useState(taskTemplates)

const handleEditTask = (editedTask: TaskTemplate) => {
  setTasks(prevTasks => prevTasks.map(task => 
    task.id === editedTask.id ? editedTask : task
  ))
}

const filteredTasks = tasks.filter(task => {
  const matchesSearch = task.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                       task.description.toLowerCase().includes(searchQuery.toLowerCase())
  const matchesCategory = selectedCategory === "All" || task.category === selectedCategory
  const matchesType = selectedType === "All" || task.type === selectedType

  return matchesSearch && matchesCategory && matchesType
})

return (
  <div className="space-y-6">
    <Card>
      <CardHeader>
        <CardTitle>Available Task Templates</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger>
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              {types.map((type) => (
                <SelectItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTasks.length === 0 ? (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              No task templates found matching your filters.
            </div>
          ) : (
            filteredTasks.map((task) => (
              <TaskTemplateCard
                key={task.id}
                task={task}
                onClick={() => setSelectedTask(task)}
                onEdit={handleEditTask}
              />
            ))
          )}
        </div>
      </CardContent>
    </Card>
  </div>
)
}

