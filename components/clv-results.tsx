import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface CLVResults {
  predictedCLV: number;
  churnProbability: number;
  upsellProbability: number;
  customerSegment: string;
  explanation: string;
}

interface CLVResultsProps {
  results: CLVResults;
}

export function CLVResults({ results }: CLVResultsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer Lifetime Value Prediction Results</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <span className="text-sm font-medium">Predicted CLV</span>
            <p className="text-2xl font-bold">${results.predictedCLV.toLocaleString()}</p>
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Churn Probability</span>
              <span className="text-sm font-medium">{(results.churnProbability * 100).toFixed(2)}%</span>
            </div>
            <Progress value={results.churnProbability * 100} className="h-2" />
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Upsell Probability</span>
              <span className="text-sm font-medium">{(results.upsellProbability * 100).toFixed(2)}%</span>
            </div>
            <Progress value={results.upsellProbability * 100} className="h-2" />
          </div>
          <div>
            <span className="text-sm font-medium">Customer Segment</span>
            <p className="text-lg font-semibold">{results.customerSegment}</p>
          </div>
          <div>
            <span className="text-sm font-medium">Explanation</span>
            <p className="mt-1 text-sm">{results.explanation}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

