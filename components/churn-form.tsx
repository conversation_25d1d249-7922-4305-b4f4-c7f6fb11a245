"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

export function ChurnForm({ onSubmit }: { onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    customerId: '',
    tenure: '',
    contractType: '',
    totalSpend: '',
    lastInteraction: '',
    serviceCallsLastMonth: '',
    paymentDelays: '',
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customerId">Customer ID</Label>
          <Input
            id="customerId"
            name="customerId"
            value={formData.customerId}
            onChange={handleChange}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="tenure">Tenure (months)</Label>
          <Input
            id="tenure"
            name="tenure"
            type="number"
            value={formData.tenure}
            onChange={handleChange}
            required
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="contractType">Contract Type</Label>
        <Select onValueChange={(value) => handleSelectChange('contractType', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select a contract type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="month-to-month">Month-to-month</SelectItem>
            <SelectItem value="one-year">One Year</SelectItem>
            <SelectItem value="two-year">Two Year</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="totalSpend">Total Spend ($)</Label>
          <Input
            id="totalSpend"
            name="totalSpend"
            type="number"
            value={formData.totalSpend}
            onChange={handleChange}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastInteraction">Last Interaction Date</Label>
          <Input
            id="lastInteraction"
            name="lastInteraction"
            type="date"
            value={formData.lastInteraction}
            onChange={handleChange}
            required
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="serviceCallsLastMonth">Service Calls (Last Month)</Label>
          <Input
            id="serviceCallsLastMonth"
            name="serviceCallsLastMonth"
            type="number"
            value={formData.serviceCallsLastMonth}
            onChange={handleChange}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="paymentDelays">Payment Delays</Label>
          <Input
            id="paymentDelays"
            name="paymentDelays"
            type="number"
            value={formData.paymentDelays}
            onChange={handleChange}
            required
          />
        </div>
      </div>
      <Button type="submit" className="w-full">Predict Churn</Button>
    </form>
  )
}

