"use client"

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Send, ChevronDown, ChevronUp } from 'lucide-react'
import { ChatHistorySidebar } from './chat-history-sidebar'
import axios from 'axios'
import ReactMarkdown from 'react-markdown'

interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
}

interface ChatSession {
  id: string
  title: string
  preview: string
  timestamp: string
  messages: Message[]
}

// Force these values for now
const API_BASE_URL = 'https://assistant-waxy.onrender.com'
const TENANT_ID = 'demo'
const USER_ID = 'fcosta'
const FIXED_SESSION_ID = 'demo-fcosta-95039994-e495-4b79-b2b2-96a9e56a963b'

// Simplify mock sessions to just one session with the fixed ID
const mockSessions: ChatSession[] = [
  {
    id: FIXED_SESSION_ID,
    title: 'New Investigation',
    preview: 'Start a new investigation',
    timestamp: 'Just now',
    messages: [] // Empty messages array
  }
]

export function FraudAssistantChat() {
  const [sessions, setSessions] = useState<ChatSession[]>(mockSessions)
  const [activeSessionId, setActiveSessionId] = useState(FIXED_SESSION_ID)
  const [sessionIdInput, setSessionIdInput] = useState(FIXED_SESSION_ID)
  const [input, setInput] = useState('')
  const [isResponding, setIsResponding] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [isSessionInputVisible, setIsSessionInputVisible] = useState(false)

  const activeSession = sessions.find(s => s.id === FIXED_SESSION_ID)
  const messages = activeSession?.messages || []

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSend = async () => {
    if (input.trim()) {
      const userMessage: Message = { 
        role: 'user', 
        content: input, 
        timestamp: new Date().toLocaleTimeString() 
      }
      
      setSessions(prev => prev.map(session => ({
        ...session,
        messages: [...session.messages, userMessage]
      })))
      
      setInput('')
      setIsResponding(true)

      try {
        const response = await axios.post(`${API_BASE_URL}/stream_response`, {
          prompt: input,
          tenant_id: TENANT_ID,
          user_id: USER_ID,
          session_id: activeSessionId
        })

        if (response.data) {
          const assistantMessage: Message = {
            role: 'assistant',
            content: response.data,
            timestamp: new Date().toLocaleTimeString()
          }

          setSessions(prev => prev.map(session => ({
            ...session,
            messages: [...session.messages, assistantMessage]
          })))
        } else {
          throw new Error('Empty response from server')
        }

      } catch (error) {
        console.error('Error:', error)
        const errorMessage: Message = {
          role: 'assistant',
          content: 'Sorry, I encountered an error processing your request.',
          timestamp: new Date().toLocaleTimeString()
        }
        setSessions(prev => prev.map(session => ({
          ...session,
          messages: [...session.messages, errorMessage]
        })))
      } finally {
        setIsResponding(false)
        scrollToBottom()
      }
    }
  }

  return (
    <main className="flex h-full bg-background">
      <ChatHistorySidebar 
        sessions={sessions}
        activeSessionId={activeSessionId}
        onSessionSelect={() => {}}
        onNewChat={() => {}}
        isCreatingNewChat={false}
      />
      <div className="flex-1 flex flex-col">
        <div className="px-6 py-5 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/50">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold tracking-tight mb-0.5">Fraud Assistant</h1>
              <p className="text-sm text-muted-foreground">Chat with AI to investigate potential fraud</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSessionInputVisible(!isSessionInputVisible)}
              className="ml-2"
            >
              {isSessionInputVisible ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
              <span className="ml-2 text-sm">Session Settings</span>
            </Button>
          </div>

          <div className={`overflow-hidden transition-all duration-200 ease-in-out ${
            isSessionInputVisible 
              ? 'max-h-24 opacity-100 mt-3' 
              : 'max-h-0 opacity-0'
          }`}>
            <div className="flex gap-3 items-center">
              <Input
                placeholder="Enter session ID"
                value={sessionIdInput}
                onChange={(e) => setSessionIdInput(e.target.value)}
                className="max-w-md text-sm"
              />
              <Button
                onClick={() => {
                  setActiveSessionId(sessionIdInput)
                  setSessions(prev => prev.map(session => ({
                    ...session,
                    messages: []
                  })))
                  setIsSessionInputVisible(false)
                }}
                variant="secondary"
                size="sm"
                disabled={!sessionIdInput.trim() || sessionIdInput === activeSessionId}
              >
                Update Session
              </Button>
            </div>
            
            <p className="text-xs text-muted-foreground mt-2">
              Active Session: {activeSessionId}
            </p>
          </div>
        </div>
        
        <div className="flex-1 overflow-auto bg-gradient-to-b from-muted/20 to-transparent">
          <ScrollArea className="h-full">
            <div className="px-4 sm:px-6 py-6 space-y-6">
              {messages.map((message, index) => (
                <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`flex items-start group max-w-[85%] w-fit ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                    <Avatar className={`flex-shrink-0 w-8 h-8 transition-opacity ${
                      messages[index - 1]?.role === message.role ? 'opacity-0 group-hover:opacity-100' : ''
                    }`}>
                      <AvatarImage src={message.role === 'user' ? "/avatars/user.png" : "/avatars/ai-assistant.png"} />
                      <AvatarFallback>{message.role === 'user' ? 'U' : 'AI'}</AvatarFallback>
                    </Avatar>
                    
                    <div 
                      className={`rounded-2xl px-4 py-2.5 shadow-sm transition-all
                        ${message.role === 'user' 
                          ? 'bg-primary text-primary-foreground ml-3' 
                          : 'bg-card border border-border/40 hover:border-border/70 mr-3'
                        }
                        ${messages[index - 1]?.role === message.role ? 'mt-1' : 'mt-2'}
                      `}
                      style={{ wordBreak: 'break-word', minWidth: '50px', maxWidth: '100%' }}
                    >
                      {message.role === 'assistant' ? (
                        <ReactMarkdown
                          className="prose prose-sm dark:prose-invert max-w-none prose-p:leading-relaxed prose-pre:my-2"
                          components={{
                            pre: ({ children, ...props }) => (
                              <pre className="overflow-auto rounded-lg bg-muted/50 p-3 text-sm" {...props}>
                                {children}
                              </pre>
                            ),
                            code: ({ children, className, ...props }) => {
                              const match = /language-(\w+)/.exec(className || '');
                              const isInline = !className;
                              return (
                                <code 
                                  className={`${isInline ? 'bg-muted/50 rounded px-1 py-0.5 text-sm' : ''}`} 
                                  {...props}
                                >
                                  {children}
                                </code>
                              );
                            },
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                      ) : (
                        message.content
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </div>

        <div className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/50">
          <div className="max-w-3xl mx-auto p-4">
            <div className="flex items-center gap-3 bg-card rounded-xl border shadow-sm p-1.5">
              <Input
                placeholder={isResponding ? "AI is responding..." : "Type your message here..."}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !isResponding && handleSend()}
                disabled={isResponding || !activeSessionId}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent"
              />
              <Button 
                onClick={handleSend}
                disabled={isResponding || !input.trim() || !activeSessionId}
                size="icon"
                variant="default"
                className="shrink-0 hover:bg-primary hover:text-primary-foreground transition-colors rounded-lg"
              >
                {isResponding ? (
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

