import pino from 'pino'

const logger = pino({
  browser: {
    asObject: true
  },
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  base: {
    env: process.env.NODE_ENV,
  },
})

export const log = logger.child({ component: 'client' })

// Utility function to send logs to backend
export async function sendLogToBackend(logData: any) {
  const endpoint = `${process.env.NEXT_PUBLIC_SERVER_HOST}/api/v1/logs`;
  
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(logData),
    })
    
    if (!response.ok) {
      const errorText = await response.text();
  
      throw new Error(`Failed to send log: ${response.status} - ${errorText}`);
    }
    
    const responseData = await response.json();
 
    return responseData;
  } catch (error) {
 
    throw error;
  }
}