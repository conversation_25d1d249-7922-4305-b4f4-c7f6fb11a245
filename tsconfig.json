{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "target": "ES2015", "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "types/**/*.ts", "types/**/*.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}